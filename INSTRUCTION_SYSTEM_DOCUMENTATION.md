# Instruction System Documentation

## Overview

The Instruction System is a comprehensive role-based notification system that allows authorized users to send instructions to specific user roles. The system includes popup notifications, read tracking, and a dedicated interface for managing instructions.

## Features

### Phase 1: CRUD for Instruction Permissions
- **Role-based permissions**: Define which user roles can send instructions to other roles
- **Filament admin interface**: Easy management of instruction permissions
- **Bulk operations**: Create multiple permissions at once
- **Permission validation**: Prevent unauthorized instruction creation

### Phase 2: Sending and Receiving Instructions
- **Instruction creation**: Authorized users can create instructions with priority levels
- **Popup notifications**: Real-time popup notifications for unread instructions
- **Read tracking**: Track which users have read which instructions
- **Instruction history**: Dedicated page for viewing all read instructions
- **Priority system**: Low, Medium, High, and Urgent priority levels
- **Expiration dates**: Optional expiration for time-sensitive instructions

## Database Schema

### Tables Created

#### `instruction_permissions`
- `id` - Primary key
- `from_role` - Role that can send instructions
- `to_role` - Role that receives instructions
- `is_active` - Whether the permission is active
- `created_at`, `updated_at` - Timestamps

#### `instructions`
- `id` - Primary key
- `title` - Instruction title
- `content` - Instruction content (text)
- `target_role` - Role that should receive this instruction
- `created_by` - Foreign key to users table (creator)
- `is_active` - Whether the instruction is active
- `priority` - Priority level (low, medium, high, urgent)
- `expires_at` - Optional expiration date
- `created_at`, `updated_at` - Timestamps

#### `instruction_reads`
- `id` - Primary key
- `instruction_id` - Foreign key to instructions table
- `user_id` - Foreign key to users table
- `read_at` - When the instruction was read
- `created_at`, `updated_at` - Timestamps

## Models

### InstructionPermission
**Location**: `app/Models/InstructionPermission.php`

**Key Methods**:
- `getAllowedRolesFor($role)` - Get roles a user can send instructions to
- `getRolesThatCanSendTo($role)` - Get roles that can send to a specific role
- `canSendInstructions($fromRole, $toRole)` - Check if role can send to another role
- `getAllRoles()` - Get all available roles in the system

### Instruction
**Location**: `app/Models/Instruction.php`

**Key Methods**:
- `creator()` - Relationship to user who created the instruction
- `reads()` - Relationship to instruction reads
- `targetUsers()` - Get users with the target role
- `hasBeenReadBy($user)` - Check if user has read the instruction
- `markAsReadBy($user)` - Mark instruction as read by user
- `getUnreadForRole($role, $user)` - Get unread instructions for a role
- `getReadForUser($user)` - Get read instructions for a user
- `canUserCreateForRole($user, $targetRole)` - Check creation permissions
- `getAllowedTargetRoles($user)` - Get roles user can send to

### InstructionRead
**Location**: `app/Models/InstructionRead.php`

**Relationships**:
- `instruction()` - Belongs to instruction
- `user()` - Belongs to user

## Filament Resources

### InstructionPermissionResource
**Location**: `app/Filament/Resources/InstructionPermissionResource.php`

**Features**:
- Create, read, update, delete instruction permissions
- Bulk operations for managing multiple permissions
- Role-based filtering and search
- Validation to prevent duplicate permissions

### InstructionResource
**Location**: `app/Filament/Resources/InstructionResource.php`

**Features**:
- Create, read, update, delete instructions
- Priority-based filtering and sorting
- Read count tracking
- Role-based access control (users can only see instructions they created or can send to)
- Bulk status toggle operations

## Livewire Components

### InstructionNotifications
**Location**: `app/Http/Livewire/InstructionNotifications.php`
**View**: `resources/views/livewire/instruction-notifications.blade.php`

**Features**:
- Real-time popup notifications for unread instructions
- Priority-based display order
- Auto-refresh functionality
- Mark as read functionality
- Floating notification badge

**Key Methods**:
- `loadUnreadInstructions()` - Load unread instructions for current user
- `showNextInstruction()` - Display the next highest priority instruction
- `markAsRead()` - Mark current instruction as read
- `closeModal()` - Close the notification modal

### MyInstructions
**Location**: `app/Http/Livewire/MyInstructions.php`
**View**: `resources/views/livewire/my-instructions.blade.php`

**Features**:
- View all read and unread instructions
- Search and filter functionality
- Sortable columns
- Pagination
- Mark unread instructions as read
- Expandable content view

**Key Methods**:
- `getReadInstructionsProperty()` - Get paginated read instructions
- `getUnreadInstructionsProperty()` - Get unread instructions
- `markAsRead($instructionId)` - Mark specific instruction as read
- `sortBy($field)` - Sort instructions by field
- `clearFilters()` - Reset all filters

## Routes

### Web Routes
**Location**: `routes/web.php`

```php
// My Instructions page
Route::get('/my-instructions', \App\Http\Livewire\MyInstructions::class)
    ->name('my-instructions');

// Mark instruction as read API
Route::post('/instructions/{instruction}/mark-read', function(\App\Models\Instruction $instruction) {
    // Mark instruction as read logic
})->name('instructions.mark-read');

// Get unread count API
Route::get('/api/instructions/unread-count', function() {
    // Return unread count for current user
})->name('api.instructions.unread-count');
```

## Installation & Setup

### 1. Run Migrations
```bash
php artisan migrate
```

### 2. Set Up Permissions
1. Access Filament admin panel
2. Navigate to "Instruction Management" → "Instruction Permissions"
3. Create permissions defining which roles can send instructions to which roles

### 3. Include in Layout
The instruction notifications are automatically included in the main layout for authenticated users:

```blade
@auth
    @livewire('instruction-notifications')
@endauth
```

## Usage

### For Administrators (Setting up permissions)

1. **Access Filament Admin Panel**
   - Navigate to "Instruction Management" → "Instruction Permissions"

2. **Create Permission**
   - Select "From Role" (who can send)
   - Select "To Role" (who receives)
   - Set as active
   - Save

3. **Bulk Create Permissions**
   - Use the bulk creation feature to set up multiple permissions at once

### For Authorized Users (Sending instructions)

1. **Access Filament Admin Panel**
   - Navigate to "Instruction Management" → "Instructions"

2. **Create Instruction**
   - Enter title and content
   - Select target role (limited to roles you have permission to send to)
   - Set priority level
   - Optionally set expiration date
   - Save

3. **Manage Instructions**
   - View all your created instructions
   - See read counts
   - Edit or deactivate instructions
   - Use bulk operations for multiple instructions

### For Users (Receiving instructions)

1. **Popup Notifications**
   - Unread instructions automatically appear as popups when logging in
   - Highest priority instructions are shown first
   - Click "Mark as Read" to dismiss and mark as read
   - Click "Close" to dismiss without marking as read

2. **Floating Badge**
   - When popups are closed, a floating badge shows unread count
   - Click the badge to show the next unread instruction

3. **My Instructions Page**
   - Access via `/my-instructions` route
   - View all unread instructions at the top
   - Click unread instructions to mark them as read
   - Search and filter through read instructions
   - View full content of any instruction

## Technical Details

### Auto-refresh Functionality
- Instructions are automatically checked every 30 seconds
- Additional checks when user becomes active (focus, click, mousemove)
- Real-time updates using Livewire

### Priority System
- **Urgent**: Red color, highest priority
- **High**: Orange color, high priority
- **Medium**: Blue color, medium priority (default)
- **Low**: Green color, lowest priority

### Security Features
- Role-based access control
- Permission validation before instruction creation
- Users can only see instructions they created or are authorized to send
- Secure API endpoints with authentication

### Performance Optimizations
- Efficient database queries with proper indexing
- Pagination for large instruction lists
- Lazy loading of relationships
- Optimized unread count calculations

## Customization

### Adding New Priority Levels
1. Update the `priority` enum in the instructions migration
2. Update the priority options in the InstructionResource form
3. Update the color mappings in the Livewire components

### Extending Functionality
- Add file attachments to instructions
- Implement instruction categories
- Add email notifications
- Create instruction templates
- Add instruction scheduling

### Styling Customization
- Modify the Blade templates in `resources/views/livewire/`
- Update Tailwind CSS classes for different styling
- Customize the modal appearance and behavior

## Troubleshooting

### Common Issues

1. **Permissions not working**
   - Ensure instruction permissions are created and active
   - Check that user roles match the permission settings

2. **Popups not appearing**
   - Verify user has unread instructions for their role
   - Check that instructions are active and not expired
   - Ensure Livewire is properly loaded

3. **Read status not updating**
   - Check database connectivity
   - Verify the instruction_reads table is properly created
   - Ensure user authentication is working

### Debug Commands
```bash
# Check unread instructions for a user
php artisan tinker
>>> $user = App\Models\User::find(1);
>>> App\Models\Instruction::getUnreadForRole($user->role, $user)->get();

# Check permissions for a role
>>> App\Models\InstructionPermission::getAllowedRolesFor('manager');
```

## Future Enhancements

1. **Real-time Notifications**
   - Implement WebSocket connections for instant notifications
   - Add browser push notifications

2. **Advanced Features**
   - Instruction templates
   - Scheduled instructions
   - Instruction categories and tags
   - File attachments
   - Rich text editor

3. **Analytics**
   - Instruction read rates
   - User engagement metrics
   - Popular instruction types

4. **Mobile App Support**
   - API endpoints for mobile applications
   - Push notifications for mobile devices

## Support

For technical support or feature requests, please refer to the development team or create an issue in the project repository. 