# دليل إنشاء البيانات من الداش بورد لاختبار نظام الإشعارات المالية

## 🎯 الهدف
هذا الدليل يوضح كيفية إنشاء البيانات المطلوبة من الداش بورد لاختبار نظام الإشعارات المالية بطريقة صحيحة.

## 👥 الخطوة الأولى: إنشاء المستخدمين المطلوبين

### 1. الدخول إلى إدارة الموظفين
- اذهب إلى: **Dashboard → Employees**
- اضغط على **"New Employee"**

### 2. إنشاء المستخدمين التاليين:

#### أ) محا<PERSON><PERSON> النقل (Transportation Accountant)
```
Name: محا<PERSON><PERSON> النقل
Email: <EMAIL>
Password: password123
Roles: ✅ Accountant Transportation
Transportation Countries: اختر جورجيا (أو أي دولة متاحة)
Is Active: ✅ Yes
```

#### ب) محاسب الفنادق (Hotel Accountant)
```
Name: محاسب الفنادق
Email: <EMAIL>
Password: password123
Roles: ✅ Accountant Hotel
Hotel Countries: اختر جورجيا (أو أي دولة متاحة)
Is Active: ✅ Yes
```

#### ج) محاسب BR - يوسف (BR Accountant)
```
Name: يوسف - محاسب BR
Email: <EMAIL>
Password: password123
Roles: ✅ Accountant BR
Is Active: ✅ Yes
```

#### د) مدير النقل (Transportation Manager)
```
Name: مدير النقل
Email: <EMAIL>
Password: password123
Roles: ✅ TRANSPORTATION MANAGER
Transportation Countries: اختر جورجيا
Transportation Companies: (سيتم ربطها لاحقاً)
Is Active: ✅ Yes
```

#### هـ) مستخدم شركة النقل (Transportation Company User)
```
Name: شركة النقل التجريبية
Email: <EMAIL>
Password: password123
Roles: ✅ Transportation Company
Is Active: ✅ Yes
```

## 🏢 الخطوة الثانية: إنشاء شركة النقل

### 1. الدخول إلى إدارة شركات النقل
- اذهب إلى: **Dashboard → Transportation Companies**
- اضغط على **"New Transportation Company"**

### 2. بيانات شركة النقل:
```
Name: شركة النقل التجريبية
Country: Georgia (جورجيا)
City: Tbilisi (تبليسي)
Phone: +************
Email: <EMAIL>
Password: password123
Is Active: ✅ Yes
Bonus Type: Fixed
Bonus Amount USD: 50
Bonus Amount Lari: 100
```

**ملاحظة:** بعد إنشاء الشركة، سيتم إنشاء مستخدم تلقائياً لها.

## 🏨 الخطوة الثالثة: إنشاء فندق (إذا لم يكن موجود)

### 1. الدخول إلى إدارة الفنادق
- اذهب إلى: **Dashboard → Hotels**
- اضغط على **"New Hotel"**

### 2. بيانات الفندق:
```
Name: فندق تجريبي
Country: Georgia
City: Tbilisi
Address: شارع تجريبي، تبليسي
Phone: +995555654321
Email: <EMAIL>
Is Active: ✅ Yes
```

## 📋 الخطوة الرابعة: إنشاء حجز (Reservation)

### 1. الدخول إلى إدارة الحجوزات
- اذهب إلى: **Dashboard → Reservations**
- اضغط على **"New Reservation"**

### 2. بيانات الحجز الأساسية:
```
Customer Name: عميل تجريبي
Customer Name EN: Test Customer
Phone: +995555999888
Arrival Country: Georgia
Arrival Date: (اختر تاريخ في المستقبل القريب)
Departure Date: (اختر تاريخ بعد أسبوع من تاريخ الوصول)
Number of Adults: 2
Number of Children: 0
Hotel Reservation Manager: محاسب الفنادق (اختر من القائمة)
Transportation Reservation Manager: محاسب النقل (اختر من القائمة)
```

### 3. حفظ الحجز
- اضغط **"Create"**
- احفظ رقم الحجز (Reservation ID) للاستخدام لاحقاً

## 🚗 الخطوة الخامسة: إنشاء حجز النقل

### 1. الدخول إلى حجوزات النقل المعلقة
- اذهب إلى: **Dashboard → Pending Transportation Company Bookings**
- ابحث عن الحجز الذي أنشأته
- اضغط على **"Edit"** أو **"View"**

### 2. إضافة تفاصيل النقل:
```
Transportation Company: شركة النقل التجريبية
From Date: نفس تاريخ الوصول
To Date: نفس تاريخ المغادرة
Price USD: 200
Price Lari: 500
VAT USD: 20
VAT Lari: 50
Status: Confirmed ✅
```

### 3. حفظ التغييرات

## 🏨 الخطوة السادسة: إنشاء حجز الفندق

### 1. الدخول إلى حجوزات الفنادق المعلقة
- اذهب إلى: **Dashboard → Pending Hotel Bookings**
- ابحث عن الحجز الذي أنشأته
- اضغط على **"Edit"**

### 2. إضافة تفاصيل الفندق:
```
Hotel: فندق تجريبي
Check In: نفس تاريخ الوصول
Check Out: نفس تاريخ المغادرة
Room Count: 1
Price USD: 300
Price Lari: 750
VAT USD: 30
VAT Lari: 75
Status: Confirmed ✅
```

### 3. حفظ التغييرات

## 💰 الخطوة السابعة: إنشاء المدفوعات

### أ) مدفوعات النقل

#### 1. الدخول إلى مدفوعات شركات النقل
- اذهب إلى: **Dashboard → Transportation Company Payments**
- ستجد مدفوعات تم إنشاؤها تلقائياً للحجز

#### 2. تعديل حالة الدفع:
- اضغط على **"Edit"** للدفعة
- غير الحالة إلى:
  - **"Pending"** لاختبار إشعار "في انتظار الموافقة"
  - **"Approved"** لاختبار إشعار "تم الموافقة"
- اضغط **"Save"**

### ب) مدفوعات الفنادق

#### 1. الدخول إلى مدفوعات الفنادق
- اذهب إلى: **Dashboard → Hotel Payments** (إذا كان متاحاً)
- أو ابحث في قسم الفنادق عن المدفوعات

#### 2. تعديل حالة الدفع بنفس الطريقة

## 🧪 الخطوة الثامنة: اختبار النظام

### 1. تسجيل الدخول بحسابات مختلفة
```bash
# في متصفحات مختلفة أو نوافذ خفية:
- محاسب النقل: <EMAIL>
- محاسب الفنادق: <EMAIL>  
- يوسف BR: <EMAIL>
- مدير النقل: <EMAIL>
```

### 2. تشغيل أوامر الاختبار
```bash
# اختبار إشعار الصوت
php artisan notifications:test-financial --type=sound --user-id=90

# اختبار إشعار الموافقة على الدفع
php artisan notifications:test-financial --type=payment_approved --user-id=90

# اختبار إشعار انتظار الموافقة
php artisan notifications:test-financial --type=payment_pending --user-id=90

# اختبار إشعار الجولة النشطة
php artisan notifications:test-financial --type=active_tour --user-id=90

# اختبار الإشعارات اليومية
php artisan notifications:test-financial --type=daily_alerts --user-id=90
```

### 3. مراقبة الإشعارات
- راقب جرس الإشعارات في كل متصفح
- تأكد من ظهور الإشعارات فوراً بدون تحديث الصفحة
- تأكد من تشغيل الصوت
- تأكد من ظهور إشعارات المتصفح

## 🔧 نصائح للاختبار

### 1. للحصول على User ID:
```bash
# في terminal
php artisan tinker
User::where('email', '<EMAIL>')->first()->id;
```

### 2. لمراقبة Pusher:
- افتح Developer Tools (F12)
- اذهب إلى Console
- ستجد رسائل Pusher والإشعارات

### 3. لاختبار الإشعارات اليومية:
```bash
# تشغيل الإشعارات اليومية يدوياً
php artisan notifications:send-daily-financial-alerts
```

### 4. صفحات الاختبار:
```
# صفحة اختبار Pusher
http://your-domain/test-financial-pusher

# صفحة اختبار Pusher البسيطة  
http://your-domain/pusher-simple-test
```

## ⚠️ ملاحظات مهمة

1. **تأكد من إعدادات Pusher:**
   ```env
   PUSHER_APP_KEY=309d0f1beaad790cf00e
   PUSHER_APP_CLUSTER=eu
   BROADCAST_DRIVER=pusher
   ```

2. **تأكد من تفعيل الإشعارات في المتصفح:**
   - اضغط على جرس الإشعارات
   - اقبل طلب الإذن للإشعارات

3. **للاختبار الشامل:**
   - أنشئ عدة حجوزات بتواريخ مختلفة
   - جرب تغيير حالات المدفوعات
   - اختبر مع مستخدمين مختلفين

4. **في حالة عدم وصول الإشعارات:**
   - تحقق من Console في Developer Tools
   - تأكد من اتصال Pusher
   - تحقق من صحة User ID المستخدم

## 📊 أنواع الإشعارات المتاحة للاختبار

1. **Daily Transportation Financial Alerts** - إشعارات النقل اليومية
2. **Daily Hotel Financial Alerts** - إشعارات الفنادق اليومية  
3. **Daily Financial Summary** - الملخص المالي اليومي
4. **Payment Approved by Youssef** - موافقة يوسف على الدفع
5. **Payment Pending Approval** - دفع في انتظار الموافقة
6. **Active Tour Notification** - إشعار الجولة النشطة
7. **Sales Manager Rejection** - رفض مدير المبيعات

بهذا الدليل، ستتمكن من إنشاء جميع البيانات المطلوبة واختبار النظام بطريقة شاملة! 🎉 