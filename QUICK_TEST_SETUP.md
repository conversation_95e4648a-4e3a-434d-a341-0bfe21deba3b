# دليل الإعداد السريع لاختبار الإشعارات المالية

## 🚀 الخطوات السريعة (5 دقائق)

### 1️⃣ إنشاء المستخدمين الأساسيين
```bash
# في Dashboard → Employees → New Employee

محاسب النقل:
- Name: مح<PERSON><PERSON><PERSON> النقل
- Email: <EMAIL>  
- Roles: ✅ Accountant Transportation
- Transportation Countries: Georgia

محاسب BR (يوسف):
- Name: يوسف BR
- Email: <EMAIL>
- Roles: ✅ Accountant BR
```

### 2️⃣ إنشاء شركة نقل
```bash
# في Dashboard → Transportation Companies → New

- Name: شركة تجريبية
- Country: Georgia
- Email: <EMAIL>
- Password: password123
```

### 3️⃣ إنشاء حجز
```bash
# في Dashboard → Reservations → New

- Customer Name: عميل تجريبي
- Phone: +************
- Arrival Country: Georgia
- Arrival Date: غداً
- Departure Date: بعد أسبوع
- Hotel Manager: م<PERSON><PERSON><PERSON><PERSON> النقل
- Transportation Manager: محاس<PERSON> النقل
```

### 4️⃣ إضافة حجز النقل
```bash
# في Dashboard → Pending Transportation Company Bookings
# ابحث عن الحجز → Edit

- Transportation Company: شركة تجريبية
- Price USD: 200
- Price Lari: 500
- Status: Confirmed ✅
```

### 5️⃣ اختبار الإشعارات
```bash
# احصل على User ID
php artisan tinker
User::where('email', '<EMAIL>')->first()->id;

# اختبر الإشعار (استبدل 123 بـ User ID الحقيقي)
php artisan notifications:test-financial --type=sound --user-id=123
```

### 6️⃣ مراقبة النتائج
1. سجل دخول بحساب: `<EMAIL>`
2. راقب جرس الإشعارات 🔔
3. افتح Console (F12) لمراقبة Pusher
4. يجب أن تسمع صوت وترى الإشعار فوراً!

## 🎯 اختبار سريع للأنواع المختلفة

```bash
# إشعار الموافقة على الدفع
php artisan notifications:test-financial --type=payment_approved --user-id=123

# إشعار انتظار الموافقة  
php artisan notifications:test-financial --type=payment_pending --user-id=123

# إشعار الجولة النشطة
php artisan notifications:test-financial --type=active_tour --user-id=123

# الإشعارات اليومية
php artisan notifications:test-financial --type=daily_alerts --user-id=123
```

## 🔧 حل المشاكل السريع

### إذا لم تظهر الإشعارات:
1. تحقق من Console (F12) - هل يوجد أخطاء؟
2. تأكد من إعدادات Pusher في `.env`
3. تأكد من User ID صحيح
4. جرب تحديث الصفحة واقبل إذن الإشعارات

### إذا لم يعمل الصوت:
1. اضغط في أي مكان في الصفحة أولاً
2. اقبل إذن الإشعارات في المتصفح
3. تأكد من عدم كتم الصوت

## 📱 صفحات الاختبار المباشر

```
# اختبار Pusher مباشر
http://your-domain/test-financial-pusher

# اختبار بسيط
http://your-domain/pusher-simple-test
```

**🎉 بهذه الخطوات البسيطة ستتمكن من اختبار النظام في أقل من 5 دقائق!** 