# Instruction System Testing Guide

## Quick Test Setup

### 1. Create Test Data

Run these commands in `php artisan tinker`:

```php
// Create test users with different roles
$manager = App\Models\User::create([
    'name' => 'Test Manager',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'manager'
]);

$sales = App\Models\User::create([
    'name' => 'Test Sales Rep',
    'email' => '<EMAIL>', 
    'password' => bcrypt('password'),
    'role' => 'sales'
]);

// Create instruction permission (manager can send to sales)
App\Models\InstructionPermission::create([
    'from_role' => 'manager',
    'to_role' => 'sales',
    'is_active' => true
]);

// Create test instruction
App\Models\Instruction::create([
    'title' => 'Test Urgent Instruction',
    'content' => 'This is a test instruction to verify the system is working correctly.',
    'target_role' => 'sales',
    'created_by' => $manager->id,
    'priority' => 'urgent',
    'is_active' => true
]);
```

### 2. Test the System

1. **Login as Manager** (<EMAIL> / password)
   - Access Filament admin panel
   - Navigate to "Instruction Management" → "Instructions"
   - Verify you can see the test instruction
   - Create a new instruction for sales role

2. **Login as Sales Rep** (<EMAIL> / password)
   - You should see a popup notification with the urgent instruction
   - Test the "Mark as Read" and "Close" buttons
   - Visit `/my-instructions` to see the instruction history

3. **Test Permissions**
   - Try creating an instruction for a role you don't have permission for
   - Verify the system prevents unauthorized instruction creation

## Manual Testing Checklist

### Phase 1: Instruction Permissions

- [ ] Can create instruction permissions in Filament
- [ ] Can edit existing permissions
- [ ] Can delete permissions
- [ ] Bulk operations work correctly
- [ ] Duplicate permissions are prevented
- [ ] Inactive permissions don't allow instruction creation

### Phase 2: Instructions

#### Creating Instructions
- [ ] Can create instructions for authorized roles only
- [ ] Cannot create instructions for unauthorized roles
- [ ] All priority levels work correctly
- [ ] Expiration dates work properly
- [ ] Instructions are saved with correct creator

#### Viewing Instructions
- [ ] Popup notifications appear for unread instructions
- [ ] Highest priority instructions show first
- [ ] Floating badge shows correct unread count
- [ ] My Instructions page loads correctly
- [ ] Search and filtering work properly

#### Reading Instructions
- [ ] Mark as read functionality works
- [ ] Read status persists across sessions
- [ ] Read instructions appear in history
- [ ] Unread count updates correctly

### User Interface Testing

#### Popup Notifications
- [ ] Modal appears with correct styling
- [ ] Priority colors display correctly
- [ ] Content is properly formatted
- [ ] Buttons work as expected
- [ ] Auto-refresh functionality works

#### My Instructions Page
- [ ] Unread instructions section appears when applicable
- [ ] Search functionality works
- [ ] Priority filtering works
- [ ] Sorting works correctly
- [ ] Pagination works
- [ ] Expandable content works
- [ ] Mobile responsive design

### Security Testing

- [ ] Users can only see instructions for their role
- [ ] Users can only create instructions they have permission for
- [ ] API endpoints require authentication
- [ ] Role-based access control works correctly

## Automated Testing

### Unit Tests

Create test files in `tests/Unit/`:

```php
// tests/Unit/InstructionPermissionTest.php
public function test_can_check_if_role_can_send_instructions()
{
    InstructionPermission::create([
        'from_role' => 'manager',
        'to_role' => 'sales',
        'is_active' => true
    ]);
    
    $this->assertTrue(
        InstructionPermission::canSendInstructions('manager', 'sales')
    );
    
    $this->assertFalse(
        InstructionPermission::canSendInstructions('sales', 'manager')
    );
}

// tests/Unit/InstructionTest.php
public function test_can_mark_instruction_as_read()
{
    $user = User::factory()->create(['role' => 'sales']);
    $instruction = Instruction::factory()->create(['target_role' => 'sales']);
    
    $this->assertFalse($instruction->hasBeenReadBy($user));
    
    $instruction->markAsReadBy($user);
    
    $this->assertTrue($instruction->hasBeenReadBy($user));
}
```

### Feature Tests

Create test files in `tests/Feature/`:

```php
// tests/Feature/InstructionSystemTest.php
public function test_user_can_view_my_instructions_page()
{
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/my-instructions');
    
    $response->assertStatus(200);
    $response->assertSeeLivewire('my-instructions');
}

public function test_user_can_mark_instruction_as_read()
{
    $user = User::factory()->create(['role' => 'sales']);
    $instruction = Instruction::factory()->create(['target_role' => 'sales']);
    
    $response = $this->actingAs($user)
        ->post("/instructions/{$instruction->id}/mark-read");
    
    $response->assertJson(['success' => true]);
    $this->assertTrue($instruction->fresh()->hasBeenReadBy($user));
}
```

## Performance Testing

### Database Queries
- [ ] Unread instruction queries are optimized
- [ ] Pagination doesn't cause N+1 queries
- [ ] Indexes are properly used

### Load Testing
- [ ] System handles multiple concurrent users
- [ ] Popup notifications don't cause performance issues
- [ ] Auto-refresh doesn't overload the server

## Browser Testing

Test in multiple browsers:
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

Test responsive design:
- [ ] Desktop (1920x1080)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)

## Common Issues and Solutions

### Issue: Popups not appearing
**Solution**: Check that:
- User has unread instructions for their role
- Instructions are active and not expired
- Livewire is properly loaded
- JavaScript console for errors

### Issue: Permissions not working
**Solution**: Verify:
- Instruction permissions are created and active
- User roles match exactly (case-sensitive)
- Database relationships are correct

### Issue: Read status not updating
**Solution**: Check:
- Database connectivity
- instruction_reads table exists
- User authentication is working
- CSRF tokens are valid

## Test Data Cleanup

After testing, clean up test data:

```php
// In tinker
App\Models\Instruction::where('title', 'LIKE', 'Test%')->delete();
App\Models\InstructionPermission::where('from_role', 'manager')->where('to_role', 'sales')->delete();
App\Models\User::where('email', 'LIKE', '%@test.com')->delete();
```

## Continuous Integration

Add these tests to your CI pipeline:

```yaml
# .github/workflows/tests.yml
- name: Run Instruction System Tests
  run: |
    php artisan test --filter=InstructionTest
    php artisan test --filter=InstructionPermissionTest
    php artisan test --filter=InstructionSystemTest
```

## Monitoring

Set up monitoring for:
- Instruction creation rates
- Read rates
- System performance
- Error rates
- User engagement metrics 