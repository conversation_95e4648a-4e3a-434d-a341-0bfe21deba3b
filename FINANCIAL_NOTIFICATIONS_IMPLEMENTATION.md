# Financial Notification System Implementation

## Overview

This document describes the comprehensive financial notification system implemented for the Laravel CRM application. The system provides automated notifications for financial events, payment approvals, and daily financial alerts.

## Features Implemented

### 1. Daily Financial Alerts 📊
- **Transportation Unpaid Reports**: Daily notifications for unpaid transportation amounts where tours started or ended on the current day
- **Hotel Unpaid Reports**: Daily notifications for unpaid hotel amounts where check-in or check-out occurred on the current day
- **Financial Summary**: Comprehensive daily financial summary for managers

### 2. Payment Approval Notifications ✅
- **Approval Notifications**: When <PERSON><PERSON><PERSON> (BR Accountant) approves payments, relevant accounting users receive notifications
- **Pending Approval Notifications**: When payments are sent for approval, <PERSON><PERSON><PERSON> receives notifications

### 3. Active Tour Notifications 🚌
- **Driver Notifications**: When tours become active, transportation company users receive notifications
- **Manager Notifications**: Transportation managers are notified of active tours

### 4. Sales Manager Rejection Notifications ❌
- **Rejection Alerts**: When sales managers reject requests, requesters receive detailed notifications with reasons

## System Architecture

### Core Components

#### 1. NotificationService (`app/Services/NotificationService.php`)
Enhanced with new methods:
- `sendDailyTransportationFinancialAlerts()`
- `sendDailyHotelFinancialAlerts()`
- `sendPaymentApprovedNotification()`
- `sendPaymentPendingApprovalNotification()`
- `sendActiveTourNotification()`
- `sendSalesManagerRejectionNotification()`
- `sendDailyFinancialSummaryNotification()`

#### 2. Events (`app/Events/`)
- `PaymentApproved`: Triggered when payments are approved
- `PaymentSentForApproval`: Triggered when payments are sent for approval
- `TourActivated`: Triggered when tours become active

#### 3. Event Listeners (`app/Listeners/`)
- `SendPaymentApprovedNotification`: Handles payment approval events
- `SendPaymentPendingApprovalNotification`: Handles pending approval events
- `SendTourActivatedNotification`: Handles tour activation events

#### 4. Model Observers (`app/Observers/`)
- `TransportationCompanyPaymentObserver`: Monitors payment status changes
- `HotelPaymentObserver`: Monitors hotel payment status changes
- `TransportationCompanyBookingObserver`: Monitors tour activation

#### 5. Console Commands (`app/Console/Commands/`)
- `SendDailyFinancialAlertsCommand`: Manual execution of daily alerts
- `TestFinancialNotificationsCommand`: Comprehensive testing tool

#### 6. Jobs (`app/Jobs/`)
- `SendDailyFinancialAlertsJob`: Queued job for daily financial alerts

## User Roles and Permissions

### Notification Recipients

#### Transportation Accountants
- Role: `ACCOUNTANT_TRANSPORTATION`
- Receives: Transportation payment notifications, daily transportation alerts

#### Hotel Accountants
- Role: `ACCOUNTANT_HOTEL`
- Receives: Hotel payment notifications, daily hotel alerts

#### BR Accountants (Youssef)
- Role: `ACCOUNTANT_BR`
- Receives: Pending approval notifications, financial summaries
- Sends: Payment approval notifications

#### Transportation Managers
- Role: `TRANSPORTATION_MANAGER`, `BOOKING_MANAGER_TRANSPORTATION`
- Receives: Active tour notifications

#### Transportation Companies
- Role: `ROLE_TRANSPORTATION_COMPANY`
- Receives: Active tour notifications for their tours

## Scheduling

### Automated Schedules (configured in `app/Console/Kernel.php`)

```php
// Daily financial alerts at 8:00 AM (Asia/Tbilisi)
$schedule->command('notifications:send-daily-financial-alerts')
         ->dailyAt('08:00')
         ->timezone('Asia/Tbilisi');

// Alternative job-based approach at 8:30 AM
$schedule->job(new SendDailyFinancialAlertsJob())
         ->dailyAt('08:30')
         ->timezone('Asia/Tbilisi');

// Financial summary at 6:00 PM
$schedule->call(function () {
    app(\App\Services\NotificationService::class)->sendDailyFinancialSummaryNotification();
})->dailyAt('18:00');
```

## Testing

### Test Command Usage

```bash
# Test all notification features
php artisan notifications:test-financial

# Test only daily alerts
php artisan notifications:test-financial --type=daily

# Test only events
php artisan notifications:test-financial --type=events

# Test sound notifications for specific user
php artisan notifications:test-financial --type=sound --user-id=123

# Run daily alerts manually
php artisan notifications:send-daily-financial-alerts

# Dry run (no actual notifications sent)
php artisan notifications:send-daily-financial-alerts --dry-run
```

### Testing Scenarios

1. **Daily Alerts Testing**
   - Creates test notifications for unpaid transportation amounts
   - Creates test notifications for unpaid hotel amounts
   - Sends financial summary to managers

2. **Event Testing**
   - Triggers payment approval events
   - Triggers payment pending approval events
   - Triggers tour activation events

3. **Sound Notification Testing**
   - Sends test notification with sound alert
   - Verifies browser notification functionality

## Integration Points

### Existing Notification System
The implementation extends the existing notification infrastructure:
- Uses existing `Notification` model and `createForUser()` method
- Integrates with existing Livewire notification navbar
- Supports existing notification types and sound alerts

### Payment Workflow Integration
- Automatically triggers events when payment status changes
- Monitors `send_to_approve_at` field changes
- Tracks payment approval status transitions

### Tour Management Integration
- Monitors `is_active` field changes in transportation bookings
- Tracks booking status changes to active/confirmed states

## Configuration

### Environment Variables
```env
# Admin email for failure notifications
MAIL_ADMIN_EMAIL=<EMAIL>

# Queue configuration for notification processing
QUEUE_CONNECTION=database
```

### Queue Configuration
Notifications are processed in the background using Laravel queues:
- Queue name: `notifications`
- Retry attempts: 3
- Timeout: 300 seconds

## Notification Types

### Financial Alert Types
- `financial_alert`: Daily unpaid amount alerts
- `payment_approved`: Payment approval confirmations
- `payment_pending_approval`: Pending approval requests
- `active_tour`: Tour activation notifications
- `rejection`: Sales manager rejection notifications
- `financial_summary`: Daily financial summaries

### Notification Data Structure
```php
[
    'type' => 'financial_alert',
    'title' => '🚨 تنبيه مالي - مدفوعات النقل المطلوبة اليوم',
    'message' => 'يوجد 5 جولة نقل تحتاج دفع اليوم بإجمالي 1500 لاري',
    'data' => [
        'tour_count' => 5,
        'total_amount' => 1500,
        'tours' => [...],
    ],
    'isImportant' => true,
    'icon' => '💰',
    'actionUrl' => '/admin/transportation-company-payments'
]
```

## Monitoring and Logging

### Log Channels
All notification activities are logged with detailed information:
- Successful notifications sent
- Failed notification attempts
- Event processing status
- Observer trigger events

### Error Handling
- Comprehensive try-catch blocks in all components
- Failed job handling with retry mechanisms
- Detailed error logging for debugging

## Performance Considerations

### Database Optimization
- Efficient queries with proper indexing
- Eager loading of relationships
- Filtered queries to reduce data processing

### Queue Processing
- Background processing for all notifications
- Retry mechanisms for failed jobs
- Timeout protection for long-running processes

## Security

### Access Control
- Role-based notification targeting
- User activity status verification
- Secure data handling in notifications

### Data Privacy
- Sensitive financial data properly handled
- User-specific notification filtering
- Audit trail for all notification activities

## Maintenance

### Regular Tasks
1. Monitor queue processing performance
2. Review notification delivery rates
3. Update user role assignments as needed
4. Verify scheduled task execution

### Troubleshooting
1. Check Laravel scheduler is running: `php artisan schedule:list`
2. Verify queue workers are active: `php artisan queue:work`
3. Review logs for error patterns: `storage/logs/laravel.log`
4. Test notification system: `php artisan notifications:test-financial`

## Future Enhancements

### Potential Improvements
1. **Real-time Notifications**: WebSocket integration for instant notifications
2. **Email Notifications**: Email backup for critical financial alerts
3. **SMS Integration**: SMS alerts for urgent financial matters
4. **Dashboard Widgets**: Financial notification summary widgets
5. **Notification Preferences**: User-configurable notification settings

### Scalability Considerations
1. **Redis Queue**: Upgrade to Redis for better queue performance
2. **Notification Channels**: Multiple delivery channels (email, SMS, push)
3. **Batch Processing**: Bulk notification processing for large datasets
4. **Caching**: Cache frequently accessed financial data

## Support

For technical support or questions about the financial notification system:
1. Review this documentation
2. Check application logs
3. Run test commands to verify functionality
4. Contact the development team with specific error details

---

**Implementation Date**: Current
**Version**: 1.0
**Status**: Production Ready ✅ 