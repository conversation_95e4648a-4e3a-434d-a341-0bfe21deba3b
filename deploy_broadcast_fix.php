<?php

echo "🚀 Deploying Broadcast Driver Fix...\n";

// Step 1: Check current configuration
echo "\n1. Checking current broadcast configuration...\n";

$currentConfig = config('broadcasting.default');
echo "Current broadcast driver: " . $currentConfig . "\n";

if ($currentConfig === 'pusher') {
    echo "❌ Still using Pusher driver - needs to be changed\n";
} else {
    echo "✅ Broadcast driver is: " . $currentConfig . "\n";
}

// Step 2: Check environment variable
echo "\n2. Checking environment variable...\n";
$envBroadcast = env('BROADCAST_DRIVER');
echo "BROADCAST_DRIVER from env: " . ($envBroadcast ?: 'NOT SET') . "\n";

// Step 3: Update .env file if needed
echo "\n3. Updating .env file...\n";

$envPath = base_path('.env');
if (!file_exists($envPath)) {
    echo "❌ .env file not found at: " . $envPath . "\n";
    exit(1);
}

$envContent = file_get_contents($envPath);

// Check if BROADCAST_DRIVER exists in .env
if (strpos($envContent, 'BROADCAST_DRIVER=') !== false) {
    // Replace existing
    $newContent = preg_replace('/BROADCAST_DRIVER=.*/', 'BROADCAST_DRIVER=log', $envContent);
    file_put_contents($envPath, $newContent);
    echo "✅ Updated existing BROADCAST_DRIVER to 'log'\n";
} else {
    // Add new line
    $newContent = $envContent . "\nBROADCAST_DRIVER=log\n";
    file_put_contents($envPath, $newContent);
    echo "✅ Added new BROADCAST_DRIVER=log\n";
}

// Step 4: Clear configuration cache
echo "\n4. Clearing configuration cache...\n";
try {
    \Illuminate\Support\Facades\Artisan::call('config:clear');
    echo "✅ Configuration cache cleared\n";
    
    \Illuminate\Support\Facades\Artisan::call('cache:clear');
    echo "✅ Application cache cleared\n";
} catch (Exception $e) {
    echo "❌ Cache clear failed: " . $e->getMessage() . "\n";
}

// Step 5: Verify the change
echo "\n5. Verifying the change...\n";
// Re-read config after clearing cache
config()->forget('broadcasting');
$newConfig = config('broadcasting.default');
echo "New broadcast driver: " . $newConfig . "\n";

if ($newConfig === 'log') {
    echo "✅ SUCCESS: Broadcast driver is now set to 'log'\n";
    echo "✅ Chat system will now use polling instead of Pusher\n";
} else {
    echo "❌ FAILED: Broadcast driver is still: " . $newConfig . "\n";
    echo "❌ You may need to restart your web server\n";
}

// Step 6: Test that Pusher errors are gone
echo "\n6. Testing that broadcast events work without Pusher errors...\n";
try {
    // This should not cause any Pusher errors now
    event(new \App\Events\UserOnlineStatusChanged(1, now()));
    echo "✅ Broadcast event dispatched successfully (logged instead of Pusher)\n";
} catch (Exception $e) {
    echo "❌ Broadcast event failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 Deployment completed!\n";
echo "\n📋 Next steps for your server:\n";
echo "1. Upload this script to your server\n";
echo "2. Run: php deploy_broadcast_fix.php\n";
echo "3. Restart your web server (nginx/apache)\n";
echo "4. Test the chat system\n";
echo "\n💡 The chat system will now work with polling instead of Pusher!\n"; 