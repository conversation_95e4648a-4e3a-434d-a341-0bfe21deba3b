# نظام إشعارات "طرق الدفع الأخرى"

## نظرة عامة
يقوم هذا النظام بإرسال إشعارات فورية للمستخدمين الإداريين عند إنشاء حجوزات جديدة باستخدام طريقة دفع "Other payment methods".

## كيف يعمل النظام

### 1. مراقبة المدفوعات
- يتم مراقبة إنشاء المدفوعات الجديدة عبر `PaymentObserver`
- يتحقق النظام من أن طريقة الدفع هي بالضبط "Other payment methods"
- يتم التحقق من أن الدفعة مرتبطة بحجز (وليس نوع آخر)

### 2. إنشاء الإشعارات
عند إنشاء دفعة بطريقة "Other payment methods":

#### أ) إشعارات قاعدة البيانات
- يتم إنشاء سجل إشعار في جدول `notifications` لكل مستخدم إداري
- يحتوي الإشعار على تفاصيل الحجز والدفعة
- يظهر في قائمة الإشعارات في شريط التنقل

#### ب) الإشعارات الفورية (Pusher)
- يتم بث الإشعار فوراً عبر Pusher WebSockets
- يصل الإشعار لجميع المستخدمين الإداريين المتصلين
- يظهر الإشعار فوراً بدون الحاجة لتحديث الصفحة

### 3. عرض الإشعارات
- تظهر الإشعارات في أيقونة الجرس في شريط التنقل
- يتم تحديث عداد الإشعارات فوراً
- يتم تشغيل صوت إشعار
- يمكن عرض إشعار المتصفح (إذا كانت الأذونات ممنوحة)

## الملفات المعنية

### الأحداث (Events)
- `app/Events/ReservationWithOtherPaymentCreated.php` - حدث البث الفوري

### المراقبات (Observers)
- `app/Observers/PaymentObserver.php` - مراقب إنشاء المدفوعات

### النماذج (Models)
- `app/Models/Payment.php` - نموذج المدفوعات
- `app/Models/Notification.php` - نموذج الإشعارات
- `app/Models/User.php` - نموذج المستخدمين

### العروض (Views)
- `resources/views/livewire/notification-navbar.blade.php` - شريط الإشعارات
- `resources/views/test-other-payment-notifications.blade.php` - صفحة الاختبار

### المسارات (Routes)
- `/test-other-payment-notifications` - صفحة اختبار النظام
- `/test-create-other-payment` - API لإنشاء دفعة تجريبية

## كيفية الاختبار

### 1. الاختبار اليدوي
1. سجل الدخول كمستخدم إداري
2. اذهب إلى `/test-other-payment-notifications`
3. اضغط على "محاكاة دفعة جديدة"
4. راقب شريط التنقل للإشعارات الجديدة

### 2. الاختبار البرمجي
```bash
# إنشاء دفعة تجريبية عبر Tinker
php artisan tinker

$reservation = App\Models\Reservation::first();
$paymentMethod = App\Models\PaymentMethod::firstOrCreate(['name' => 'Other payment methods']);
$payment = App\Models\Payment::create([
    'payable_type' => App\Models\Reservation::class,
    'payable_id' => $reservation->id,
    'payment_method_id' => $paymentMethod->id,
    'amount' => 100.00,
    'status' => 'pending',
    'user_id' => 1
]);
```

## التكوين المطلوب

### متغيرات البيئة
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

### تسجيل المراقب
تأكد من تسجيل `PaymentObserver` في `AppServiceProvider`:

```php
// في app/Providers/AppServiceProvider.php
public function boot()
{
    Payment::observe(PaymentObserver::class);
}
```

## المستخدمون المستهدفون
- جميع المستخدمين الذين لديهم `is_admin = true`
- المستخدمون النشطون فقط (`is_active = true`)

## معلومات الإشعار
- **العنوان**: "🔔 حجز جديد بطريقة دفع أخرى"
- **الرسالة**: تحتوي على رقم الحجز، اسم العميل، المبلغ، وطريقة الدفع
- **الأيقونة**: 💳
- **الأولوية**: مهم (`is_important = true`)
- **رابط الإجراء**: `/admin/pending-payments`

## استكشاف الأخطاء

### لا تظهر الإشعارات
1. تحقق من تكوين Pusher
2. تأكد من تسجيل المراقب
3. تحقق من وجود طريقة دفع "Other payment methods"
4. راجع سجلات Laravel

### الإشعارات لا تصل فوراً
1. تحقق من اتصال Pusher في وحدة تحكم المتصفح
2. تأكد من الاشتراك في القناة الصحيحة
3. تحقق من أذونات المتصفح للإشعارات

### عدم تشغيل الصوت
1. تحقق من أذونات المتصفح للصوت
2. تأكد من تفاعل المستخدم مع الصفحة أولاً
3. راجع وحدة تحكم المتصفح للأخطاء

## الأمان
- الإشعارات محدودة للمستخدمين الإداريين فقط
- يتم التحقق من صحة البيانات قبل الإرسال
- القنوات محمية بمعرف المستخدم

## الأداء
- الإشعارات فورية عبر WebSockets
- لا توجد حاجة لاستطلاع الخادم
- تحديث فوري لواجهة المستخدم 