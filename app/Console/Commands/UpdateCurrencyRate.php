<?php

namespace App\Console\Commands;

use App\Models\Expense;
use App\Models\HotelRoom;
use App\Models\Reservation;
use App\Models\TransportationCompanyExtraService;
use App\Models\TransportationCompanyPrice;
use Illuminate\Console\Command;

class UpdateCurrencyRate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-currency-rate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        HotelRoom::query()->get()->each(function (HotelRoom $hotelRoom) {
            $hotelRoom->update([
                'currency_rate_fixed' => $hotelRoom->modelCurrencyRate?->currencyRate?->rate,
            ]);
        });

        TransportationCompanyPrice::query()->get()->each(function (TransportationCompanyPrice $transportationCompanyPrice) {
            $transportationCompanyPrice->update([
                'currency_rate_fixed' =>  $transportationCompanyPrice->modelCurrencyRate?->currencyRate?->rate,
            ]);
        });

        TransportationCompanyExtraService::query()->get()->each(function (TransportationCompanyExtraService $transportationCompanyExtraService) {
            $transportationCompanyExtraService->update([
                'currency_rate_fixed' => $transportationCompanyExtraService->modelCurrencyRate?->currencyRate?->rate,
            ]);
        });

        Reservation::query()->get()->each(function (Reservation $reservation) {
            $reservation->update([
                'currency_rate_fixed' => $reservation->modelCurrencyRate?->currencyRate?->rate,
            ]);
        });

        Expense::query()->get()->each(function (Expense $expense) {
            $expense->update([
                'currency_rate_fixed' => $expense->modelCurrencyRate?->currencyRate?->rate,
            ]);
        });

        return Command::SUCCESS;
    }
}
