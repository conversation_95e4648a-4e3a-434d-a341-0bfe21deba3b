<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DriverExcel implements FromCollection, WithHeadings
{
    public function __construct(
        protected $tours,
        protected $reservation,
    )
    {
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return collect($this->tours)->map(function ($tour) {
            return array_merge(
                [
                    'trip_code' => $this->reservation?->trip_code,
                ],
                $tour,
                [
                    'sales' => $this->reservation->user?->name,
                    'sales_phone' => $this->reservation->user?->phone,
                    'persons' => $this->reservation?->number_of_people,
                    'children' => $this->reservation?->number_of_children,
                    'arrival_airport' => $this->reservation?->arrivalAirport?->name,
                    'arrival_flight_number' => $this->reservation?->arrival_flight_number,
                    'arrival_date_time' => $this->reservation->arrival_date,
                    'departure_airport' => $this->reservation?->departureAirport?->name,
                    'departure_flight_number' => $this->reservation?->departure_flight_number,
                    'departure_date' => $this->reservation?->departure_date,
                    'tourist_visit_office' => $this->reservation->tourist_visit_office ? 'yes' : 'no',
                    'transportation_manager' => $this->reservation?->bookingTransportationUser?->name,
                    'booking_manager' => $this->reservation?->bookingHotelUser?->name,
                    'booking_manager_phone' => $this->reservation?->bookingHotelUser?->phone,
                ]
            );
        });
    }

    public function headings(): array
    {
        return [
            'Trip Code',
            'Date',
            'Hotel',
            'Tour Name',
            'Comment',
            'Sales',
            'Sales Phone',
            'Persons',
            'Children',
            'Arrival Airport',
            'Arrival Flight Number',
            'Arrival Date Time',
            'Departure Airport',
            'Departure Flight Number',
            'Departure Date',
            'Tourist Visit Office',
            'Transportation Manager',
            'Booking Manager',
            'Booking Manager Phone',
        ];
    }
}
