<?php

namespace App\Filament\Actions;

use App\Enums\PaymentStatus;
use App\Filament\Resources\CancelledHotelBookingResource\Pages\ListCancelledHotelBookings;
use App\Filament\Resources\ConfirmedHotelBookingResource\Pages\ListConfirmedHotelBookings;
use App\Forms\Components\UsdToCurrencyInputs;
use App\Models\Hotel;
use App\Models\HotelBooking;
use App\Models\HotelBookingRoom;
use App\Models\HotelInvoice;
use App\Models\HotelPayment;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class EditHotelPaymentsAction extends Action
{

    public static function make(?string $name = 'edit_payments'): static
    {
        return parent::make($name);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Payments'))
            ->icon('heroicon-o-pencil');

        $this->form(function ($record) {
            $hotel = $record->hotel;

            $records = $record->hotelBookings;

            return [
                Group::make([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('hotel_name')
                                ->label(__('Hotel Name'))
                                ->columnSpan(2)
                                ->default(function () use ($hotel) {
                                    return $hotel?->name;
                                }),
                            TextInput::make('original_price_usd')
                                ->label(__('Original cost USD'))
                                ->default(function () use ($records) {
                                    return $records->sum('original_price_usd');
                                }),

                            TextInput::make('original_price_lari')
                                ->label(__('Original cost LARI'))
                                ->default(function () use ($records) {
                                    return $records->sum(function ($record) {
                                        return $record->amountLocalCurrency('original_price_usd');
                                    });
                                }),

                            TextInput::make('actual_cost_usd')
                                ->label(__('Actual cost USD'))
                                ->default(function () use ($records) {
                                    return $records->sum('actual_cost_usd');
                                }),

                            TextInput::make('actual_cost_lari')
                                ->label(__('Actual cost LARI'))
                                ->default(function () use ($records) {
                                    return $records->sum(function ($record) {
                                        return $record->amountLocalCurrency('actual_cost_usd');
                                    });
                                }),
                        ])->disabled(),

                    Repeater::make('hotelBookings')
                        ->model(HotelBooking::class)
                        ->label(__('Hotel Bookings'))
                        ->disableItemCreation()
                        ->disableItemDeletion()
                        ->afterStateHydrated(function ($component) use ($records) {
                            $state = $records->mapWithKeys(function ($record) {
                                $uuid1 = Str::uuid()->toString();

                                $countryId = $record->getCountryId();

                                return [
                                    $uuid1 => [
                                        'hotel_booking_id' => $record->id,
                                        'currency_rate_fixed' => $record->currency_rate_fixed,
                                        'country_id' => $countryId,
                                        'trip_code' => $record->reservation?->trip_code,
                                        'cost_usd' => $record->price_usd,
                                        'cost_lari' => $record->price_lari,
                                        'hotel_booking_rooms' => $record->hotelBookingRooms->mapWithKeys(function (HotelBookingRoom $room) use ($countryId, $record) {
                                            $uuid1 = Str::uuid()->toString();
                                            $uuid2 = Str::uuid()->toString();

                                            $oldPayments = $room->hotelPayments->mapWithKeys(function (HotelPayment $payment) use ($countryId, $record) {
                                                $uuid = Str::uuid()->toString();
                                                return [
                                                    $uuid => [
                                                        'hotel_payment_id' => $payment->id,
                                                        'price_usd' => $payment->amount,
                                                        'price_lari' => $payment->amount_lari,
                                                        'date' => $payment->scheduled_at,
                                                        'country_id' => $countryId,
                                                        'use_wallet' => $payment->use_wallet,
                                                        'currency_rate_fixed' => $record->currency_rate_fixed,
                                                    ]
                                                ];
                                            })->toArray();
                                            return [
                                                $uuid1 => [
                                                    'hotel_booking_room_id' => $room->id,
                                                    'country_id' => $countryId,
                                                    'room_type' => 'X(' . $room->count . ') ' . $room->roomType?->name,
                                                    'price_usd' => $room->price_usd,
                                                    'price_lari' => $room->price_lari,
                                                    'currency_rate_fixed' => $record->currency_rate_fixed,
                                                    'payments' => count($oldPayments) ? $oldPayments : [
                                                        $uuid2 => [
                                                            'price_usd' => $room->price_usd,
                                                            'price_lari' => $room->price_lari,
                                                            'date' => now()->toDateString(),
                                                            'country_id' => $countryId,
                                                            'currency_rate_fixed' => $record->currency_rate_fixed,
                                                            'use_wallet' => false
                                                        ]
                                                    ]
                                                ]
                                            ];
                                        })->toArray(),
                                    ]
                                ];
                            })->toArray();

                            $component->state($state);
                        })
                        ->registerListeners([
                            'repeater::deleteItem' => [
                                function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                    if ($statePath !== $component->getStatePath()) {
                                        return;
                                    }
                                },
                            ],
                        ])
                        ->schema(function () use ($hotel) {
                            return [
                                Hidden::make('hotel_booking_id'),

                                Grid::make(3)
                                    ->schema([
                                        TextInput::make('trip_code')
                                            ->label(__('Trip code'))
                                            ->disabled(),

                                        TextInput::make('cost_usd')
                                            ->label(__('Cost USD'))
                                            ->disabled(),

                                        TextInput::make('cost_lari')
                                            ->disabled()
                                            ->label(__('Cost Local Currency')),
                                    ]),

                                Repeater::make('hotel_booking_rooms')
                                    ->model(HotelBookingRoom::class)
                                    ->disableItemCreation()
                                    ->disableItemDeletion()
                                    ->label(__('Rooms'))
                                    ->schema(function () use ($hotel) {
                                        return [
                                            Hidden::make('hotel_booking_room_id'),

                                            Grid::make(3)
                                                ->schema([
                                                    TextInput::make('room_type')
                                                        ->label(__('Room Type'))
                                                        ->disabled()
                                                        ->columnSpan(1),

                                                    UsdToCurrencyInputs::make()
                                                        ->countryId(function () use ($hotel) {
                                                            return $hotel->getCountryId();
                                                        })
                                                        ->disabled()
                                                        ->usdInputName('price_usd')
                                                        ->lariInputName('price_lari')
                                                        ->columnSpan(2),
                                                ]),

                                            Repeater::make('payments')
                                                ->model(HotelPayment::class)
                                                ->label(__('Payments'))
                                                ->registerListeners([
                                                    'repeater::deleteItem' => [
                                                        function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                                            if ($statePath !== $component->getStatePath()) {
                                                                return;
                                                            }

                                                        },
                                                    ],
                                                ])
                                                ->schema(function () use ($hotel) {
                                                    return [
                                                        Grid::make(3)
                                                            ->schema([
                                                                Hidden::make('hotel_payment_id'),

                                                                DatePicker::make('date')
                                                                    ->label(__('Date'))
                                                                    ->required()
                                                                    ->default(now())
                                                                    ->columnSpan(1),

                                                                UsdToCurrencyInputs::make()
                                                                    ->countryId(function () use ($hotel) {
                                                                        return $hotel->getCountryId();
                                                                    })
                                                                    ->currencyRate(function ($component, $get) {
                                                                        $hotelBookingRoomStatePath = Str::before($component->getStatePath(), '.payments');

                                                                        return data_get($get($hotelBookingRoomStatePath, true), 'currency_rate_fixed');
                                                                    })
                                                                    ->usdInputName('price_usd')
                                                                    ->lariInputName('price_lari')
                                                                    ->required()
                                                                    ->columnSpan(2)
                                                                    ->reactive(),

                                                                Toggle::make('use_wallet')
                                                                    ->label(__('Use wallet'))
                                                            ])
                                                    ];
                                                })
                                        ];
                                    })
                            ];
                        }),

                ])->disabled(function () {
                    if (is_admin() || has_roles([User::ACCOUNTANT_HOTEL])) {;
                        return false;
                    }

                    return true;
                })
            ];
        });

        $this->mountUsing(function ($form) {
            $form->fill();
        });

        $this->visible(function () {
            return (is_admin() || has_roles([User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_BR, User::ACCOUNTANT_HOTEL_VIEW]));
        });

        $this->modalActions(function (Action $action, $record) {
            $actions = [
                (is_admin() || has_roles([User::ACCOUNTANT_HOTEL])) ? $action->getModalSubmitAction() : null,
                $action->getModalCancelAction(),
            ];
            return array_filter($actions);
        });

        $this->action(function ($record, $data) {
            $hotelBookings = data_get($data, 'hotelBookings');

            $bookings = collect($hotelBookings)->groupBy('hotel_booking_id')->toArray();

            $records = $record->hotelBookings;

            try {
                $sumPaymentAmount = collect($hotelBookings)->sum(function ($booking) {
                    return collect($booking['hotel_booking_rooms'])->sum(function ($room) {
                        return collect($room['payments'])->sum(function ($payment) {
                            return (double) $payment['price_usd'];
                        });
                    });
                });

                $sumRecordsAmount = HotelBooking::query()->whereIn('id', array_column($hotelBookings, 'hotel_booking_id'))->get()->sum('actual_cost_usd');

                if ($sumPaymentAmount > $sumRecordsAmount) {
                    Filament::notify('danger', __('The total paid amount is greater than booking cost.'));

                    return;
                }

                DB::beginTransaction();

                $records->each(function ($record) use ($bookings) {
                    $booking = data_get($bookings, "{$record->id}.0");

                    foreach ($booking['hotel_booking_rooms'] as $room) {
                        $hotelBookingRoom = HotelBookingRoom::query()->findOrFail(data_get($room, 'hotel_booking_room_id'));

                        $hotelPaymentIds = [];
                        foreach ($room['payments'] as $payment) {
                            if (data_get($payment, 'hotel_payment_id')) {
                                $hotelPayment = HotelPayment::query()->findOrFail($payment['hotel_payment_id']);
                                $hotelPayment->update([
                                    'hotel_booking_id' => $record->id,
                                    'amount' => data_get($payment, 'price_usd'),
                                    'amount_lari' => data_get($payment, 'price_lari'),
                                    'scheduled_at' => data_get($payment, 'date'),
                                    'is_scheduled' => Carbon::parse(data_get($payment, 'date'))->startOfDay()->isFuture(),
                                    'use_wallet' => data_get($payment, 'use_wallet'),
                                ]);
                                $hotelPaymentIds[] = $hotelPayment->id;
                            } else {
                                $hotelPayment = HotelPayment::query()->create([
                                    'hotel_booking_id' => $record->id,
                                    'hotel_booking_room_id' => $hotelBookingRoom->id,
                                    'amount' => data_get($payment, 'price_usd'),
                                    'amount_lari' => data_get($payment, 'price_lari'),
                                    'scheduled_at' => data_get($payment, 'date'),
                                    'is_scheduled' => Carbon::parse(data_get($payment, 'date'))->startOfDay()->isFuture(),
                                    'status' => PaymentStatus::PENDING,
                                    'user_id' => auth()->id(),
                                    'hotel_refund_id' => data_get($payment, 'hotel_refund_id'),
                                    'hotel_refund_transfer_id' => data_get($payment, 'hotel_refund_transfer_id'),
                                    'use_wallet' => data_get($payment, 'use_wallet'),
                                    'hotel_invoice_id' => $record->hotel_invoice_id,
                                ]);
                                $hotelPaymentIds[] = $hotelPayment->id;
                            }
                        }

                        $hotelBookingRoom->fresh()->hotelPayments()->whereNotIn('id', $hotelPaymentIds)->each(function ($payment) {;
                            $payment->forceDelete();
                        });
                    }
                });

                $record->touch();

                DB::commit();

                Filament::notify('success', __('Payments updated'));
            }catch (\Exception $e) {
                DB::rollBack();

                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());

                Filament::notify('danger', $e->getMessage());
            }
        });

    }

}
