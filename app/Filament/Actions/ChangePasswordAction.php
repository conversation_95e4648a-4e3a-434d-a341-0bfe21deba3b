<?php

namespace App\Filament\Actions;

use Filament\Facades\Filament;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;

class ChangePasswordAction extends Action
{

    public static function make(?string $name = 'change_password'): static
    {
        return parent::make($name);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->form([

            TextInput::make('password')
                ->label(__('Password'))
                ->password()
                ->minLength(6)
                ->maxLength(250)
                ->required(),

            TextInput::make('password_confirmation')
                ->label(__('Password Confirmation'))
                ->password()
                ->same('password')
                ->minLength(6)
                ->maxLength(250)
                ->required(),

        ])
            ->label(__('Change Password'))
            ->icon('heroicon-o-key')
            ->action(function ($data, $record) {
                $record->update([
                    'password' => data_get($data, 'password'),
                ]);
                Filament::notify('success', 'تم تغيير كلمة المرور بنجاح');
            });
    }

}
