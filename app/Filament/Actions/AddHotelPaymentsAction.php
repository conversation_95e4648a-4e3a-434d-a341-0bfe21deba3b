<?php

namespace App\Filament\Actions;

use App\Enums\PaymentStatus;
use App\Filament\Resources\CancelledHotelBookingResource\Pages\ListCancelledHotelBookings;
use App\Filament\Resources\ConfirmedHotelBookingResource\Pages\ListConfirmedHotelBookings;
use App\Forms\Components\UsdToCurrencyInputs;
use App\Models\CurrencyRate;
use App\Models\Hotel;
use App\Models\HotelBooking;
use App\Models\HotelBookingRoom;
use App\Models\HotelInvoice;
use App\Models\HotelPayment;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class AddHotelPaymentsAction extends BulkAction
{

    public static function make(?string $name = 'add_payments'): static
    {
        return parent::make($name);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Add payments'));

        $this->form(function ($records) {
            $hotelIds = $records->toQuery()->with('hotel')->get()->pluck('hotel.id')->unique()->toArray();

            $recordWithInvoice = $records->first(function ($record) {
                return $record->hotelInvoice &&
                    ! $record->hotelInvoice?->is_cancelled &&
                    $record->hotelInvoice?->status != PaymentStatus::REJECTED;
            });

            if ($recordWithInvoice) {
                return [
                    Placeholder::make('hotel_invoice_id')
                        ->label(__('Hotel Invoice ID'))
                        ->content(new HtmlString('<span style="color: red">' . __('One or more bookings already has an invoice.') . '</span>')),
                ];
            }

            if (count($hotelIds) > 1) {
                return [
                    Placeholder::make('hotel_ids')
                        ->label(__('Hotel IDs'))
                        ->content(new HtmlString('<span style="color: red">' . __('Please select bookings from the same hotel.') . '</span>')),
                ];
            }

            $hotel = Hotel::query()->findOrFail($hotelIds[0]);

            $recordWithUnAvailableRateDate = $records->map(function (HotelBooking $record) {
                $rate = $record->calculatedCurrencyRate(now());

                return data_get($rate, 'date');
            })->filter()->first();

            if ($recordWithUnAvailableRateDate) {
                return [
                    Placeholder::make('currency_rate')
                        ->label(__('Currency Rate'))
                        ->content(new HtmlString('<span style="color: red">' . __('Currency rate is not available for the selected date.') . ' (' . $recordWithUnAvailableRateDate . ')' . '</span>')),
                ];
            }

            $currencyRatesCount = $records->map(function (HotelBooking $record) {
                return data_get($record->calculatedCurrencyRate(now()), 'currency_rate', null);
            })->filter()->count();

            if ($currencyRatesCount != $records->count()) {
                return [
                    Placeholder::make('currency_rate')
                        ->label(__('Currency Rate'))
                        ->content(new HtmlString('<span style="color: red">' . __('Please add currency rates first') . '</span>')),
                ];
            }

            return [
                Grid::make(2)
                    ->schema([
                        TextInput::make('hotel_name')
                            ->label(__('Hotel Name'))
                            ->columnSpan(2)
                            ->default(function () use ($records) {
                                return $records->first()?->hotel?->name;
                            }),
                        TextInput::make('price_usd')
                            ->label(__('Cost USD'))
                            ->default(function () use ($records) {
                                return $records->sum('price_usd');
                            }),

                        TextInput::make('price_lari')
                            ->label(__('Cost Local Currency'))
                            ->default(function () use ($records) {
                                return $records->sum('price_lari');
                            }),
                    ])->disabled(),

                Repeater::make('hotelBookings')
                    ->model(HotelBooking::class)
                    ->label(__('Hotel Bookings'))
                    ->disableItemCreation()
                    ->disableItemDeletion()
                    ->afterStateHydrated(function ($component) use ($records) {
                        $state = $records->mapWithKeys(function ($record) {
                            $uuid1 = Str::uuid()->toString();

                            $countryId = $record->getCountryId();

                            $currencyRateFixed = data_get($record->calculatedCurrencyRate(now()), 'currency_rate', null);

                            $record->updateQuietly([
                                'currency_rate_fixed' => $currencyRateFixed,
                                'price_lari' => round($record->price_usd * $currencyRateFixed, 2),
                                'vat_lari' => round($record->vat_usd * $currencyRateFixed, 2),
                            ]);

                            return [
                                $uuid1 => [
                                    'hotel_booking_id' => $record->id,
                                    'currency_rate_fixed' => $currencyRateFixed,
                                    'country_id' => $countryId,
                                    'trip_code' => $record->reservation?->trip_code,
                                    'cost_usd' => $record->price_usd,
                                    'cost_lari' => $record->price_lari,
                                    'hotel_booking_rooms' => $record->hotelBookingRooms->mapWithKeys(function (HotelBookingRoom $room) use ($countryId, $currencyRateFixed) {
                                        $uuid1 = Str::uuid()->toString();
                                        $uuid2 = Str::uuid()->toString();

                                        $room->updateQuietly([
                                            'currency_rate_fixed' => $currencyRateFixed,
                                            'price_lari' => round($room->price_usd * $currencyRateFixed, 2),
                                        ]);

                                        $oldPayments = $room->hotelPayments->mapWithKeys(function (HotelPayment $payment) use ($countryId, $currencyRateFixed) {
                                            $uuid = Str::uuid()->toString();
                                            return [
                                                $uuid => [
                                                    'hotel_payment_id' => $payment->id,
                                                    'price_usd' => $payment->amount,
                                                    'price_lari' => $payment->amount_lari,
                                                    'currency_rate_fixed' => $currencyRateFixed,
                                                    'date' => $payment->scheduled_at,
                                                    'country_id' => $countryId,
                                                    'use_wallet' => $payment->use_wallet,
                                                ]
                                            ];
                                        })->toArray();
                                        return [
                                            $uuid1 => [
                                                'hotel_booking_room_id' => $room->id,
                                                'country_id' => $countryId,
                                                'room_type' => 'X(' . $room->count . ') ' . $room->roomType?->name,
                                                'price_usd' => $room->price_usd,
                                                'price_lari' => $room->price_lari,
                                                'currency_rate_fixed' => $currencyRateFixed,
                                                'payments' => count($oldPayments) ? $oldPayments : [
                                                    $uuid2 => [
                                                        'price_usd' => $room->price_usd,
                                                        'price_lari' => $room->price_lari,
                                                        'currency_rate_fixed' => $currencyRateFixed,
                                                        'date' => now()->toDateString(),
                                                        'country_id' => $countryId,
                                                        'use_wallet' => false,
                                                    ]
                                                ]
                                            ]
                                        ];
                                    })->toArray(),
                                ]
                            ];
                        })->toArray();

                        $records->each(function (HotelBooking $record)  {
                            $record->reservation?->updateCost();
                        });

                        $component->state($state);
                    })
                    ->registerListeners([
                        'repeater::deleteItem' => [
                            function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                if ($statePath !== $component->getStatePath()) {
                                    return;
                                }
                            },
                        ],
                    ])
                    ->schema(function () use ($hotel) {
                        return [
                            Hidden::make('hotel_booking_id'),

                            Grid::make(3)
                                ->schema([
                                    TextInput::make('trip_code')
                                        ->label(__('Trip code'))
                                        ->disabled(),

                                    TextInput::make('cost_usd')
                                        ->label(__('Cost USD'))
                                        ->disabled(),

                                    TextInput::make('cost_lari')
                                        ->disabled()
                                        ->label(__('Cost Local Currency')),
                                ]),

                            Repeater::make('hotel_booking_rooms')
                                ->model(HotelBookingRoom::class)
                                ->disableItemCreation()
                                ->disableItemDeletion()
                                ->label(__('Rooms'))
                                ->schema(function () use ($hotel) {
                                    return [
                                        Hidden::make('hotel_booking_room_id'),

                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('room_type')
                                                    ->label(__('Room Type'))
                                                    ->disabled()
                                                    ->columnSpan(1),

                                                UsdToCurrencyInputs::make()
                                                    ->countryId(function ($get) use ($hotel) {
                                                        return $hotel->getCountryId();
                                                    })
                                                    ->disabled()
                                                    ->usdInputName('price_usd')
                                                    ->lariInputName('price_lari')
                                                    ->columnSpan(2),
                                            ]),

                                        Repeater::make('payments')
                                            ->model(HotelPayment::class)
                                            ->label(__('Payments'))
                                            ->registerListeners([
                                                'repeater::deleteItem' => [
                                                    function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                                        if ($statePath !== $component->getStatePath()) {
                                                            return;
                                                        }
                                                    },
                                                ],
                                            ])
                                            ->schema(function () use ($hotel) {
                                                return [
                                                    Grid::make(3)
                                                        ->schema([
                                                            Hidden::make('hotel_payment_id'),

                                                            DatePicker::make('date')
                                                                ->label(__('Date'))
                                                                ->required()
                                                                ->default(now())
                                                                ->columnSpan(1),

                                                            UsdToCurrencyInputs::make()
                                                                ->countryId(function () use ($hotel) {
                                                                    return $hotel->getCountryId();
                                                                })
                                                                ->currencyRate(function ($component, $get) {
                                                                    $hotelBookingRoomStatePath = Str::before($component->getStatePath(), '.payments');

                                                                    return data_get($get($hotelBookingRoomStatePath, true), 'currency_rate_fixed');
                                                                })
                                                                ->usdInputName('price_usd')
                                                                ->lariInputName('price_lari')
                                                                ->required()
                                                                ->columnSpan(2)
                                                                ->reactive(),

                                                            Toggle::make('use_wallet')
                                                                ->label(__('Use wallet'))
                                                        ])
                                                ];
                                            })
                                    ];
                                })
                        ];
                    }),
            ];
        });

        $this->action(function ($records, $data, array $arguments = []) {
            $recordWithInvoice = $records->first(function ($record) {
                return $record->hotelInvoice &&
                    ! $record->hotelInvoice?->is_cancelled &&
                    $record->hotelInvoice?->status != PaymentStatus::REJECTED;
            });

            if ($recordWithInvoice) {
                Filament::notify('danger', __('One or more bookings already has an invoice.'));
                return;
            }

            if (data_get($arguments ?? [], 'cancel')) {
                $records->each->update([
                    'in_payment' => 0,
                ]);

                return ;
            }

            $hotelBookings = data_get($data, 'hotelBookings');

            if (!$hotelBookings || count($hotelBookings) === 0) {
                Filament::notify('danger', __('Please select bookings.'));
                return;
            }

            $bookings = collect($hotelBookings)->groupBy('hotel_booking_id')->toArray();

            try {
                DB::beginTransaction();

                /** @var HotelInvoice $hotelInvoice */
                $hotelInvoice = HotelInvoice::query()
                    ->create([
                        'user_id' => auth()->id(),
                        'hotel_id' => $records->first()->hotel_id,
                        'status' => PaymentStatus::PENDING,
                    ]);

                $sumPaymentAmount = collect($hotelBookings)->sum(function ($booking) {
                    return collect($booking['hotel_booking_rooms'])->sum(function ($room) {
                        return collect($room['payments'])->sum(function ($payment) {
                            return (double) $payment['price_usd'];
                        });
                    });
                });

                $sumRecordsAmount = HotelBooking::query()->whereIn('id', array_column($hotelBookings, 'hotel_booking_id'))->get()->sum('actual_cost_usd');

                if ($sumPaymentAmount > $sumRecordsAmount) {
                    Filament::notify('danger', __('The total paid amount is greater than booking cost.'));

                    return;
                }

                $records->each(function ($record) use ($bookings, $hotelInvoice) {
                    $booking = data_get($bookings, "{$record->id}.0");

                    foreach ($booking['hotel_booking_rooms'] as $room) {
                        $hotelBookingRoom = HotelBookingRoom::query()->findOrFail(data_get($room, 'hotel_booking_room_id'));

                        $record->update([
                            'hotel_invoice_id' => $hotelInvoice->id,
                        ]);

                        $hotelPaymentIds = [];
                        foreach ($room['payments'] as $payment) {
                            if (data_get($payment, 'hotel_payment_id')) {
                                $hotelPayment = HotelPayment::query()->findOrFail($payment['hotel_payment_id']);
                                $hotelPayment->update([
                                    'hotel_booking_id' => $record->id,
                                    'amount' => data_get($payment, 'price_usd'),
                                    'amount_lari' => data_get($payment, 'price_lari'),
                                    'scheduled_at' => data_get($payment, 'date'),
                                    'is_scheduled' => Carbon::parse(data_get($payment, 'date'))->startOfDay()->isFuture(),
                                    'hotel_invoice_id' => $record->hotel_invoice_id,
                                    'use_wallet' => data_get($payment, 'use_wallet'),
                                ]);
                                $hotelPaymentIds[] = $hotelPayment->id;
                            } else {
                                $hotelPayment = HotelPayment::query()->create([
                                    'hotel_booking_id' => $record->id,
                                    'hotel_booking_room_id' => $hotelBookingRoom->id,
                                    'amount' => data_get($payment, 'price_usd'),
                                    'amount_lari' => data_get($payment, 'price_lari'),
                                    'scheduled_at' => data_get($payment, 'date'),
                                    'is_scheduled' => Carbon::parse(data_get($payment, 'date'))->startOfDay()->isFuture(),
                                    'status' => PaymentStatus::PENDING,
                                    'user_id' => auth()->id(),
                                    'hotel_refund_id' => data_get($payment, 'hotel_refund_id'),
                                    'hotel_refund_transfer_id' => data_get($payment, 'hotel_refund_transfer_id'),
                                    'hotel_invoice_id' => $record->hotel_invoice_id,
                                    'use_wallet' => data_get($payment, 'use_wallet'),
                                ]);
                                $hotelPaymentIds[] = $hotelPayment->id;
                            }
                        }

                        $hotelBookingRoom->fresh()->hotelPayments()->whereNotIn('id', $hotelPaymentIds)->each(function ($payment) {;
                            $payment->forceDelete();
                        });
                    }
                });

                $hotelInvoice->touch();

                DB::commit();

                Filament::notify('success', __('Payments added'));
            }catch (\Exception $e) {
                DB::rollBack();

                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());

                Filament::notify('danger', $e->getMessage());
            }
        });

        $this->mountUsing(function ($records, $form) {
            $form->fill();

            $records->each->update([
                'in_payment' => 1,
            ]);
        });

        $this->visible(function ($livewire) {
            return ($livewire instanceof ListConfirmedHotelBookings) &&
                (is_admin() || has_roles([User::ACCOUNTANT_HOTEL]));
        });

        $this->modalActions(function (BulkAction $action) {
            return [
                $action->getModalSubmitAction(),
                ...$action->getExtraModalActions()
            ];
        });

        $this->extraModalActions(function (): array {
            return [
                $this->makeExtraModalAction('cancel', ['cancel' => true])
                    ->label(__('Cancel')),
            ];
        });
    }

}
