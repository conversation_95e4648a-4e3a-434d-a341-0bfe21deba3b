<?php

namespace App\Filament\Widgets;

use App\Enums\CommonPaymentType;
use App\Enums\PaymentStatus;
use App\Enums\SalesCostStatus;
use App\Models\CommonPayment;
use App\Models\Expense;
use App\Models\ExtraServiceBooking;
use App\Models\HotelBooking;
use App\Models\Payment;
use App\Models\Reservation;
use App\Models\TransportationCompanyBooking;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class RealProfitStats extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getColumns(): int
    {
        return 2;
    }

    public $startDate = null;

    public $endDate = null;

    public $branchId = null;

    public function mount()
    {
        $this->startDate = request()->get('startDate');

        $this->endDate = request()->get('endDate');

        $this->branchId = request()->get('branch_id');
    }

    protected function getCards(): array
    {
        // amount
        $totalAmount = Reservation::query()
            ->when($this->startDate, function ($query) {
                $query->where('arrival_date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {
                $query->where('arrival_date', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->where('branch_id', $this->branchId);
            })
            ->notCancelled()
            ->notReturned()
            ->sum('trip_price');

        $totalPaid = Payment::query()
            ->withoutGlobalScopes()
            ->when($this->startDate, function ($query) {
                $query->where('date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {
                $query->where('date', '<=', $this->endDate);
            })
            ->whereNull('deleted_at')
//            ->where('payments.payable_type', Reservation::class)
            ->where('payments.status', Payment::STATUS_CONFIRMED)
            ->whereHasMorph('payable', [Reservation::class], function ($query) {
                $query->notCancelled()
                    ->notReturned()
                    ->when($this->branchId, function ($query) {;
                        return $query->where('branch_id', $this->branchId);
                    });
            })
            ->where('payments.is_returned', false)
            ->sum('amount');

        $decorationCost = Reservation::query()
            ->where('sales_cost_status', SalesCostStatus::CONFIRMED)
            ->when($this->startDate, function ($query) {
                $query->where('arrival_date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {;
                $query->where('arrival_date', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->where('branch_id', $this->branchId);
            })
            ->sum('decoration_cost_usd');

        $hotelBookingCost = HotelBooking::query()
            ->where('status', HotelBooking::STATUS_CONFIRMED)
            ->when($this->startDate, function ($query) {
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '>=', $this->startDate);
                });
            })
            ->when($this->endDate, function ($query) {;
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '<=', $this->endDate);
                });
            })
            ->when($this->branchId, function ($query) {;
                $query->whereHas('reservation', function ($query) {
                    $query->where('branch_id', $this->branchId);
                });
            })
            ->sum('price_usd');

        $totalHotelCost = $hotelBookingCost + $decorationCost;

        $transportationCompanyBookings = TransportationCompanyBooking::query()
            ->where('status', TransportationCompanyBooking::STATUS_CONFIRMED)
            ->when($this->startDate, function ($query) {
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '>=', $this->startDate);
                });
            })
            ->when($this->endDate, function ($query) {;
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '<=', $this->endDate);
                });
            })
            ->when($this->branchId, function ($query) {
                return $query->whereHas('reservation', function ($query) {
                    $query->where('branch_id', $this->branchId);
                });
            })
            ->sum('price_usd');

        $extraServiceBookings = ExtraServiceBooking::query()
            ->whereHas('transportationCompanyBooking', function ($query) {
                $query->where('status', TransportationCompanyBooking::STATUS_CONFIRMED);
            })
            ->when($this->startDate, function ($query) {
                $query->whereHas('transportationCompanyBooking.reservation', function ($query) {
                    $query->where('arrival_date', '>=', $this->startDate);
                });
            })
            ->when($this->endDate, function ($query) {
                $query->whereHas('transportationCompanyBooking.reservation', function ($query) {
                    $query->where('arrival_date', '<=', $this->endDate);
                });
            })
            ->when($this->branchId, function ($query) {
                return $query->whereHas('transportationCompanyBooking.reservation', function ($query) {
                    $query->where('branch_id', $this->branchId);
                });
            })
            ->sum('price_usd');

        $totalTransportationCost = $transportationCompanyBookings + $extraServiceBookings;

        $salesBonus = CommonPayment::query()
            ->where('type', CommonPaymentType::SALES_BONUS_PAYMENT)
            ->when($this->startDate, function ($query) {
                $query->where('date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {;
                $query->where('date', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->whereHasMorph('payable', [Reservation::class], function ($query) {
                    $query->where('branch_id', $this->branchId);
                });
            })
            ->where('status', PaymentStatus::APPROVED)
            ->paid()
            ->sum('amount');

        $totalExpenses = Expense::query()
            ->whereHas('expenseCommonPayment', function ($query) {
                $query
                    ->where('status', PaymentStatus::APPROVED)
                    ->paid();
            })
            ->when($this->startDate, function ($query) {
                $query->where('date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {
                $query->where('date', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->where('branch_id', $this->branchId);
            })
            ->sum('amount');

        return [
            Card::make(__('Real Full Profit'), $totalAmount - ($totalHotelCost + $totalTransportationCost + $salesBonus + $totalExpenses))
                ->color('success'),

            Card::make(__('Real Paid Profit'), $totalPaid - ($totalHotelCost + $totalTransportationCost + $salesBonus + $totalExpenses))
                ->color('success'),
        ];
    }
}
