<?php

namespace App\Filament\Widgets;

use App\Enums\PaymentStatus;
use App\Enums\SalesCostStatus;
use App\Models\Branch;
use App\Models\ExtraServiceBooking;
use App\Models\HotelBooking;
use App\Models\HotelPayment;
use App\Models\Reservation;
use App\Models\TransportationCompanyBooking;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class HotelBookingStats extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected int | string | array $columnSpan = 'full';

    protected function getColumns(): int
    {
        return 3;
    }

    public static function canView(): bool
    {
        return is_admin() || has_roles([User::ACCOUNTANT_BR]);
    }


    protected function getCards(): array
    {
        $hotelBookingCost = HotelBooking::query()
            ->where('status', HotelBooking::STATUS_CONFIRMED)
            ->sum('price_usd');

        $hotelBookingPayments = HotelPayment::query()
            ->where('status', PaymentStatus::APPROVED)
            ->where('is_cancelled', 0)
            ->where('is_refund', 0)
            ->sum('amount');

        $remainingHotelBookingCost = $hotelBookingCost - $hotelBookingPayments;

        return [
            Card::make(__('Hotel Booking Cost'), $hotelBookingCost),

            Card::make(__('Hotel Booking Payments'), $hotelBookingPayments),

            Card::make(__('Remaining'), $remainingHotelBookingCost)
        ];
    }
}
