<?php

namespace App\Filament\Widgets;

use App\Enums\SalesCostStatus;
use App\Models\Branch;
use App\Models\ExtraServiceBooking;
use App\Models\HotelBooking;
use App\Models\Reservation;
use App\Models\TransportationCompanyBooking;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class HotelCostStats extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getColumns(): int
    {
        return 2;
    }

    public $startDate = null;

    public $endDate = null;

    public $branchId = null;

    public function mount()
    {
        $this->startDate = request()->get('startDate');

        $this->endDate = request()->get('endDate');

        $this->branchId = request()->get('branch_id');
    }

    protected function getCards(): array
    {
        $decorationCost = Reservation::query()
            ->where('sales_cost_status', SalesCostStatus::CONFIRMED)
            ->when($this->startDate, function ($query) {
                $query->where('arrival_date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {;
                $query->where('arrival_date', '<=', $this->endDate);
            })
            ->when($this->branchId, function ($query) {
                return $query->where('branch_id', $this->branchId);
            })
            ->sum('decoration_cost_usd');

        $hotelBookingCost = HotelBooking::query()
            ->where('status', HotelBooking::STATUS_CONFIRMED)
            ->when($this->startDate, function ($query) {
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '>=', $this->startDate);
                });
            })
            ->when($this->endDate, function ($query) {;
                $query->whereHas('reservation', function ($query) {
                    $query->where('arrival_date', '<=', $this->endDate);
                });
            })
            ->when($this->branchId, function ($query) {;
                $query->whereHas('reservation', function ($query) {
                    $query->where('branch_id', $this->branchId);
                });
            })
            ->sum('price_usd');


        return [
            Card::make(__('Hotel Cost'), $hotelBookingCost),

            Card::make(__('Decoration Cost'), $decorationCost),
        ];
    }
}
