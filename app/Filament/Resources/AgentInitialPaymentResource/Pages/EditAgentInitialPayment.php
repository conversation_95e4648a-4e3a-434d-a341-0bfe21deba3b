<?php

namespace App\Filament\Resources\AgentInitialPaymentResource\Pages;

use App\Filament\Resources\AgentInitialPaymentResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAgentInitialPayment extends EditRecord
{
    protected static string $resource = AgentInitialPaymentResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
