<?php

namespace App\Filament\Resources\B2bAccountingResource\Pages;

use App\Filament\Resources\B2bAccountingResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditB2bAccounting extends EditRecord
{
    protected static string $resource = B2bAccountingResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function getFormActions(): array
    {
        return [];
    }
}
