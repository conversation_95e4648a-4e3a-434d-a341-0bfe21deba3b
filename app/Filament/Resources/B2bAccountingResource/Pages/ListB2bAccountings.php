<?php

namespace App\Filament\Resources\B2bAccountingResource\Pages;

use App\Filament\Resources\B2bAccountingResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListB2bAccountings extends ListRecords
{
    protected static string $resource = B2bAccountingResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
