<?php

namespace App\Filament\Resources\FlightReportResource\Pages;

use App\Filament\Resources\FlightReportResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Collection;

class ListFlightReports extends ListRecords
{
    protected static string $resource = FlightReportResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
