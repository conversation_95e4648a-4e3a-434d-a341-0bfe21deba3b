<?php

namespace App\Filament\Resources\DoneB2cOrdersResource\Pages;

use App\Filament\Resources\DoneB2cOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDoneB2cOrders extends ListRecords
{
    protected static string $resource = DoneB2cOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
