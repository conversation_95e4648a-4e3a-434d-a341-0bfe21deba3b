<?php

namespace App\Filament\Resources\B2BReservationResource\Pages;

use App\Filament\Resources\B2BReservationResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditB2BReservation extends EditRecord
{
    protected static string $resource = B2BReservationResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
