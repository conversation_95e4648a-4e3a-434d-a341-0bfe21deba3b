<?php

namespace App\Filament\Resources\B2BReservationResource\Pages;

use App\Filament\Resources\B2BReservationResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListB2BReservations extends ListRecords
{
    protected static string $resource = B2BReservationResource::class;

    public $selectedIds = [];

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function setSelectedIds($ids)
    {
        $this->selectedIds = $ids;
    }
}
