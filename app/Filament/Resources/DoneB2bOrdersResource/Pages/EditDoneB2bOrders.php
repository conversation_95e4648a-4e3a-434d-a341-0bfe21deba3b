<?php

namespace App\Filament\Resources\DoneB2bOrdersResource\Pages;

use App\Filament\Resources\DoneB2bOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDoneB2bOrders extends EditRecord
{
    protected static string $resource = DoneB2bOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
