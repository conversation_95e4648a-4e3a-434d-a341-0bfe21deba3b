<?php

namespace App\Filament\Resources\DoneB2bOrdersResource\Pages;

use App\Filament\Resources\DoneB2bOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDoneB2bOrders extends ListRecords
{
    protected static string $resource = DoneB2bOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
