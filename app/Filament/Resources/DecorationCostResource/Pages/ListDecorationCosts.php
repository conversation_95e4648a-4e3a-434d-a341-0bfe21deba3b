<?php

namespace App\Filament\Resources\DecorationCostResource\Pages;

use App\Filament\Resources\DecorationCostResource;
use App\Filament\Widgets\DecorationCostStats;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDecorationCosts extends ListRecords
{
    protected static string $resource = DecorationCostResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DecorationCostStats::class,
        ];
    }

    public function updatedTableFilters(): void
    {
        parent::updatedTableFilters();

        $this->emit('tableFiltersUpdated', $this->tableFilters);
    }
}
