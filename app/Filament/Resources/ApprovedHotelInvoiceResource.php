<?php

namespace App\Filament\Resources;

use App\Enums\PaymentStatus;
use App\Filament\Concerns\SortNavigation;
use App\Filament\Resources\ApprovedHotelInvoiceResource\Pages;
use App\Models\HotelInvoice;
use App\Models\User;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ApprovedHotelInvoiceResource extends Resource
{
    use SortNavigation;

    protected static ?string $model = HotelInvoice::class;

    protected static ?string $navigationIcon = 'fas-file';

    public static function getLabel(): ?string
    {
        return __('Approved Hotel Invoice');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Approved Hotel Invoices');
    }

    protected static function getNavigationGroup(): ?string
    {
        return __('Hotel Bookings');
    }

    protected static ?string $slug = 'approved-hotel-invoices';

    protected static function getNavigationBadge(): ?string
    {
        return HotelInvoice::query()
            ->whereNotNull('send_to_approve_at')
            ->where('status', PaymentStatus::APPROVED)
            ->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return PendingHotelInvoiceResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApprovedHotelInvoices::route('/'),
            'create' => Pages\CreateApprovedHotelInvoice::route('/create'),
            'edit' => Pages\EditApprovedHotelInvoice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereNotNull('send_to_approve_at')
            ->where('status', PaymentStatus::APPROVED);
    }

    public static function canViewAny(): bool
    {
        return PendingHotelInvoiceResource::canViewAny();
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
