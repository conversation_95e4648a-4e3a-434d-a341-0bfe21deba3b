<?php

namespace App\Filament\Resources;

use App\Enums\SalesCostStatus;
use App\Exports\DriverExcel;
use App\Filament\Concerns\SortNavigation;
use App\Filament\Resources\AllReservationCostResource\Pages\EditAllReservationCost;
use App\Filament\Resources\ConfirmedReseravtionCostResource\Pages\ListConfirmedReseravtionCosts;
use App\Filament\Resources\HotelVatResource\Pages\ListHotelVats;
use App\Filament\Resources\RejectedReservationCostResource\Pages\ListRejectedReservationCosts;
use App\Filament\Resources\TourCostResource\Pages;
use App\Forms\Components\HotelBookingsRepeater;
use App\Forms\Components\UsdToCurrencyInputs;
use App\Models\Country;
use App\Models\DriverTour;
use App\Models\ExtraServiceBooking;
use App\Models\Hotel;
use App\Models\HotelBooking;
use App\Models\HotelBookingRoom;
use App\Models\InsuranceOption;
use App\Models\Reservation;
use App\Models\RoomType;
use App\Models\TransportationCompany;
use App\Models\TransportationCompanyBooking;
use App\Models\TransportationCompanyBookingCar;
use App\Models\TransportationCompanyBookingTransfer;
use App\Models\User;
use App\Models\Visa;
use App\Models\VisaOption;
use Carbon\Carbon;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Resources\Form;
use App\Filament\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class TourCostResource extends Resource
{
    use SortNavigation;

    protected static ?string $model = Reservation::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    protected static ?string $slug = 'tour-costs';

    public static function getLabel(): ?string
    {
        return __('Tour Cost');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Tour Costs');
    }

    protected static function getNavigationGroup(): ?string
    {
        return __('Tour Costs');
    }

    public static function form(Form $form): Form
    {

        return $form
            ->columns(1)
            ->schema([
                Group::make([
                    TourCostResource::basicInfoCard(),

                    TourCostResource::insuranceCard(),

                    TourCostResource::insuranceDetailsCard(),

                    TourCostResource::tourInfoCard(),

                    TourCostResource::hotelBookingsCard(),

                    TourCostResource::transportationCompanyBookingsCard(),

                    TourCostResource::visaCard(),

                    TourCostResource::ticketsCard(),

                    TourCostResource::decorationCard(),

                    Card::make([
                        Toggle::make('no_driver')
                            ->label(__('No Driver'))
                            ->reactive()
                            ->afterStateUpdated(function ($set) {
                                $set('booking_transportation_user_id', null);
                                $set('car_type_id', null);
                            })
                            ->default(false),

                        Group::make([
                            Select::make('booking_transportation_user_id')
                                ->label(__('Booking Transportation User'))
                                ->relationship('bookingTransportationUser', 'name', function ($query, $get) {
                                    $arrivalCountryId = $get('data.arrival_country_id', true);
                                    return $query->hasRoles([User::ROLE_TRANSPORTATION_COMPANY])
                                        ->whereIn('id', function ($query) use ($arrivalCountryId) {
                                            $query->select('transportation_companies.user_id')
                                                ->from('transportation_companies')
                                                ->where('transportation_companies.country_id', $arrivalCountryId);
                                        });
                                })
                                ->preload()
                                ->requiredWith('car_type_id')
                                ->searchable(),

                            Select::make('car_type_id')
                                ->label(__('Car Type'))
                                ->requiredWith('booking_transportation_user_id')
                                ->relationship('carType', 'name'),
                        ])
                            ->disabled(function ($record) {
                                return $record->transportationCompanyBooking?->transportationCompanyPayments?->filter(function ($payment) {
                                    return $payment->send_to_approve;
                                })->isNotEmpty();
                            })
                            ->visible(function ($get) {
                            return ! $get('no_driver');
                        })

                    ])->visible(function () {
                        return is_admin() || has_roles([User::BOOKING_HOTEL, User::TRANSPORTATION_AGENT]);
                    }),

                    Card::make([

                        TextInput::make('total_price_usd')
                            ->label(fn($record) => __('Cost') . ' ' . $record->getForeignCurrency()),

                        TextInput::make('total_price_lari')
                            ->label(fn($record) => __('Cost') . ' ' . $record->getLocalCurrency()),

                    ])
                        ->visible(function () {
                            return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                        })
                        ->disabled(),

                    Textarea::make('notes')
                        ->label(__('Notes'))
                        ->rows(3)
                        ->visible(function () {
                            return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                        }),

                    FileUpload::make('cost_attachments')
                        ->label(__('Attachments'))
                        ->enableDownload()
                        ->multiple(),

                ])->disabled(function ($record) {
                        if (is_admin() || has_roles([User::BOOKING_HOTEL, User::ROLE_TRANSPORTATION_COMPANY, User::TRANSPORTATION_AGENT])) return false;
                        else return true;
                    })
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('#')
                    ->searchable()
                    ->visible(fn() => is_admin())
                    ->sortable(),

                Tables\Columns\TextColumn::make('trip_code')
                    ->label(__('Trip Code'))
                    ->searchable(),

                TextColumn::make('sales_cost_reject_reason')
                    ->label(__('Reject Reason'))
                    ->visible(function ($livewire) {
                        return $livewire instanceof ListRejectedReservationCosts;
                    }),

                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('Sales'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('bookingHotelUser.name')
                    ->label(__('Hotel Reservation Manager'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('bookingTransportationUser.name')
                    ->label(__('Transportation Company')),

                Tables\Columns\TextColumn::make('arrival_date')
                    ->label(__('Arrival Date'))
                    ->dateTime('d.m.Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('departure_date')
                    ->label(__('Departure Date'))
                    ->dateTime('d.m.Y H:i')
                    ->sortable(),

                Tables\Columns\BooleanColumn::make('is_active')
                    ->label(__('Is Active'))
                    ->getStateUsing(function ($record) {
                        return ! $record->is_cancelled;
                    }),

                Tables\Columns\TextColumn::make('b2c_b2b')
                    ->label(__('B2C/B2B'))
                    ->getStateUsing(function ($record) {
                        return $record->company_id ? 'B2B' : 'B2C';
                    }),

                Tables\Columns\TextColumn::make('hotel_bookings_sum_price_usd')
                    ->label(fn($record) => __('Hotel Cost') . ' ' . $record->getForeignCurrency())
                    ->sum('hotelBookings', 'price_usd')
                    ->sortable()
                    ->visible(function ($record) {
                        return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                    }),

                Tables\Columns\TextColumn::make('hotel_bookings_sum_price_lari')
                    ->label(fn($record) => __('Hotel Cost') . ' ' . $record->getLocalCurrency())
                    ->sum('hotelBookings', 'price_lari')
                    ->sortable()
                    ->visible(function ($record) {
                        return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                    }),

                Tables\Columns\TextColumn::make('branch.name')
                    ->label(__('Branch'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('customer_name')
                    ->label(__('Customer'))
                    ->searchable()
                    ->visible(function () {
                        return !has_roles([User::BOOKING_HOTEL, User::ROLE_TRANSPORTATION_COMPANY, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION]);
                    }),

                Tables\Columns\TextColumn::make('customer_phone')
                    ->label(__('Customer Phone'))
                    ->getStateUsing(function ($record) {
                        if (has_roles(User::ROLE_SALES) && $record->user_id != auth()->id()) {
                            return '';
                        }

                        return $record->customer_phone;
                    })
                    ->searchable()
                    ->visible(function () {
                        return is_admin() || has_roles([User::ROLE_SALES]);
                    }),

//                Tables\Columns\TextColumn::make('transportation_company_bookings_sum_price_usd')
//                    ->label(__('Transportation Company Cost USD'))
//                    ->sum('transportationCompanyBookings', 'price_usd')
//                    ->sortable(),

                Tables\Columns\TextColumn::make('transportation_company_bookings_sum_price_lari')
                    ->label(__('Transportation Company Cost'))
                    ->sum('transportationCompanyBookings', 'price_lari')
                    ->sortable(),

                Tables\Columns\TextColumn::make('trip_price')
                    ->label(__('Trip price'))
                    ->visible(function () {
                        return is_admin() || has_roles([User::ROLE_SALES]);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_paid')
                    ->label(__('Total paid'))
                    ->visible(function () {
                        return is_admin() || has_roles([User::ROLE_SALES]);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_price_usd')
                    ->label(fn($record) => __('Total Cost') . ' ' . $record->getForeignCurrency())
                    ->visible(function ($record) {
                        return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_price_lari')
                    ->label(__('Total Cost Local Currency'))
                    ->visible(function ($record) {
                        return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                    })
                    ->sortable(),

                TextColumn::make('total_insurance_cost')
                    ->label(__('Total Insurance Cost'))
                    ->visible(function ($record) {
                        return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION]);
                    }),

                Tables\Columns\TextColumn::make('profit')
                    ->label(__('Paid Profit'))
                    ->visible(function () {
                        return is_admin() || has_roles([User::ROLE_SALES]);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('full_profit')
                    ->label(__('Full profit'))
                    ->visible(function () {
                        return is_admin() || has_roles([User::ROLE_SALES]);
                    })
                    ->sortable(),

                Tables\Columns\BooleanColumn::make('driver_excel_done')
                    ->getStateUsing(function ($record) {
                        return !! $record->driver_excel;
                    }),

                Tables\Columns\BooleanColumn::make('sent_to_sales')
                    ->getStateUsing(function ($record) {
                        return $record->sales_cost_status != SalesCostStatus::NEW &&
                            $record->sales_cost_status != null;
                    }),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Tables\Filters\TernaryFilter::make('has_hotel_bookings')
                    ->label(__('Has Hotel Bookings'))
                    ->queries(
                        true: fn (Builder $query) => $query->has('hotelBookings'),
                        false: fn (Builder $query) => $query->doesntHave('hotelBookings'),
                        blank: fn (Builder $query) => $query,
                    ),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label(__('Is Active'))
                    ->queries(
                        true: fn (Builder $query) => $query->whereNull('cancelled_at'),
                        false: fn (Builder $query) => $query->whereNotNull('cancelled_at'),
                        blank: fn (Builder $query) => $query,
                    ),

                Tables\Filters\TernaryFilter::make('has_transportation_company_bookings')
                    ->label(__('Has Transportation Company Bookings'))
                    ->queries(
                        true: fn (Builder $query) => $query->has('transportationCompanyBookings'),
                        false: fn (Builder $query) => $query->doesntHave('transportationCompanyBookings'),
                        blank: fn (Builder $query) => $query,
                    ),

                Tables\Filters\TrashedFilter::make(),

                Tables\Filters\SelectFilter::make('branch_id')
                    ->label(__('Branch'))
                    ->relationship('branch', 'name')
                    ->searchable()
                    ->visible(fn() => is_admin() || has_roles(User::ROLE_SALES)),

                Tables\Filters\SelectFilter::make('country_id')
                    ->label(__('Arrival Country'))
                    ->relationship('arrivalCountry', 'name', function (Builder $query) {
                        $query->arrival();
                    })
                    ->multiple()
                    ->searchable(),

                Tables\Filters\Filter::make('arrival_date')
                    ->label(__('Arrival Date'))
                    ->form([
                        DatePicker::make('arrival_date_from')
                            ->label(__('Arrival date from')),
                        DatePicker::make('arrival_date_until')
                            ->label(__('Arrival date to')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['arrival_date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('arrival_date', '>=', $date),
                            )
                            ->when(
                                $data['arrival_date_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('arrival_date', '<=', $date),
                            );
                    }),

                Tables\Filters\SelectFilter::make('company_id')
                    ->label(__('Company'))
                    ->relationship('company', 'name')
                    ->visible(function () {
                        return is_admin() || has_roles(User::ROLE_SALES);
                    })
                    ->searchable(),

                Tables\Filters\SelectFilter::make('sales')
                    ->label(__('Sales'))
                    ->relationship('user', 'name', function (Builder $query) {
                        $query->where('roles', 'like', '%"'.User::ROLE_SALES.'"%');
                    })
                    ->searchable()
                    ->optionsLimit(300),

                Tables\Filters\SelectFilter::make('booking_hotel_user_id')
                    ->label(__('Hotel Reservation Manager'))
                    ->relationship('bookingHotelUser', 'name', function (Builder $query) {
                        $query->where('roles', 'like', '%"'.User::BOOKING_HOTEL.'"%');
                    })
                    ->searchable()
                    ->optionsLimit(300),

                Tables\Filters\SelectFilter::make('booking_transportation_user_id')
                    ->label(__('Booking Transportation Employee'))
                    ->relationship('bookingTransportationUser', 'name', function (Builder $query) {
                        $query->where('roles', 'like', '%"'.User::ROLE_TRANSPORTATION_COMPANY.'"%');
                    })
                    ->searchable()
                    ->optionsLimit(300),

                Tables\Filters\SelectFilter::make('transportation_company_id')
                    ->label(__('Transportation Company'))
                    ->options(function () {
                        return TransportationCompany::query()
                            ->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->query(function ($query, $data) {
                        if (count($data['values'])) {
                            $query->whereHas('transportationCompanyBookings', function ($query) use ($data) {
                                $query->whereIn('transportation_company_id', $data['values']);
                            });
                        }
                    }),

                Tables\Filters\TernaryFilter::make('has_bookingTransportationUser')
                    ->label(__('Has Transportation Company'))
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('booking_transportation_user_id'),
                        false: fn (Builder $query) => $query->whereNull('booking_transportation_user_id'),
                        blank: fn (Builder $query) => $query,
                    )
            ])
            ->actions([
                Tables\Actions\Action::make('banner')
                    ->label(__('Banner'))
                    ->icon('heroicon-o-document-text')
                    ->openUrlInNewTab()
                    ->url(function ($record) {
                        return route('banner', $record->id);
                    }),

                Tables\Actions\Action::make('voucher')
                    ->visible(function ($livewire) {
                        return $livewire instanceof ListConfirmedReseravtionCosts &&
                            (is_admin() || has_roles(User::ROLE_SALES));
                    })
                    ->form([
                        Select::make('brand')
                            ->label(__('Brand'))
                            ->options(fn() => User::BRANDS)
                            ->required()
                            ->default(auth()->user()->brand),
                    ])
                    ->color('danger')
                    ->icon('heroicon-o-document-text')
                    ->action(function ($record, $data) {
                        return redirect(route('voucher.voucher', [$record->id, 'brand' => $data['brand']]));
                    }),

                Tables\Actions\Action::make('invoice')
                    ->visible(function ($livewire) {
                        return $livewire instanceof ListConfirmedReseravtionCosts &&
                            (is_admin() || has_roles(User::ROLE_SALES));
                    })
                    ->form([
                        Select::make('brand')
                            ->label(__('Brand'))
                            ->options(fn() => User::BRANDS)
                            ->required()
                            ->default(auth()->user()->brand),
                    ])
                    ->color('primary')
                    ->icon('heroicon-o-document-text')
                    ->action(function ($record, $data) {
                        return redirect(route('voucher.invoice', [$record->id, 'brand' => $data['brand']]));
                    }),

                Tables\Actions\Action::make('driver_excel')
                    ->label(__('Driver Excel'))
                    ->icon('heroicon-o-document-download')
                    ->visible(function () {
                        return  (
                            is_admin() || has_roles([User::ROLE_SALES, User::ACCOUNTANT_TRANSPORTATION, User::BOOKING_HOTEL])
                        );
                    })
                    ->form(function (Model|Reservation $record) {
                        $hotelBookings = $record->hotelBookings->sortBy(function ($hotelBooking) {
                            return $hotelBooking->check_in;
                        });

                        $lastCheckout = $hotelBookings->sortByDesc('check_out')->first()?->check_out;

                        $days = [];

                        foreach ($hotelBookings as $hotelBooking) {
                            $checkIn = Carbon::parse($hotelBooking->check_in);
                            $checkOut = Carbon::parse($hotelBooking->check_out);

                            $isLastOne = $hotelBooking->check_out->equalTo($lastCheckout);

                            $condition = $isLastOne ? 'lessThanOrEqualTo' : 'lessThan';

                            while ($checkIn->{$condition}($checkOut)) {
                                $days[] = [
                                    'date' => $checkIn->format('Y-m-d'),
                                    'hotel' => $hotelBooking->hotel?->name,
                                    'driver_tour_id' => null,
                                    'driver_tour_name' => null,
                                    'comment' => null,
                                    'comment_en' => null,
                                ];

                                $checkIn->addDay();
                            }
                        }

                        $savedDays = $record->driver_excel ?? [];

                        $days = $savedDays && count($savedDays) ?
                            collect($savedDays)->sortBy('date')
                                ->map(function ($day) {
                                    if (!data_get($day, 'comment_en')) {
                                        $day['comment_en'] = DriverTour::query()->find(data_get($day, 'driver_tour_id'))?->notes_en;
                                    }

                                    return $day;
                                })
                                ->toArray() :
                            $days;

                        return [
                            Repeater::make('tours')
                                ->label(__('Tours'))
                                ->afterStateHydrated(function ($set) use ($days) {
                                    $set('tours', collect($days)->mapWithKeys(function ($day) {
                                        $uuid = Str::uuid()->toString();
                                        return [
                                            $uuid => $day,
                                        ];
                                    })->toArray());
                                })
                                ->columns(2)
                                ->disableItemMovement()
                                ->schema([
                                    TextInput::make('hotel')
                                        ->label(__('Hotel')),

                                    DatePicker::make('date')
                                        ->label(__('Date')),

                                    Select::make('driver_tour_id')
                                        ->label(__('Tour'))
                                        ->options(function () use ($record) {
                                            return DriverTour::query()
                                                ->where('country_id', $record->arrival_country_id)
                                                ->pluck('name', 'id')
                                                ->toArray();
                                        })
                                        ->reactive()
                                        ->afterStateUpdated(function ($get, $set) {
                                            if (!$get('driver_tour_id')) {
                                                $set('comment', null);

                                                return;
                                            }

                                            $driverTour = DriverTour::query()->find($get('driver_tour_id'));

                                            $set('comment', $driverTour->notes);
                                            $set('comment_en', $driverTour->notes_en);

                                            $set('driver_tour_name', $driverTour->name);
                                        }),

                                    Hidden::make('driver_tour_name'),

                                    Textarea::make('comment')
                                        ->label(__('Comment'))
                                        ->rows(3),

                                    Textarea::make('comment_en')
                                        ->label(__('Comment (English)'))
                                        ->rows(3),
                                ]),
                        ];
                    })
                    ->action(function ($data, $record) {
                        $tours = data_get($data, 'tours', []);

                        $tours = array_map(function ($tour) {
                            return [
                                'date' => data_get($tour, 'date'),
                                'hotel' => data_get($tour, 'hotel'),
                                'driver_tour_id' => data_get($tour, 'driver_tour_id'),
                                'driver_tour_name' => data_get($tour, 'driver_tour_name'),
                                'comment' => data_get($tour, 'comment'),
                                'comment_en' => data_get($tour, 'comment_en'),
                            ];
                        }, $tours);

                        $record->update([
                            'driver_excel' => $tours,
                        ]);

                        $tours = array_map(function ($tour) {
                            unset($tour['driver_tour_id']);

                            return $tour;
                        }, $tours);

                        if (count($tours)) {
                            return Excel::download(new DriverExcel($tours, $record), $record->trip_code.'.xlsx');
                        }

                        return false;
                    }),

                Tables\Actions\Action::make('download_driver_excel')
                    ->icon('heroicon-o-download')
                    ->visible(function ($record) {
                        return $record->driver_excel && is_array($record->driver_excel);
                    })
                    ->url(function (Model $record) {
                        return route('driver-pdf', ['reservationId' => $record->id]);
                    })
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('download_driver_excel_english')
                    ->label(__('Driver Driver Excel (English)'))
                    ->icon('heroicon-o-download')
                    ->color('warning')
                    ->visible(function ($record) {
                        return $record->driver_excel;
                    })
                    ->url(function (Model $record) {
                        return route('driver-pdf', ['reservationId' => $record->id, 'english' => '1']);
                    })
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('send_to_sales')
                    ->label(__('Send To Sales'))
                    ->color('danger')
                    ->icon('heroicon-o-arrow-circle-right')
                    ->requiresConfirmation()
                    ->visible(function ($record) {
                        return (is_admin() || has_roles([User::BOOKING_HOTEL, User::BOOKING_TRANSPORTATION]));
                    })
                    ->action(function (Model|Reservation $record) {
                        $record->updateSalesCostStatus([
                            'status' => SalesCostStatus::PENDING,
                        ]);
                    }),

                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                ExportBulkAction::make('export')
                    ->label(__('Export')),

                Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\ForceDeleteBulkAction::make(),
                Tables\Actions\RestoreBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTourCosts::route('/'),
            'create' => Pages\CreateTourCost::route('/create'),
            'edit' => Pages\EditTourCost::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->notCancelled()
            ->with(['branch']);
    }

    public static function basicInfoCard()
    {
        return Card::make([
            Grid::make(3)
                ->schema([
                    TextInput::make('customer_name')
                        ->label(__('Customer Name'))
                        ->visible(function () {
                            return is_admin() || has_roles(User::ROLE_SALES);
                        }),

                    Select::make('company_id')
                        ->label(__('Company'))
                        ->relationship('company', 'name')
                        ->preload()
                        ->visible(function () {
                            return is_admin() || has_roles(User::ROLE_SALES);
                        }),

                    Select::make('user_id')
                        ->label(__('Sales'))
                        ->relationship('user', 'name'),

                    TextInput::make('customer_phone')
                        ->label(__('Customer Phone'))
                        ->visible(function () {
                            return is_admin() || has_roles([User::ROLE_SALES, User::ROLE_TRANSPORTATION_COMPANY, User::TRANSPORTATION_AGENT]);
                        }),

                    TextInput::make('trip_code')
                        ->label(__('Trip Code'))
                        ->required(),

                    Select::make('branch_id')
                        ->label(__('Branch'))
                        ->relationship('branch', 'name'),

                    Select::make('country_from_id')
                        ->label(__('Departure Country'))
                        ->relationship('countryFrom', 'name', fn($query) => $query->countryFrom()),

                    Select::make('number_of_people')
                        ->label(__('Number Of People'))
                        ->reactive()
                        ->afterStateUpdated(function ($state, $get, $set) {
                            $currentPeople = $get('people');
                            $newCount = (int) $state;

                            if ($newCount === 0) {
                                $set('people', []);
                                return;
                            }

                            $currentCount = count($currentPeople);

                            if ($newCount > $currentCount) {
                                for ($i = 0; $i < ($newCount - $currentCount); $i++) {
                                    $currentPeople[Str::uuid()->toString()] = [
                                        'name' => null,
                                        'passport_number' => null,
                                        'birth_date' => null,
                                        'country_name' => null,
                                    ];
                                }
                            } elseif ($newCount < $currentCount) {
                                $currentPeople = array_slice($currentPeople, 0, $newCount, true);
                            }

                            $set('people', $currentPeople);
                        })
                        ->options(function () {
                            return collect(range(1, 20))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                        }),

                    Select::make('number_of_kids_under_6')
                        ->label(__('Number Of Kids Under 6'))
                        ->reactive()
                        ->afterStateUpdated(function ($state, $get, $set) {
                            $currentKids = $get('kidsUnder6');
                            $newCount = (int) $state;

                            if ($newCount === 0) {
                                $set('kidsUnder6', []);
                                return;
                            }

                            $currentCount = count($currentKids);

                            if ($newCount > $currentCount) {
                                for ($i = 0; $i < ($newCount - $currentCount); $i++) {
                                    $currentKids[Str::uuid()->toString()] = [
                                        'name' => null,
                                        'passport_number' => null,
                                        'birth_date' => null,
                                        'country_name' => null,
                                    ];
                                }
                            } elseif ($newCount < $currentCount) {
                                $currentKids = array_slice($currentKids, 0, $newCount, true);
                            }

                            $set('kidsUnder6', $currentKids);
                        })
                        ->options(function () {
                            return collect(range(0, 20))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                        }),


                    Select::make('number_of_days')
                        ->label(__('Number Of Nights'))

                        ->options(function () {
                            // range 1, 20
                            return collect(range(1, 20))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                        }),

                    DateTimePicker::make('arrival_date')
                        ->label(__('Arrival Date')),

                    Group::make([
                        TextInput::make('total_price')
                            ->label(__('Total Price'))
                            ->disabled()
                            ->afterStateHydrated(function ($component, $record) {
                                $component->state($record?->total_price);
                            }),

                        TextInput::make('total_paid')
                            ->label(__('Total Paid'))
                            ->disabled()
                            ->afterStateHydrated(function ($component, $record) {
                                $component->state($record?->total_paid);
                            }),

                        TextInput::make('total_remaining')
                            ->label(__('Total Remaining'))
                            ->disabled()
                            ->afterStateHydrated(function ($component, $record) {
                                $component->state($record?->total_remaining);
                            }),
                    ])->visible(function () {
                        return is_admin() && has_roles([User::ROLE_SALES]);
                    })
                ])
                ->dehydrated(false)
        ])
            ->disabled()
            ->visible(function () {
                return is_admin() || has_roles([User::ROLE_SALES, User::ROLE_TRANSPORTATION_COMPANY, User::TRANSPORTATION_AGENT, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION, User::ACCOUNTANT_BR]);
            });
    }

    public static function tourInfoCard()
    {
        return Card::make([

            Grid::make([
                'default' => 1,
                'md' => 2,
                'lg' => 3,
            ])
                ->schema([
                    Select::make('arrival_country_id')
                        ->label(__('Arrival Country'))
                        ->relationship('arrivalCountry', 'name')
                        ->preload()
                        ->searchable()
                        ->optionsLimit(300)
                        ->columnSpan([
                            'default' => 1,
                            'md' => 2,
                            'lg' => 3,
                        ])
                        ->reactive()
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->disabled()
                        ->afterStateUpdated(function ($set) {
                            $set('arrival_airport_id', null);
                        }),

                    Select::make('arrival_airport_id')
                        ->label(__('Arrival Airport'))
                        ->relationship('arrivalAirport', 'name', function ($query, $get) {
                            return $query->where('country_id', $get('arrival_country_id'));
                        })
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->columnSpan(1)
                        ->preload(),

                    TextInput::make('arrival_flight_number')
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->label(__('Arrival Flight Number')),

                    DateTimePicker::make('arrival_date')
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->label(__('Arrival Date')),

                    Select::make('departure_airport_id')
                        ->label(__('Departure Airport'))
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->relationship('departureAirport', 'name', function ($query, $record, $get) {
                            return $query->where('country_id', $get('arrival_country_id'));
                        })
                        ->preload(),

                    TextInput::make('departure_flight_number')
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->label(__('Departure Flight Number')),

                    DateTimePicker::make('departure_date')
                        ->required(function ($livewire) {
                            return $livewire instanceof EditAllReservationCost;
                        })
                        ->label(__('Departure Date')),
                ]),

            Toggle::make('tourist_visit_office')
                ->label(__('Tourist Visit Office')),

        ])
            ->visible(function () {
                return is_admin() || has_roles([User::ROLE_SALES, User::ROLE_TRANSPORTATION_COMPANY, User::TRANSPORTATION_AGENT, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION, User::ACCOUNTANT_BR]);
            });
    }

    public static function hotelBookingsCard()
    {

        return Card::make([

            HotelBookingsRepeater::make('hotelBookings')
                ->label(__('Hotel Bookings'))
                ->relationship('hotelBookings')
//                ->registerListeners([
//                    'repeater::deleteItem' => [
//                        function (Repeater $component, string $statePath, string $uuidToDelete): void {
//                            if ($statePath !== $component->getStatePath()) {
//                                return;
//                            }
//
//                            $component->evaluate(function ($get, $set) {
//                                TourCostResource::updateCost($get, $set);
//                            });
//                        },
//                    ],
//                ])
                ->schema([

                    Group::make()
                        ->schema(function () {
                            return [
                                Hidden::make('country_id')
                                    ->default(function ($get) {
                                        return $get('data.arrival_country_id', true);
                                    })
                                    ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                        $set('country_id', $get('data.arrival_country_id', true));
                                    }),

                                Grid::make(2)
                                    ->schema([
                                        DatePicker::make('check_in')
                                            ->label(__('Check In Date'))
                                            ->required()
                                            ->reactive()
                                            ->before('check_out')
                                            ->default(function ($get) {
                                                return $get('data.arrival_date', true);
                                            })
                                            ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                                TourCostResource::calculateHotelPrice($get, $set, $livewire, $component);
                                                TourCostResource::setHotelBookingRooms($get, $set, $livewire, $component);
                                            }),

                                        DatePicker::make('check_out')
                                            ->label(__('Check Out Date'))
                                            ->required()
                                            ->reactive()
                                            ->after('check_in')
                                            ->default(function ($get) {
                                                return $get('data.departure_date', true);
                                            })
                                            ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                                TourCostResource::calculateHotelPrice($get, $set, $livewire, $component);
                                                TourCostResource::setHotelBookingRooms($get, $set, $livewire, $component);
                                            }),
                                    ]),

                                Grid::make(3)
                                    ->schema([

                                        Select::make('city_id')
                                            ->label(__('City'))
                                            ->relationship('city', 'name', function ($query, $get) {
                                                return $query->where('country_id', $get('data.arrival_country_id', true));
                                            })
                                            ->preload()
                                            ->reactive()
                                            ->afterStateUpdated(function ($set) {
                                                $set('hotel_id', null);
                                            })
                                            ->searchable(),

                                        Select::make('hotel_id')
                                            ->label(__('Hotel'))
                                            ->relationship('hotel', 'name', function ($query, $get) {
                                                return $query->where('city_id', $get('city_id') ?? $get('data.arrival_country_id', true));
                                            })
                                            ->preload()
                                            ->reactive()
                                            ->searchable()
                                            ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                                TourCostResource::calculateHotelPrice($get, $set, $livewire, $component);
                                                TourCostResource::setHotelBookingRooms($get, $set, $livewire, $component);
                                            }),

                                        Select::make('room_count')
                                            ->label(__('Room Count'))
                                            ->options(function () {
                                                return collect(range(1, 20))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                                            })
                                            ->reactive()
                                            ->required()
                                            ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                                TourCostResource::calculateHotelPrice($get, $set, $livewire, $component);
                                                TourCostResource::setHotelBookingRooms($get, $set, $livewire, $component);
                                            }),
                                    ]),

                                Repeater::make('hotelBookingRooms')
//                                    ->disableItemDeletion()
                                    ->visible(function ($get) {
                                        return $get('hotel_id') && $get('check_in') && $get('check_out') && $get('room_count');
                                    })
                                    ->afterStateHydrated(function ($set, $get, $record) {
                                        if ($record->hotelBookingRooms->isEmpty()) {
                                            $set('hotelBookingRooms', []);
                                        }
                                    })
                                    ->registerListeners([
                                        'repeater::deleteItem' => [
                                            function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                                if ($statePath !== $component->getStatePath()) {
                                                    return;
                                                }

                                                $count = count($component->getState());

                                                $component->evaluate(function ($get, $set) use ($count) {
                                                    TourCostResource::calculateHotelPrice($get, $set);

                                                    $set('room_count', $count);
                                                });
                                            },
                                        ],
                                    ])
                                    ->label(__('Rooms'))
                                    ->disableItemCreation()
                                    ->disableItemMovement()
                                    ->relationship('hotelBookingRooms')
                                    ->schema(function ($get, $set) {
                                        $rowGet = $get;
                                        $rowSet = $set;

                                        return [
                                            Grid::make([
                                                'default' => 1,
                                                'md' => 2,
                                                'lg' => 4,
                                            ])
                                                ->schema([

                                                    Select::make('room_type_id')
                                                        ->label(__('Room Type'))
                                                        ->relationship('roomType', 'name')
                                                        ->preload()
                                                        ->required()
                                                        ->reactive()
                                                        ->columnSpan([
                                                            'default' => 2,
                                                            'md' => 1,
                                                            'lg' => 1,
                                                        ])
                                                        ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                            TourCostResource::calculateHotelPrice($rowGet, $rowSet);
                                                        }),

                                                    Select::make('count')
                                                        ->label(__('Count'))
                                                        ->reactive()
                                                        ->required()
                                                        ->default(1)
                                                        ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                            TourCostResource::calculateHotelPrice($rowGet, $rowSet);
                                                        })
                                                        ->options(function () {
                                                            return collect(range(1, 30))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                                                        }),

                                                    UsdToCurrencyInputs::make()
                                                        ->countryId(function ($get) {
                                                            return $get('data.arrival_country_id', true);
                                                        })
                                                        ->lazy()
                                                        ->columnSpan(2)
                                                        ->usdInputName('price_usd')
                                                        ->lariInputName('price_lari')
                                                        ->disabled(function ($get) {
                                                            $roomType = RoomType::query()->find($get('room_type_id'));

                                                            if ($roomType) {
                                                                return $roomType?->handle != 'another';
                                                            }

                                                            return true;
                                                        })
                                                        ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                            TourCostResource::calculateHotelPrice($rowGet, $rowSet);
                                                        }),

                                                    TextInput::make('room_name')
                                                        ->label(__('Room Name'))
                                                        ->visible(function ($get) {
                                                            $roomType = RoomType::query()->find($get('room_type_id'));

                                                            return $roomType?->handle == 'another';
                                                        })
                                                ]),
                                        ];
                                    }),

                                Grid::make(2)
                                    ->schema([

                                        TextInput::make('price_usd')
                                            ->label(__('Price USD'))
                                            ->disabled()
                                            ->required(),

                                        TextInput::make('price_lari')
                                            ->label(__('Price Local Currency'))
                                            ->disabled()
                                            ->required(),

                                        Hidden::make('vat_usd')
                                            ->disabled()
                                            ->label(__('Vat USD')),

                                        Hidden::make('vat_lari')
                                            ->disabled()
                                            ->label(__('Vat Local Currency')),

                                    ]),

                                Textarea::make('notes')
                                    ->label(__('Notes'))
                                    ->rows(3),
                            ];
                        })
                        ->disabled(function ($record) {
                            if ($record && ($record->hotelPayments->count() || $record->in_payment) && $record->reservation?->hotel_cost_status == SalesCostStatus::CONFIRMED) {
                                return true;
                            }

                            return false;
                        }),
                ])

        ])
            ->visible(function () {
                return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION, User::ACCOUNTANT_BR]);
            });
    }

    public static function transportationCompanyBookingsCard()
    {
        return Card::make([

            Repeater::make('transportationCompanyBookings')
                ->label(__('Transportation Company Bookings'))
                ->collapsible()
                ->relationship('transportationCompanyBookings')
                ->disableItemDeletion(function () {
                    if (has_roles([User::TRANSPORTATION_AGENT])) {
                        return false;
                    }
                    return true;
                })
                ->disableItemCreation(function ($state) {
                    if (has_roles([User::TRANSPORTATION_AGENT])) {
                        return false;
                    }
                    return count($state) == 1 || (!has_roles([User::ROLE_TRANSPORTATION_COMPANY]) && !is_admin());
                })
                ->registerListeners([
                    'repeater::deleteItem' => [
                        function (Repeater $component, string $statePath, string $uuidToDelete): void {
                            if ($statePath !== $component->getStatePath()) {
                                return;
                            }

                            $component->evaluate(function ($get, $set) {
                                TourCostResource::updateCost($get, $set);
                            });
                        },
                    ],
                ])
                ->schema([
                    Hidden::make('country_id')
                        ->default(function ($get) {
                            return $get('data.arrival_country_id', true);
                        })
                        ->afterStateHydrated(function ($set, $get) {
                            $set('country_id', $get('data.arrival_country_id', true));
                        }),

                    Grid::make(2)
                        ->schema([
                            DateTimePicker::make('from_date')
                                ->label(__('From Date'))
                                ->required()
                                ->reactive()
                                ->beforeOrEqual('to_date')
                                ->extraAttributes(function ($record) {
                                    return [
                                        'data-item-id' => $record?->id,
                                    ];
                                })
                                ->default(function ($get) {
                                    return $get('data.arrival_date', true);
                                })
                                ->afterStateUpdated(function ($get, $set, $livewire, $component){
                                    TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                    TourCostResource::setTransportationCompanyBookingCars($get, $set, $livewire, $component);
                                }),

                            DateTimePicker::make('to_date')
                                ->label(__('To Date'))
                                ->required()
                                ->reactive()
                                ->afterOrEqual('from_date')
                                ->default(function ($get) {
                                    return $get('data.departure_date', true);
                                })
                                ->afterStateUpdated(function ($get, $set, $livewire, $component){
                                    TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                    TourCostResource::setTransportationCompanyBookingCars($get, $set, $livewire, $component);
                                }),
                        ]),

                    Grid::make(2)
                        ->schema([

                            Select::make('transportation_company_id')
                                ->label(__('Transportation Company'))
                                ->relationship('transportationCompany', 'name', function ($query, $get) {
                                    return $query->where('country_id', $get('data.arrival_country_id', true));
                                })
                                ->preload()
                                ->required()
                                ->reactive()
                                ->afterStateHydrated(function ($get, $set, $livewire, $component){
                                    $reservation = $livewire->record;

                                    if (has_roles([User::TRANSPORTATION_AGENT])) {
                                        return false;
                                    }

                                    if ($reservation && $reservation->booking_transportation_user_id) {
                                        $set('transportation_company_id', TransportationCompany::query()->firstWhere('user_id', $reservation->booking_transportation_user_id)?->id);
                                    }
                                })
                                ->afterStateUpdated(function ($get, $set, Component $livewire, Select $component) {
                                    $set('car_type_id', $get('data.car_type_id', true));
                                    TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                    TourCostResource::setTransportationCompanyBookingCars($get, $set, $livewire, $component);
                                }),

                            Select::make('car_type_id')
                                ->label(__('Car Type'))
                                ->disabled(function () {
                                    return !has_roles([User::TRANSPORTATION_AGENT]);
                                })
                                ->options(function ($get) {
                                    return TransportationCompany::query()
                                        ->find($get('transportation_company_id'))
                                        ?->transportationCompanyPrices
                                        ->pluck('carType.name', 'car_type_id');
                                })
                                ->preload()
                                ->required()
                                ->reactive()
                                ->afterStateHydrated(function ($get, $set, Component $livewire, Select $component) {
                                    /** @var Reservation $reservation */
                                    $reservation = $livewire->record;

                                    if (has_roles([User::TRANSPORTATION_AGENT])) {
                                        return false;
                                    }

                                    if ($reservation && $reservation->car_type_id) {
                                        $set('car_type_id', $reservation->car_type_id);
                                    }
                                })
                                ->afterStateUpdated(function ($get, $set, $livewire, $component){
                                    TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                    TourCostResource::setTransportationCompanyBookingCars($get, $set, $livewire, $component);
                                }),
                        ]),

                    Grid::make(1)
                        ->schema([
                            Select::make('transportation_company_car_id')
                                ->label(__('Driver'))
                                ->reactive()
                                ->searchable()
                                ->afterStateUpdated(function ($get, $set, Component $livewire, Field $component) {
                                    $transportationCompanyCars = $get('transportationCompanyBookingCars');


                                    if ($transportationCompanyCars && count($transportationCompanyCars)) {
                                        $dates = collect(array_column($transportationCompanyCars, 'date'))
                                            ->map(function ($date) {
                                                return Carbon::parse($date)->format('Y-m-d');
                                            })
                                            ->unique()
                                            ->toArray();

                                        $transportationCompanyCarId = $get('transportation_company_car_id');

                                        $booking = TransportationCompanyBooking::query()
                                            ->where('transportation_company_car_id', $transportationCompanyCarId)
                                            ->whereHas('transportationCompanyBookingCars', function ($query) use ($dates) {
                                                $query->whereIn('date', $dates);
                                            })
                                            ->first();

                                        if ($booking) {
                                            $livewire->addError($component->getStatePath(), __('This driver is already booked for this code: ' . $booking->reservation?->trip_code));

                                            $set('transportation_company_car_id', null);
                                        }else{
                                            $livewire->resetErrorBag($component->getStatePath());
                                        }

                                    }else{
                                        $livewire->resetErrorBag($component->getStatePath());
                                    }
                                })
                                ->options(function ($get) {
                                    /** @var TransportationCompany $transportationCompany */
                                    $transportationCompany = TransportationCompany::query()
                                        ->find($get('transportation_company_id'));

                                    if ($transportationCompany) {
                                        return $transportationCompany->transportationCompanyCars
                                            ->where('is_active', true)
                                            ->mapWithKeys(function ($car) {;
                                                return [$car->id => $car->driver_name . ' ' . "({$car->driver_phone})"];
                                            });
                                    }

                                    return [];
                                }),

                            TextInput::make('driver_name')
                                ->label(__('Driver Name'))
                                ->requiredWith('driver_phone'),

                            TextInput::make('driver_phone')
                                ->label(__('Driver Phone'))
                                ->requiredWith('driver_name'),

                            Textarea::make('driver_comment')
                                ->rows(2)
                                ->label(__('Driver Comment')),

                        ])
                        ->hidden(function () {
                            return has_roles([User::ROLE_SALES]);
                        })
                        ->disabled(function ($get) {
                            return !$get('transportation_company_id');
                        }),

                    Toggle::make('add_days')
                        ->default(false)
                        ->label(function ($get, $set, $state) {
                            $transportationCompanyBookingCars = $get('transportationCompanyBookingCars');
                            return $transportationCompanyBookingCars && count($transportationCompanyBookingCars) ? __('Remove Days') : __('Add Days Automatically');
                        })
                        ->reactive()
                        ->afterStateUpdated(function ($get, $set, $livewire, $component, $state){
                            if ($state) {
                                TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                TourCostResource::setTransportationCompanyBookingCars($get, $set, $livewire, $component);
                            }else{
                                $set('transportationCompanyBookingCars', []);
                                TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                            }
                        }),

                    Card::make([
                        Repeater::make('transportationCompanyBookingCars')
                            ->label(__('Cars'))
                            ->relationship('transportationCompanyBookingCars')
                            ->afterStateHydrated(function ($record, $set) {
                                if (!$record) {
                                    $set('transportationCompanyBookingCars', []);
                                }
                            })
                            ->itemLabel(function ($component, $state) {
                                $items = array_values($component->getState());
                                if ($items && count($items)) {
                                    $state = json_encode($state);

                                    $indexOfState = collect($items)
                                        ->search(function ($item) use ($state) {
                                            return json_encode($item) === $state;
                                        });

                                    if ($indexOfState >= 0) {
                                        return "Day " . ($indexOfState + 1);
                                    }
                                }

                                return '';
                            })
                            ->registerListeners([
                                'repeater::deleteItem' => [
                                    function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                        if ($statePath !== $component->getStatePath()) {
                                            return;
                                        }

                                        $component->evaluate(function ($get, $set)  {
                                            TourCostResource::calculateTransportationCompanyPrice($get, $set);
                                        });
                                    },
                                ],
                            ])
                            ->schema(function ($get, $set) {
                                $rowGet = $get;
                                $rowSet = $set;

                                return [
                                    Grid::make(4)
                                        ->schema([

                                            Select::make('car_type_id')
                                                ->label(__('Car Type'))
                                                ->options(function ($get) use ($rowGet) {
                                                    return TransportationCompany::query()
                                                        ->find($rowGet('transportation_company_id'))
                                                        ?->transportationCompanyPrices
                                                        ->pluck('carType.name', 'car_type_id');
                                                })
                                                ->extraAttributes(function ($record) {
                                                    return [
                                                        'data-item-id' => $record?->id,
                                                    ];
                                                })
                                                ->preload()
                                                ->required()
                                                ->reactive()
                                                ->default(function () use ($rowGet) {
                                                    return $rowGet('car_type_id');
                                                })
                                                ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                }),

                                            DatePicker::make('date')
                                                ->label(__('Date'))
                                                ->required(),

                                            TextInput::make('price_lari')
                                                ->label(__('Price'))
                                                ->lazy()
                                                ->numeric()
                                                ->minValue(0)
                                                ->afterStateUpdated(function ($set) use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);

                                                    $set('price_usd', null);
                                                }),

                                                Toggle::make('half_day')
                                                    ->label(__('Half Day'))
                                                    ->reactive()
                                                    ->afterStateUpdated(function ($set, $get, $state) use ($rowGet, $rowSet) {
                                                        $priceLari = $get('price_lari');
                                                        if ($priceLari && $priceLari > 0 && $state) {
                                                            $set('price_lari', $priceLari / 2);
                                                        }

                                                        if ($priceLari && $priceLari > 0 && !$state) {
                                                            $set('price_lari', $priceLari * 2);
                                                        }

                                                        TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                    })
                                                    ->default(false),

                                                Textarea::make('comment')
                                                    ->columnSpan([
                                                        'default' => 1,
                                                        'md' => 2,
                                                        'lg' => 2,
                                                    ])
                                                    ->rows(2)
                                                    ->label(__('Comment')),

//                                            TextInput::make('price_usd')
//                                                ->label(__('Price USD'))
//                                                ->lazy()
//                                                ->numeric()
//                                                ->minValue(0)
//                                                ->afterStateUpdated(function ($set) use ($rowGet, $rowSet) {
//                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
//
//                                                    $set('price_lari', null);
//                                                }),

                                        ]),
                                ];
                            })
                    ]),

                    Card::make([
                        Repeater::make('extraServiceBookings')
                            ->label(__('Extra Service Bookings'))
                            ->relationship('extraServiceBookings')
                            ->registerListeners([
                                'repeater::deleteItem' => [
                                    function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                        if ($statePath !== $component->getStatePath()) {
                                            return;
                                        }

                                        $component->evaluate(function ($get, $set)  {
                                            TourCostResource::calculateTransportationCompanyPrice($get, $set);
                                        });
                                    },
                                ],
                            ])
                            ->schema(function ($get, $set) {
                                $rowGet = $get;
                                $rowSet = $set;

                                return [
                                    Grid::make([
                                        'default' => 1,
                                        'md' => 2,
                                        'lg' => 4,
                                    ])
                                        ->schema([

                                            Select::make('extra_service_id')
                                                ->label(__('Extra Service'))
                                                ->relationship('extraService', 'name', function ($query) use ($rowGet) {
                                                    if (!$rowGet('transportation_company_id')) {
                                                        return $query->where('id', 0);
                                                    }
                                                    return $query->whereIn('id', function ($query) use ($rowGet) {
                                                        $query->select('extra_service_id')
                                                            ->from('transportation_company_extra_services')
                                                            ->where('transportation_company_id', $rowGet('transportation_company_id'));
                                                    });
                                                })
                                                ->preload()
                                                ->required()
                                                ->reactive()
                                                ->columnSpan(1)
                                                ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                }),

                                            Select::make('count')
                                                ->label(__('Count'))
                                                ->default(1)
                                                ->options(function () {
                                                    return collect(range(1, 20))->mapWithKeys(fn($number) => [$number => $number])->toArray();
                                                })
                                                ->required()
                                                ->reactive()
                                                ->columnSpan(1)
                                                ->afterStateUpdated(function ()  use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                }),

                                            UsdToCurrencyInputs::make()
                                                ->columnSpan([
                                                    'default' => 1,
                                                    'md' => 2,
                                                    'lg' => 2,
                                                ])
                                                ->disabled()
//                                                ->lazy()
//                                                ->afterStateUpdated(function ()  use ($rowGet, $rowSet) {
//                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
//                                                })
                                                ->lariInputName('price_lari')
                                                ->lariInputPlaceholder('Price'),

                                            Textarea::make('comment')
                                                ->columnSpan([
                                                    'default' => 1,
                                                    'md' => 2,
                                                    'lg' => 2,
                                                ])
                                                ->rows(2)
                                                ->label(__('Comment')),
                                        ]),
                                ];
                            })
                    ]),

                    Card::make([
                        Repeater::make('transportationCompanyBookingTransfers')
                            ->label(__('Transfers'))
                            ->relationship('transportationCompanyBookingTransfers')
                            ->registerListeners([
                                'repeater::deleteItem' => [
                                    function (Repeater $component, string $statePath, string $uuidToDelete): void {
                                        if ($statePath !== $component->getStatePath()) {
                                            return;
                                        }

                                        $component->evaluate(function ($get, $set)  {
                                            TourCostResource::calculateTransportationCompanyPrice($get, $set);
                                        });
                                    },
                                ],
                            ])
                            ->schema(function ($get, $set) {
                                $rowGet = $get;
                                $rowSet = $set;

                                return [
                                    Grid::make(4)
                                        ->schema([

                                            Select::make('transportation_company_transfer_id')
                                                ->label(__('Transfer'))
                                                ->relationship('transportationCompanyTransfer', 'description', function ($query) use ($rowGet) {
                                                    if (!$rowGet('transportation_company_id')) {
                                                        return $query->where('id', 0);
                                                    }
                                                    return $query->where('transportation_company_id', $rowGet('transportation_company_id'));
                                                })
                                                ->preload()
                                                ->required()
                                                ->reactive()
                                                ->columnSpan(1)
                                                ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                }),

                                            TextInput::make('price_lari')
                                                ->label(__('Price'))
                                                ->lazy()
                                                ->numeric()
                                                ->minValue(0)
                                                ->afterStateUpdated(function () use ($rowGet, $rowSet) {
                                                    TourCostResource::calculateTransportationCompanyPrice($rowGet, $rowSet);
                                                }),

                                            Textarea::make('comment')
                                                ->columnSpan([
                                                    'default' => 1,
                                                    'md' => 2,
                                                    'lg' => 2,
                                                ])
                                                ->rows(2)
                                                ->label(__('Comment')),
                                        ]),
                                ];
                            })
                    ]),

                    Grid::make(2)
                        ->schema([
                            TextInput::make('price_lari')
                                ->label(__('Price'))
                                ->disabled(),

//                            TextInput::make('price_usd')
//                                ->label(__('Price USD'))
//                                ->disabled(),

                            TextInput::make('bonus_lari')
                                ->label(__('Bonus'))
                                ->lazy()
                                ->afterStateUpdated(function ($get, $set, $livewire, $component) {
                                    TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
                                }),

                            TextInput::make('days')
                                ->label('Days')
                                ->disabled()
                                ->dehydrated(false)
                                ->afterStateHydrated(function ($get, $set) {
                                    $transportationCompanyBookingCars = $get('transportationCompanyBookingCars');
                                    $count = $transportationCompanyBookingCars && count($transportationCompanyBookingCars) ?
                                        collect($transportationCompanyBookingCars)->sum(function ($item) {
                                            return data_get($item, 'half_day') ? 0.5 : 1;
                                        }) :
                                        0;
                                    $set('days', $count);
                                }),


//                            TextInput::make('bonus_usd')
//                                ->label(__('Bonus USD'))
//                                ->disabled(),

                            Hidden::make('vat_lari')
                                ->label(__('Vat'))
                                ->disabled(),

//                            TextInput::make('vat_usd')
//                                ->label(__('Vat USD'))
//                                ->disabled(),

//                            Hidden::make('extra_services_cost_usd'),

                            Hidden::make('extra_services_cost_lari'),

                        ]),

//                    UsdToCurrencyInputs::make()
//                        ->countryId(function ($get) {
//                            return $get('data.arrival_country_id', true);
//                        })
//                        ->usdInputName('bonus_per_day_usd')
//                        ->lariInputName('bonus_per_day_lari')
//                        ->required(false),

                    Select::make('payment_type')
                        ->label(__('Payment Type'))
                        ->options([
                            100 => __('100%'),
                            50 => __('50%'),
                        ])
                        ->reactive()
                        ->disabled(function ($record) {
                            if ($record) {
                                return $record->transportationCompanyPayments->filter(function ($payment) {;
                                    return $payment->send_to_approve;
                                })->isNotEmpty();
                            }

                            return false;
                        })
                        ->afterStateUpdated(function ($get, $set) {
                            $paymentType = $get('payment_type');
                            $priceLari = $get('price_lari');

                            if ($paymentType == 100 && $priceLari) {
                                $set('total_cost_lari', $get('price_lari'));
                            } elseif ($paymentType == 50 && $priceLari) {
                                $set('total_cost_lari', $get('price_lari') / 2);
                            } else {
                                $set('total_cost_lari', null);
                            }
                        })
                        ->afterStateHydrated(function ($get, $set) {
                            $transportationCompany = TransportationCompany::query()
                                ->find($get('transportation_company_id'));

                            if ($transportationCompany && $transportationCompany->payment_type) {
                                $set('payment_type', $transportationCompany->payment_type);
                            } else {
                                $set('payment_type', 100);
                            }
                        })
                        ->required(),

                    TextInput::make('total_cost_lari')
                        ->label(__('Total Cost'))
                        ->dehydrated(false)
                        ->afterStateHydrated(function ($get, $set) {
                            $paymentType = $get('payment_type');
                            $priceLari = $get('price_lari');

                            if ($paymentType == 100 && $priceLari) {
                                $set('total_cost_lari', $get('price_lari'));
                            } elseif ($paymentType == 50 && $priceLari) {
                                $set('total_cost_lari', $get('price_lari') / 2);
                            } else {
                                $set('total_cost_lari', null);
                            }
                        })
                        ->disabled(),
                ])

        ])->visible(function () {
            return is_admin() || has_roles([User::ROLE_TRANSPORTATION_COMPANY, User::ROLE_SALES, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION, User::TRANSPORTATION_MANAGER, User::ACCOUNTANT_BR, User::TRANSPORTATION_AGENT]);
        })
        ->disabled(function () {
            if (is_admin() || has_roles([User::ROLE_TRANSPORTATION_COMPANY]) || has_roles([User::TRANSPORTATION_AGENT])) {
                return false;
            }

            return true;
        });
    }

    public static function visaCard()
    {
        return Card::make([

            Toggle::make('has_visa')
                ->label(__('Has Visa'))
                ->reactive()
                ->afterStateHydrated(function ($record, $set) {
                    if ($record?->visa) {
                        $set('has_visa', true);
                    }
                }),

            Group::make([
                Placeholder::make('visa')
                    ->label(__('Visa')),

                TextInput::make('number_of_people')
                    ->label(__('Number of people'))
                    ->required(),

                TextInput::make('number_of_children')
                    ->label(__('Number of children')),

                FileUpload::make('passport_images')
                    ->label(__('Passport images'))
                    ->multiple(),

                FileUpload::make('people_images')
                    ->label(__('People images'))
                    ->multiple(),

                Textarea::make('notes')
                    ->label(__('Notes')),
            ])->visible(function ($get) {
                return !! $get('has_visa');
            })->relationship('visa')

        ])->visible(function ($get) {
            $arrivalCountryId = $get('data.arrival_country_id', true);

            return (is_admin() || has_roles([User::BOOKING_HOTEL, User::ACCOUNTANT_BR])) &&
                !! VisaOption::query()->firstWhere('country_id', $arrivalCountryId);
        });
    }

    public static function ticketsCard()
    {
        return Card::make([
            Placeholder::make('tickets')
                ->label(__('Tickets')),

            Repeater::make('tickets')
                ->label(__('Tickets'))
                ->relationship('tickets')
                ->visible(function () {
                    return is_admin() || has_roles([User::BOOKING_HOTEL]);
                })
                ->schema([

                    TextInput::make('number_of_people')
                        ->label(__('Number of people'))
                        ->default(function ($get) {
                            return $get('data.number_of_people', true);
                        }),

                    TextInput::make('number_of_children')
                        ->label(__('Number of children'))
                        ->default(function ($get) {
                            return $get('data.number_of_children', true);
                        }),

                    Select::make('transport_type')
                        ->label(__('Transport Type'))
                        ->reactive()
                        ->afterStateUpdated(function ($set) {
                            $set('from_bus_station_id', null);
                            $set('to_bus_station_id', null);
                            $set('from_airport_id', null);
                            $set('to_airport_id', null);
                        })
                        ->options([
                            'bus' => __('Bus'),
                            'plane' => __('Plane'),
                        ]),

                    Group::make([
                        Select::make('from_bus_station_id')
                            ->label(__('From Bus Station'))
                            ->relationship('fromBusStation', 'name', function ($query, $get) {
                                $countryId = $get('data.arrival_country_id', true);
                                return $query->where('country_id', $countryId);
                            })
                            ->required()
                            ->preload(),

                        Select::make('to_bus_station_id')
                            ->label(__('To Bus Station'))
                            ->relationship('toBusStation', 'name', function ($query, $get) {
                                $countryId = $get('data.arrival_country_id', true);
                                return $query->where('country_id', $countryId);
                            })
                            ->required()
                            ->preload(),
                    ])->visible(function ($get) {
                        return $get('transport_type') == 'bus';
                    }),

                    Group::make([
                        Select::make('from_airport_id')
                            ->label(__('From Airport'))
                            ->relationship('fromAirport', 'name', function ($query, $get) {
                                $countryId = $get('data.arrival_country_id', true);
                                return $query->where('country_id', $countryId);
                            })
                            ->required()
                            ->preload(),

                        Select::make('to_airport_id')
                            ->label(__('To Airport'))
                            ->relationship('toAirport', 'name', function ($query, $get) {
                                $countryId = $get('data.arrival_country_id', true);
                                return $query->where('country_id', $countryId);
                            })
                            ->required()
                            ->preload(),
                    ])->visible(function ($get) {
                        return $get('transport_type') == 'plane';
                    }),

                    DateTimePicker::make('arrival_date')
                        ->label(__('Arrival Date'))
                        ->required(),

                    DateTimePicker::make('departure_date')
                        ->label(__('Departure Date'))
                        ->required(),

                    FileUpload::make('attachments')
                        ->label(__('Attachments'))
                        ->multiple(),

                    Toggle::make('already_paid')
                        ->label(__('Pay'))
                        ->reactive()
                        ->afterStateUpdated(function ($set, $get) {
                            if ($get('already_paid')) {
                                $set('paid_at', now()->toDateTimeString());
                                $set('user_id', auth()->id());
                            } else {
                                $set('paid_at', null);
                                $set('user_id', null);
                            }
                        }),

                    Group::make([

                        UsdToCurrencyInputs::make()
                            ->lariInputName('price_lari'),

                        Hidden::make('paid_at')
                            ->afterStateHydrated(function ($set, $get) {
                                if ($get('already_paid')) {
                                    $set('paid_at', now()->toDateTimeString());
                                } else {
                                    $set('paid_at', null);
                                }
                            }),

                        Hidden::make('user_id')
                            ->afterStateHydrated(function ($set, $get) {
                                if ($get('already_paid')) {
                                    $set('user_id', auth()->id());
                                } else {
                                    $set('user_id', null);
                                }
                            }),

                    ])->visible(function ($get) {
                        return !! $get('already_paid');
                    })
                ])
        ])
            ->visible(function () {
                return is_admin() || has_roles([User::BOOKING_HOTEL, User::ACCOUNTANT_BR]);
            });
    }

    public static function insuranceCard()
    {
        return Card::make([
            TextInput::make('insurance_cost')
                ->label(__('Insurance Cost Per Day (Local Currency)'))
                ->afterStateHydrated(function ($component, $record) {

                })
                ->afterStateHydrated(function ($state, $record, $set) {
                    if (!$state && $record) {
                        $insuranceOption = InsuranceOption::query()
                            ->where('country_id', $record->arrival_country_id)
                            ->first();

                        if ($insuranceOption) {
                            $set('insurance_cost', $insuranceOption->price_per_day);
                        }
                    }
                }),
        ])->visible(function () {
            return is_admin() || has_roles([User::BOOKING_HOTEL, User::ACCOUNTANT_BR]);
        });
    }

    public static function insuranceDetailsCard()
    {
        return Card::make([
            Section::make(__('Insurance Details'))
                ->schema([
                    // Bulk Actions for All People
                    Group::make([
                        Placeholder::make('bulk_people_actions')
                            ->label('')
                            ->content(function ($record, $livewire) {
                                if (!$record || !$record->exists || !isset($livewire->record) || !$livewire->record) {
                                    return '';
                                }

                                try {
                                    $reservation = $livewire->record;

                                    if ($reservation->number_of_people <= 0) {
                                        return '';
                                    }

                                    $downloadAllUrl = route('insurance.download-all-people', [
                                        'reservation' => $reservation,
                                        'type' => 'people'
                                    ]);

                                    $html = '<div style="display: flex; justify-content: center; gap: 10px; margin: 12px 0; padding: 12px; border-radius: 8px; border: 1px solid #e2e8f0;">';

                                    // Download All People Insurance
                                    $html .= '<a href="' . $downloadAllUrl . '" ';
                                    $html .= 'class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition ease-in-out duration-150" ';
                                    $html .= 'target="_blank">';
                                    $html .= '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                                    $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>';
                                    $html .= '</svg>';
                                    $html .= __('Download All People Insurance');
                                    $html .= '</a>';

                                    $html .= '</div>';

                                    return new \Illuminate\Support\HtmlString($html);
                                } catch (\Exception $e) {
                                    return '';
                                }
                            })
                            ->visible(function ($record, $livewire) {
                                return $record &&
                                    $record->exists &&
                                    isset($livewire->record) &&
                                    $livewire->record &&
                                    $livewire->record->number_of_people > 0 &&
                                    (is_admin() || has_roles([User::ROLE_SALES, User::ROLE_B2B]));
                            }),
                    ]),

                    Repeater::make('people')
                        ->label(__('People Information'))
                        ->relationship('people')
                        ->schema([
                            Hidden::make('order')
                                ->default(function ($get) {
                                    $existingItems = $get('../../people') ?? [];
                                    return count($existingItems) + 1;
                                }),

                            Grid::make(2)
                                ->schema([
                                    TextInput::make('name')
                                        ->label(__('Full Name'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),

                                    TextInput::make('passport_number')
                                        ->label(__('Passport Number'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),

                                    DatePicker::make('birth_date')
                                        ->label(__('Birth Date'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->displayFormat('d/m/Y'),

                                    TextInput::make('country_name')
                                        ->label(__('Country Name'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),
                                ]),

                                // Individual PDF Actions
                                Placeholder::make('pdf_actions')
                                    ->label('')
                                    ->content(function ($record, $livewire) {
                                        if (!$record || !$record->exists || !isset($livewire->record)) {
                                            return '';
                                        }

                                        try {
                                            $reservation = $livewire->record;

                                            if (!$reservation) {
                                                return '';
                                            }

                                            $downloadUrl = route('insurance.download-single-person', [
                                                'reservation' => $reservation,
                                                'person' => $record
                                            ]);

                                            $previewUrl = route('insurance.preview', [
                                                'reservation' => $reservation,
                                                'person' => $record,
                                                'type' => 'person'
                                            ]);

                                            return view('filament.components.pdf-actions', [
                                                'downloadUrl' => $downloadUrl,
                                                'previewUrl' => $previewUrl
                                            ]);
                                        } catch (\Exception $e) {
                                            return '';
                                        }
                                    })
                                    ->visible(function ($record, $livewire) {
                                        return $record &&
                                            $record->exists &&
                                            isset($livewire->record) &&
                                            $livewire->record &&
                                            $livewire->record->number_of_people > 0 &&
                                            (is_admin() || has_roles([User::ROLE_SALES, User::ROLE_B2B]));
                                    }),
                        ])
                        ->createItemButtonLabel(__('Add Person'))
                        ->collapsible()
                        ->defaultItems(function ($get) {
                            return max(0, intval($get('number_of_people')) ?: 0);
                        })
                        ->minItems(0)
                        ->maxItems(function ($get) {
                            return max(1, intval($get('number_of_people')) ?: 1);
                        }),

                    // Bulk Actions for All Kids
                    Group::make([
                        Placeholder::make('bulk_kids_actions')
                            ->label('')
                            ->content(function ($record, $livewire) {
                                if (!$record || !$record->exists || !isset($livewire->record) || !$livewire->record) {
                                    return '';
                                }

                                try {
                                    $reservation = $livewire->record;

                                    if (($reservation->number_of_kids_under_6 ?? 0) <= 0) {
                                        return '';
                                    }

                                    $downloadAllKidsUrl = route('insurance.download-all-people', [
                                        'reservation' => $reservation,
                                        'type' => 'kids'
                                    ]);

                                    $html = '<div style="display: flex; justify-content: center; gap: 10px; margin: 12px 0; padding: 12px; border-radius: 8px; border: 1px solid #e2e8f0;">';

                                    // Download All Kids Insurance
                                    $html .= '<a href="' . $downloadAllKidsUrl . '" ';
                                    $html .= 'class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition ease-in-out duration-150" ';
                                    $html .= 'target="_blank">';
                                    $html .= '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                                    $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>';
                                    $html .= '</svg>';
                                    $html .= __('Download All Kids Insurance');
                                    $html .= '</a>';

                                    $html .= '</div>';

                                    return new \Illuminate\Support\HtmlString($html);
                                } catch (\Exception $e) {
                                    return '';
                                }
                            })
                            ->visible(function ($record, $livewire) {
                                return $record &&
                                    $record->exists &&
                                    isset($livewire->record) &&
                                    $livewire->record &&
                                    ($livewire->record->number_of_kids_under_6 ?? 0) > 0 &&
                                    (is_admin() || has_roles([User::ROLE_SALES, User::ROLE_B2B]));
                            }),
                    ]),

                    // Kids Under 6 Years Section
                    Repeater::make('kidsUnder6')
                        ->label(__('Kids Under 6 Years'))
                        ->relationship('kidsUnder6')
                        ->schema([
                            Hidden::make('order')
                                ->default(function ($get) {
                                    $existingItems = $get('../../kidsUnder6') ?? [];
                                    return count($existingItems) + 1;
                                }),

                            Grid::make(2)
                                ->schema([
                                    TextInput::make('name')
                                        ->label(__('Full Name'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),

                                    TextInput::make('passport_number')
                                        ->label(__('Passport Number'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),

                                    DatePicker::make('birth_date')
                                        ->label(__('Birth Date'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->displayFormat('d/m/Y'),

                                    TextInput::make('country_name')
                                        ->label(__('Country Name'))
                                        ->visible(function () {
                                            return is_admin() || has_roles([
                                                User::ROLE_SALES,
                                                User::ROLE_B2B,
                                                User::BOOKING_MANAGER_HOTEL,
                                                User::BOOKING_HOTEL
                                            ]);
                                        })
                                        ->maxLength(255),
                                ]),

                                // Individual PDF Actions for Kids
                                Placeholder::make('pdf_actions_kid')
                                    ->label('')
                                    ->content(function ($record, $livewire) {
                                        if (!$record || !$record->exists || !isset($livewire->record)) {
                                            return '';
                                        }

                                        try {
                                            $reservation = $livewire->record;

                                            if (!$reservation) {
                                                return '';
                                            }

                                            $downloadUrl = route('kids.download-single', [
                                                'reservation' => $reservation,
                                                'kid' => $record
                                            ]);

                                            $previewUrl = route('insurance.preview', [
                                                'reservation' => $reservation,
                                                'person' => $record,
                                                'type' => 'kid'
                                            ]);

                                            return view('filament.components.pdf-actions', [
                                                'downloadUrl' => $downloadUrl,
                                                'previewUrl' => $previewUrl
                                            ]);
                                        } catch (\Exception $e) {
                                            return '';
                                        }
                                    })
                                    ->visible(function ($record, $livewire) {
                                        return $record &&
                                            $record->exists &&
                                            isset($livewire->record) &&
                                            $livewire->record &&
                                            $livewire->record->number_of_kids_under_6 > 0 &&
                                            (is_admin() || has_roles([User::ROLE_SALES, User::ROLE_B2B]));
                                    }),
                        ])
                        ->createItemButtonLabel(__('Add Kid'))
                        ->collapsible()
                        ->defaultItems(function ($get) {
                            return max(0, intval($get('number_of_kids_under_6')) ?: 0);
                        })
                        ->minItems(0)
                        ->maxItems(function ($get) {
                            return max(1, intval($get('number_of_kids_under_6')) ?: 1);
                        })
                        ->visible(function ($get) {
                            return intval($get('number_of_kids_under_6')) > 0;
                        }),
                ])
                ->visible(function () {
                    return is_admin() || has_roles([
                        User::ROLE_SALES,
                        User::ROLE_B2B,
                        User::BOOKING_MANAGER_HOTEL,
                        User::BOOKING_HOTEL
                    ]);
                })
                ->collapsible()
                ->collapsed(false),
        ])
            ->visible(function () {
                return is_admin() || has_roles([
                    User::ROLE_SALES,
                    User::ROLE_B2B,
                    User::BOOKING_MANAGER_HOTEL,
                    User::BOOKING_HOTEL
                ]);
            });
    }

    public static function decorationCard()
    {
        return Card::make([
            Select::make('decoration_hotel_id')
                ->label(__('Decoration Hotel'))
                ->searchable()
                ->getSearchResultsUsing(function ($search, $get) {
                    return Hotel::query()
                        ->where('country_id', $get('data.arrival_country_id', true))
                        ->where('name', 'like', '%'.$search.'%')
                        ->get()
                        ->pluck('name', 'id');
                })
                ->getOptionLabelUsing(function ($value) {
                    return Hotel::query()->find($value)?->name;
                })
                ->requiredWith('decoration_cost_usd'),

            UsdToCurrencyInputs::make()
                ->countryId(function ($get) {
                    return $get('data.arrival_country_id', true);
                })
                ->required(false)
                ->usdInputName('decoration_cost_usd')
                ->lariInputName('decoration_cost_lari')
                ->afterStateUpdated(function ($get, $set) {
                    TourCostResource::updateCost($get, $set);
                }),

            DateTimePicker::make('decoration_date')
                ->label(__('Decoration Date')),

        ])
            ->visible(function () {
                return is_admin() || has_roles([User::ROLE_SALES, User::BOOKING_HOTEL, User::BOOKING_MANAGER_HOTEL, User::BOOKING_MANAGER_TRANSPORTATION, User::ACCOUNTANT_HOTEL, User::ACCOUNTANT_TRANSPORTATION, User::ACCOUNTANT_BR]);
            });
    }

    public static function calculateHotelPrice($get, $set, Component $livewire = null, Field $component = null)
    {
        $checkIn = $get('check_in') ? Carbon::parse($get('check_in')) : null;
        $checkOut = $get('check_out') ? Carbon::parse($get('check_out')) : null;
        $roomTypeId = $get('room_type_id');
        $hotelId = $get('hotel_id');
        $hotelBookingRooms = array_values($get('hotelBookingRooms'));
        $roomCount = $get('room_count');
        $countryId = $get('data.arrival_country_id', true);

        $hotelBooking = new HotelBooking([
            'check_in' => $checkIn,
            'check_out' => $checkOut,
            'room_type_id' => $roomTypeId,
            'hotel_id' => $hotelId,
            'room_count' => $roomCount,
            'country_id' => $countryId,
        ]);

        $hotelBooking->setRelation('hotelBookingRooms', collect($hotelBookingRooms)->map(function ($hotelBookingRoom) {
            return new HotelBookingRoom([
                'room_type_id' => data_get($hotelBookingRoom, 'room_type_id'),
                'count' => data_get($hotelBookingRoom, 'count'),
                'price_usd' => data_get($hotelBookingRoom, 'price_usd'),
                'price_lari' => data_get($hotelBookingRoom, 'price_lari'),
                'room_name' => data_get($hotelBookingRoom, 'room_name'),
            ]);
        })->values());

        $prices = $hotelBooking->calculateHotelPrice();

        $set('price_usd', data_get($prices, 'price_usd'));
        $set('price_lari', data_get($prices, 'price_lari'));
        $set('vat_usd', data_get($prices, 'vat_usd'));
        $set('vat_lari', data_get($prices, 'vat_lari'));
        $set('error', data_get($prices, 'error'));

        $hotelBookingRoomsTotals = data_get($prices, 'hotel_booking_rooms_totals');
        if ($hotelBookingRoomsTotals && count($hotelBookingRoomsTotals)) {
            $set('hotelBookingRooms', collect(data_get($prices, 'hotel_booking_rooms_totals'))->mapWithKeys(function ($hotelBookingRoomTotal) {
                $uuid = Str::uuid()->toString();

                return [$uuid => [
                    'price_usd' => data_get($hotelBookingRoomTotal, 'price_usd'),
                    'price_lari' => data_get($hotelBookingRoomTotal, 'price_lari'),
                    'room_type_id' => data_get($hotelBookingRoomTotal, 'room_type_id'),
                    'count' => data_get($hotelBookingRoomTotal, 'count'),
                    'room_name' => data_get($hotelBookingRoomTotal, 'room_name'),
                ]];
            })->toArray());
        }

        if ($livewire && $component) {
            $paths = [
                Str::replace($component->getStatePath(false), 'hotel_id', $component->getStatePath(true)),
                Str::replace($component->getStatePath(false), 'check_in', $component->getStatePath(true)),
                Str::replace($component->getStatePath(false), 'check_out', $component->getStatePath(true)),
            ];

            if ($error = $get('error')) {
                foreach ($paths as $statePath) {
                    $livewire->addError($statePath, $error);
                }
            }else{
                foreach ($paths as $statePath) {
                    $livewire->resetErrorBag($statePath);
                }
            }
        }

        TourCostResource::updateCost($get, $set);
    }

    public static function calculateTransportationCompanyPrice($get, $set, Component $livewire = null, Field $component = null)
    {
        $fromDate = $get('from_date') ? Carbon::parse($get('from_date')) : null;
        $toDate = $get('to_date') ? Carbon::parse($get('to_date')) : null;
        $carTypeId = $get('car_type_id');
        $bonusLari = $get('bonus_lari');
        $transportationCompanyId = $get('transportation_company_id');
        $countryId = $get('data.arrival_country_id', true);
        $transportationCompanyBookingCars = array_values($get('transportationCompanyBookingCars') ?? []);
        $transportationCompanyBookingTransfers = array_values($get('transportationCompanyBookingTransfers') ?? []);

        $days = $fromDate->diffInDays($toDate) + 1;

        /** @var TransportationCompanyBooking $transportationCompanyBooking */
        $transportationCompanyBooking = new TransportationCompanyBooking([
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'country_id' => $countryId,
            'car_type_id' => $carTypeId,
            'transportation_company_id' => $transportationCompanyId,
            'bonus_lari' => $bonusLari,
        ]);

        $extraServiceBookings = collect(array_values($get('extraServiceBookings') ?? []))
            ->map(function ($extraServiceBooking) {
                if (!data_get($extraServiceBooking, 'extra_service_id') || !data_get($extraServiceBooking, 'count')) {
                    return null;
                }
                return new ExtraServiceBooking([
                    'extra_service_id' => data_get($extraServiceBooking, 'extra_service_id'),
                    'count' => data_get($extraServiceBooking, 'count'),
                ]);
            })
            ->filter()
            ->values();


        if (count($extraServiceBookings)) {
            $transportationCompanyBooking->setRelation('extraServiceBookings', $extraServiceBookings);
        }

        if (count($transportationCompanyBookingCars)) {
            $transportationCompanyBooking->setRelation('transportationCompanyBookingCars', collect($transportationCompanyBookingCars)->map(function ($transportationCompanyBookingCar) {
                return new TransportationCompanyBookingCar([
                    'car_type_id' => data_get($transportationCompanyBookingCar, 'car_type_id'),
                    'date' => data_get($transportationCompanyBookingCar, 'date'),
                    'price_usd' => data_get($transportationCompanyBookingCar, 'price_usd'),
                    'price_lari' => data_get($transportationCompanyBookingCar, 'price_lari'),
                    'half_day' => data_get($transportationCompanyBookingCar, 'half_day'),
                ]);
            })->values());
        }

        $transportationCompanyBookingTransfers = collect($transportationCompanyBookingTransfers)->map(function ($transportationCompanyBookingTransfer) {
            return new TransportationCompanyBookingTransfer([
                'transportation_company_transfer_id' => data_get($transportationCompanyBookingTransfer, 'transportation_company_transfer_id'),
                'price_lari' => data_get($transportationCompanyBookingTransfer, 'price_lari'),
            ]);
        })->values();

        if (count($transportationCompanyBookingTransfers)) {
            $transportationCompanyBooking->setRelation('transportationCompanyBookingTransfers', $transportationCompanyBookingTransfers);
        }

        $prices = $transportationCompanyBooking->calculatePrice();

        $set('price_usd', data_get($prices, 'price_usd'));
        $set('price_lari', data_get($prices, 'price_lari'));

        $paymentType = $get('payment_type');
        $priceLari = $get('price_lari');

        if ($paymentType && $priceLari) {
            if ($paymentType == "100") {
                $set('total_cost_lari', $priceLari);
            }else if ($paymentType == "50") {
                $set('total_cost_lari', $priceLari / 2);
            }
        }

        $set('vat_usd', data_get($prices, 'vat_usd'));
        $set('vat_lari', data_get($prices, 'vat_lari'));
        $set('extra_services_cost_usd', data_get($prices, 'extra_services_cost_usd'));
        $set('extra_services_cost_usd', data_get($prices, 'extra_services_cost_usd'));
        $set('bonus_usd', data_get($prices, 'bonus_usd'));
        $set('bonus_lari', data_get($prices, 'bonus_lari'));

        $transportationCompanyBookingCars = data_get($prices, 'transportation_company_booking_car_prices');
        $set('days', $transportationCompanyBookingCars && count($transportationCompanyBookingCars) > 0 ? collect($transportationCompanyBookingCars)->sum(function ($item) {
            return data_get($item, 'half_day') ? 0.5 : 1;
        }) : null);

        $set('transportationCompanyBookingCars', $transportationCompanyBookingCars);
        $set('transportationCompanyBookingTransfers', data_get($prices, 'transportation_company_booking_transfers'));
        $set('extraServiceBookings', data_get($prices, 'extra_service_bookings'));
        $set('error', data_get($prices, 'error'));

        if ($livewire && $component) {
            $paths = [
                Str::replace($component->getStatePath(false), 'transportation_company_id', $component->getStatePath(true)),
                Str::replace($component->getStatePath(false), 'from_date', $component->getStatePath(true)),
                Str::replace($component->getStatePath(false), 'to_date', $component->getStatePath(true)),
            ];

            if ($error = $get('error')) {
                foreach ($paths as $statePath) {
                    $livewire->addError($statePath, $error);
                }
            }else{
                foreach ($paths as $statePath) {
                    $livewire->resetErrorBag($statePath);
                }
            }
        }

        TourCostResource::updateCost($get, $set);
    }

    public static function updateCost($get, $set): void
    {
        $hotelPrices = collect($get('data.hotelBookings', true))->map(function ($hotelBooking) {
            return [
                'price_usd' => $hotelBooking['price_usd'],
                'price_lari' => $hotelBooking['price_lari'],
            ];
        });

        $transportationCompanyPrices = collect($get('data.transportationCompanyBookings', true))->map(function ($transportationCompanyBooking) {
            return [
                'price_usd' => $transportationCompanyBooking['price_usd'],
                'price_lari' => $transportationCompanyBooking['price_lari'],
            ];
        });

        $decorationCostUsd = (double) $get('data.decoration_cost_usd', true);
        $decorationCostLari = (double) $get('data.decoration_cost_lari', true);

        $totalPriceUsd = $hotelPrices->sum('price_usd') + $transportationCompanyPrices->sum('price_usd') + $decorationCostUsd;
        $totalPriceLari = $hotelPrices->sum('price_lari') + $transportationCompanyPrices->sum('price_lari')  + $decorationCostLari;

        $set('data.total_price_usd', $totalPriceUsd, true);
        $set('data.total_price_lari', $totalPriceLari, true);
    }

    public static function setHotelBookingRooms($get, $set, $livewire, $component)
    {
        if ($get('hotel_id') && $get('check_in') && $get('check_out') && $get('room_count')) {
            $oldHotelBookingRooms = array_values($get('hotelBookingRooms') ?? []);

            $newCount = $get('room_count') - count($oldHotelBookingRooms);

            if ($newCount > 0) {
                $newHotelBookingRooms = collect(range(1, $newCount))->map(function () {
                    return [
                        'room_type_id' => null,
                        'count' => 1,
                        'price_usd' => null,
                        'price_lari' => null,
                    ];
                })->toArray();

                $hotelBookingRooms = collect(array_merge($oldHotelBookingRooms, $newHotelBookingRooms))
                    ->mapWithKeys(function ($hotelBookingRoom) {
                        $uuid = Str::uuid()->toString();
                        return [$uuid => $hotelBookingRoom];
                    })->toArray();

                $set('hotelBookingRooms', $hotelBookingRooms);
            }
        }
    }

    public static function setTransportationCompanyBookingCars($get, $set, $livewire, $component)
    {
        $fromDate = $get('from_date') ? Carbon::parse($get('from_date')) : null;
        $toDate = $get('to_date') ? Carbon::parse($get('to_date')) : null;
        $carTypeId = $get('car_type_id');

        $numberOfDays = $fromDate->diffInDays($toDate) + 1;

        if ($numberOfDays && $numberOfDays > 0 && $carTypeId && $get('transportation_company_id')) {

            $start = 1;
            $end = $numberOfDays;

            if ($numberOfDays > 0) {
                $newTransportationCompanyBookingCars = collect(range($start, $end))->map(function ($number) use ($carTypeId, $fromDate, $toDate) {
                    return [
                        'car_type_id' => $carTypeId,
                        'date' => $fromDate->copy()->addDays($number - 1)->toDateString(),
                        'price_usd' => null,
                        'price_lari' => null,
                    ];
                })->toArray();

                $transportationCompanyBookingCars = collect(array_merge($newTransportationCompanyBookingCars))
                    ->mapWithKeys(function ($transportationCompanyBookingCar) {
                        $uuid = Str::uuid()->toString();
                        return [$uuid => $transportationCompanyBookingCar];
                    })->toArray();

                $set('transportationCompanyBookingCars', $transportationCompanyBookingCars);
            }
        }

        TourCostResource::calculateTransportationCompanyPrice($get, $set, $livewire, $component);
    }

    public static function canViewAny(): bool
    {
        return false;
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }

    public static function canForceDeleteAny(): bool
    {
        return false;
    }
}
