<?php

namespace App\Filament\Resources\NewB2cOrdersResource\Pages;

use App\Filament\Resources\NewB2cOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNewB2cOrders extends ListRecords
{
    protected static string $resource = NewB2cOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
