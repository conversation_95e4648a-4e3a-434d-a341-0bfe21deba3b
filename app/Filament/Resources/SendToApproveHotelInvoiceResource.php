<?php

namespace App\Filament\Resources;

use App\Enums\PaymentStatus;
use App\Filament\Concerns\SortNavigation;
use App\Filament\Resources\SendToApproveHotelInvoiceResource\Pages;
use App\Models\HotelInvoice;
use App\Models\User;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class SendToApproveHotelInvoiceResource extends Resource
{
    use SortNavigation;

    protected static ?string $model = HotelInvoice::class;

    protected static ?string $navigationIcon = 'fas-file';

    public static function getLabel(): ?string
    {
        return __('Send To Approve');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Send To Approve');
    }

    protected static function getNavigationGroup(): ?string
    {
        return __('Hotel Bookings');
    }

    protected static ?string $slug = 'send-to-approve-hotel-invoices';

    protected static function getNavigationBadge(): ?string
    {
        return HotelInvoice::query()
            ->whereNull('send_to_approve_at')
            ->where('status', PaymentStatus::PENDING)
            ->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return PendingHotelInvoiceResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSendToApproveHotelInvoices::route('/'),
            'create' => Pages\CreateSendToApproveHotelInvoice::route('/create'),
            'edit' => Pages\EditSendToApproveHotelInvoice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereNull('send_to_approve_at')
            ->where('status', PaymentStatus::PENDING);
    }

    public static function canViewAny(): bool
    {
        return is_admin() || has_roles([User::ACCOUNTANT_HOTEL]);
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
