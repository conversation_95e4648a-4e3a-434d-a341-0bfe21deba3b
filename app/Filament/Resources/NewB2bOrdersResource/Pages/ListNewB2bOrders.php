<?php

namespace App\Filament\Resources\NewB2bOrdersResource\Pages;

use App\Filament\Resources\NewB2bOrdersResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNewB2bOrders extends ListRecords
{
    protected static string $resource = NewB2bOrdersResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
