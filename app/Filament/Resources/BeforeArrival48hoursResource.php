<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BeforeArrival48hoursResource\Pages;
use App\Models\Reservation;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BeforeArrival48hoursResource extends Resource
{
    protected static ?string $model = Reservation::class;

    protected static ?string $navigationIcon = 'fas-dollar-sign';

    protected static ?int $navigationSort = 2;

    protected static ?string $slug = 'before-48hr-b2b-reservations';

    public static function getLabel(): ?string
    {
        return __('Before Arrival 48hours');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Before Arrival 48hours');
    }

    protected static function getNavigationGroup(): ?string
    {
        return __('B2B Reservations');
    }

    protected static function getNavigationBadge(): ?string
    {
        return Reservation::query()
            ->when(
                (has_roles(User::ROLE_MEET)) && auth()->user()->branches->count() > 0,
                function (Builder $builder) {
                    $builder->where(function ($query) {
                        $query->whereIn('branch_id', auth()->user()->branches->pluck('id'))
                            ->orWhere('user_id', auth()->id());
                    });
                }
            )
            // before 48 hours
            ->whereDate('arrival_date', '<=', now()->addHours(48))
            ->whereDate('arrival_date', '>', now())
            ->notTotalyPaid()
            ->b2b()
            ->notCancelled()
            ->count();
    }

    public static function form(Form $form): Form
    {
        return ReservationResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return ReservationResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBeforeArrival48hours::route('/'),
            'create' => Pages\CreateBeforeArrival48hours::route('/create'),
            'edit' => Pages\EditBeforeArrival48hours::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->when(
                (has_roles(User::ROLE_MEET)) && auth()->user()->branches->count() > 0,
                function (Builder $builder) {
                    $builder->where(function ($query) {
                        $query->whereIn('branch_id', auth()->user()->branches->pluck('id'))
                            ->orWhere('user_id', auth()->id());
                    });
                }
            )
            ->whereDate('arrival_date', '<=', now()->addHours(48))
            ->whereDate('arrival_date', '>', now())
            ->notTotalyPaid()
            ->notCancelled()
            ->b2b();
    }

    public static function canViewAny(): bool
    {
        return is_admin() || has_roles([User::ROLE_B2B, User::ROLE_CASH_EMPLOYEE, User::ACCOUNTANT_BR]) || only_view();
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return ReservationResource::canDelete($record);
    }

    public static function canEdit(Model $record): bool
    {
        return ReservationResource::canEdit($record);
    }

    public static function canDeleteAny(): bool
    {
        return ReservationResource::canDeleteAny();
    }

    public static function canForceDeleteAny(): bool
    {
        return ReservationResource::canDeleteAny();
    }
}
