<?php

namespace App\Filament\Resources\AgentPaymentResource\Pages;

use App\Filament\Resources\AgentPaymentResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAgentPayment extends EditRecord
{
    protected static string $resource = AgentPaymentResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
