<?php

namespace App\Filament\Resources\BeforeArrival48hoursResource\Pages;

use App\Filament\Resources\BeforeArrival48hoursResource;
use App\Filament\Resources\LeftB2bReservationResource\Pages\ListLeftB2bReservations;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBeforeArrival48hours extends ListLeftB2bReservations
{
    protected static string $resource = BeforeArrival48hoursResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
