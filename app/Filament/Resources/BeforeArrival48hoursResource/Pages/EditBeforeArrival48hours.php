<?php

namespace App\Filament\Resources\BeforeArrival48hoursResource\Pages;

use App\Filament\Resources\BeforeArrival48hoursResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBeforeArrival48hours extends EditRecord
{
    protected static string $resource = BeforeArrival48hoursResource::class;

    protected function getActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
