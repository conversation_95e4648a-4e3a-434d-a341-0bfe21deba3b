<?php

namespace App\Filament\Resources\FlightAccountingResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ConfirmedPaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'confirmedFlightPayments';

    protected static ?string $recordTitleAttribute = 'amount';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('confirmed_at')
                    ->label(__('Confirmed At'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('paymentMethod.name')
                    ->label(__('Payment Method'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('payable.ticket_number')
                    ->label(__('Ticket Number'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->sortable()
                    ->color(fn($record) => $record->amount > 0 ? 'success' : 'danger'),

                Tables\Columns\BooleanColumn::make('is_collected')
                    ->label(__('Is Collected')),
            ])
            ->defaultSort('confirmed_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name'),

                Tables\Filters\SelectFilter::make('is_collected')
                    ->options([
                        'collected' => __('Yes'),
                        'notCollected' => __('No'),
                    ])
                    ->query(function ($query, $data, $livewire) {
                        $value = data_get($data, 'value');

                        if ($value) {
                            $query->{$value}();
                        }
                    }),

                Tables\Filters\Filter::make('confirmed_at')
                    ->form([
                        Forms\Components\DatePicker::make('confirmed_at_from'),
                        Forms\Components\DatePicker::make('confirmed_at_to'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['confirmed_at_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('confirmed_at', '>=', $date),
                            )
                            ->when(
                                $data['confirmed_at_to'],
                                fn (Builder $query, $date): Builder => $query->whereDate('confirmed_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('collect')
                    ->label(__('Collect'))
                    ->requiresConfirmation()
                    ->color('success')
                    ->icon('heroicon-o-cash')
                    ->visible(fn($record) => !$record->is_collected && is_admin())
                    ->action(function ($record) {
                        $record->update([
                            'collected_at' => now(),
                        ]);
                    }),

//                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\DeleteBulkAction::make(),

                Tables\Actions\BulkAction::make('collect')
                    ->label(__('Collect'))
                    ->requiresConfirmation()
                    ->icon('heroicon-o-collection')
                    ->action(function ($records) {
                        foreach ($records as $record) {
                            $record->update([
                                'collected_at' => now(),
                            ]);
                        }
                    }),
            ]);
    }

    protected function canCreate(): bool
    {
        return false;
    }

    protected function canEdit(Model $record): bool
    {
        return false;
    }

    protected function canDelete(Model $record): bool
    {
        return false;
    }
}
