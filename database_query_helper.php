<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

function queryTable($tableName, $limit = 100, $offset = 0) {
    try {
        $results = DB::table($tableName)->offset($offset)->limit($limit)->get();
        return [
            'table' => $tableName,
            'count' => DB::table($tableName)->count(),
            'data' => $results->toArray()
        ];
    } catch (Exception $e) {
        return [
            'table' => $tableName,
            'error' => $e->getMessage()
        ];
    }
}

function getAllTables() {
    try {
        $tables = DB::select('SHOW TABLES');
        $tableNames = [];
        foreach ($tables as $table) {
            $tableNames[] = array_values((array)$table)[0];
        }
        return $tableNames;
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

function getTableSchema($tableName) {
    try {
        $columns = DB::select("DESCRIBE `$tableName`");
        return [
            'table' => $tableName,
            'schema' => $columns
        ];
    } catch (Exception $e) {
        return [
            'table' => $tableName,
            'error' => $e->getMessage()
        ];
    }
}

// Handle command line arguments
if ($argc > 1) {
    $command = $argv[1];
    
    switch ($command) {
        case 'tables':
            echo json_encode(getAllTables(), JSON_PRETTY_PRINT);
            break;
            
        case 'schema':
            if (isset($argv[2])) {
                echo json_encode(getTableSchema($argv[2]), JSON_PRETTY_PRINT);
            } else {
                echo "Usage: php database_query_helper.php schema TABLE_NAME\n";
            }
            break;
            
        case 'query':
            if (isset($argv[2])) {
                $limit = isset($argv[3]) ? (int)$argv[3] : 100;
                $offset = isset($argv[4]) ? (int)$argv[4] : 0;
                echo json_encode(queryTable($argv[2], $limit, $offset), JSON_PRETTY_PRINT);
            } else {
                echo "Usage: php database_query_helper.php query TABLE_NAME [LIMIT] [OFFSET]\n";
            }
            break;
            
        case 'count':
            if (isset($argv[2])) {
                try {
                    $count = DB::table($argv[2])->count();
                    echo json_encode(['table' => $argv[2], 'count' => $count], JSON_PRETTY_PRINT);
                } catch (Exception $e) {
                    echo json_encode(['table' => $argv[2], 'error' => $e->getMessage()], JSON_PRETTY_PRINT);
                }
            } else {
                echo "Usage: php database_query_helper.php count TABLE_NAME\n";
            }
            break;
            
        default:
            echo "Available commands:\n";
            echo "  tables - List all tables\n";
            echo "  schema TABLE_NAME - Get table schema\n";
            echo "  query TABLE_NAME [LIMIT] [OFFSET] - Query table data\n";
            echo "  count TABLE_NAME - Get table row count\n";
    }
} else {
    echo "Usage: php database_query_helper.php COMMAND [ARGS]\n";
    echo "Run 'php database_query_helper.php help' for available commands\n";
} 