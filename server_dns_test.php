<?php
/**
 * Server DNS Test Script
 * Run this on your production server to test DNS resolution
 * Usage: php server_dns_test.php
 */

echo "🔍 Server DNS Resolution Test\n";
echo "============================\n\n";

// Test domains
$testDomains = [
    'google.com',
    'cloudflare.com',
    'api-eu.pusherapp.com',
    'api-us2.pusherapp.com',
    'api-ap1.pusherapp.com',
    'pusher.com'
];

echo "1. Testing basic DNS resolution:\n";
foreach ($testDomains as $domain) {
    $ip = gethostbyname($domain);
    if ($ip !== $domain) {
        echo "✅ {$domain} → {$ip}\n";
    } else {
        echo "❌ {$domain} → FAILED\n";
    }
}

echo "\n2. Testing DNS servers:\n";
$dnsServers = [
    '*******' => 'Google DNS',
    '*******' => 'Cloudflare DNS',
    '**************' => 'OpenDNS'
];

foreach ($dnsServers as $dns => $name) {
    echo "Testing {$name} ({$dns}):\n";
    $result = shell_exec("nslookup api-eu.pusherapp.com {$dns} 2>&1");
    if (strpos($result, 'NXDOMAIN') === false && strpos($result, 'can\'t find') === false) {
        echo "✅ {$name} works\n";
    } else {
        echo "❌ {$name} failed\n";
    }
}

echo "\n3. Testing cURL with different DNS:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api-eu.pusherapp.com');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_RESOLVE, ['api-eu.pusherapp.com:443:*************']); // Example IP
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ cURL with IP resolution failed: {$error}\n";
} else {
    echo "✅ cURL with IP resolution worked (HTTP {$httpCode})\n";
}

echo "\n4. Server network configuration:\n";
echo "PHP version: " . PHP_VERSION . "\n";
echo "cURL version: " . curl_version()['version'] . "\n";

// Check resolv.conf if accessible
if (file_exists('/etc/resolv.conf')) {
    echo "\nDNS configuration (/etc/resolv.conf):\n";
    echo file_get_contents('/etc/resolv.conf');
} else {
    echo "❌ Cannot access /etc/resolv.conf\n";
}

echo "\n🏁 DNS test completed.\n";
echo "\nIf all external domains fail, contact your hosting provider about:\n";
echo "- DNS server configuration\n";
echo "- Firewall blocking DNS queries (port 53)\n";
echo "- Network connectivity issues\n";
?> 