// Company filter auto-reload script for All B2B Left Amount page
document.addEventListener('DOMContentLoaded', function() {
    // Only run on the All B2B Left Amount page
    if (window.location.pathname.includes('all-left-reservations')) {
        console.log('Company filter reload script loaded for All B2B Left Amount page');
        
        let retryCount = 0;
        const maxRetries = 10;
        
        function addCompanyFilterListener() {
            // Try to find the company filter
            const companyFilter = document.querySelector('select[name="tableFilters[company_id][value]"]');
            
            if (companyFilter) {
                console.log('Found company filter, adding change listener');
                
                companyFilter.addEventListener('change', function(event) {
                    const selectedValue = event.target.value;
                    console.log('Company filter changed to:', selectedValue);
                    
                    // Reload the page after a short delay
                    setTimeout(function() {
                        console.log('Reloading page...');
                        window.location.reload();
                    }, 200);
                });
                
                return true; // Success
            } else if (retryCount < maxRetries) {
                retryCount++;
                console.log(`Company filter not found, retry ${retryCount}/${maxRetries} in 500ms...`);
                setTimeout(addCompanyFilterListener, 500);
                return false; // Retry
            } else {
                console.log('Company filter not found after maximum retries');
                return false; // Give up
            }
        }
        
        // Start trying to add the listener
        addCompanyFilterListener();
    }
}); 