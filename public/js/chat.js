document.addEventListener('DOMContentLoaded', function() {
    // Listen for new messages
    Livewire.on('new-message', (userId, message) => {
        // Play notification sound
        const audio = new Audio('/notification.mp3');
        audio.play();

        // Update messages if chat is open
        if (document.querySelector(`[data-chat-id="${userId}"]`)) {
            Livewire.emit('message-received', userId, message);
        }
    });

    // Ensure chat sidebar is properly positioned
    const header = document.querySelector('.filament-header');
    if (header) {
        document.documentElement.style.setProperty('--topbar-height', `${header.offsetHeight}px`);
    }

    // Sidebar auto-scroll to active item
    function scrollToActiveSidebarItem() {
        // Wait for sidebar to be rendered
        setTimeout(() => {
            const sidebar = document.querySelector('.filament-sidebar-nav');
            const activeItem = document.querySelector('.filament-sidebar-item-active');
            
            if (sidebar && activeItem) {
                const sidebarRect = sidebar.getBoundingClientRect();
                const activeItemRect = activeItem.getBoundingClientRect();
                
                // Check if active item is visible in sidebar viewport
                const isVisible = activeItemRect.top >= sidebarRect.top && 
                                  activeItemRect.bottom <= sidebarRect.bottom;
                
                if (!isVisible) {
                    // Calculate the scroll position to center the active item
                    const currentScrollTop = sidebar.scrollTop;
                    const sidebarHeight = sidebar.clientHeight;
                    const activeItemTop = activeItem.offsetTop;
                    const activeItemHeight = activeItem.offsetHeight;
                    
                    // Center the active item in the sidebar
                    const scrollTo = activeItemTop - (sidebarHeight / 2) + (activeItemHeight / 2);
                    
                    sidebar.scrollTo({
                        top: Math.max(0, scrollTo),
                        behavior: 'smooth'
                    });
                }
            }
        }, 100);
    }

    // Initial scroll when page loads
    scrollToActiveSidebarItem();

    // Also scroll when navigation changes (for SPAs/Livewire navigation)
    document.addEventListener('livewire:navigated', scrollToActiveSidebarItem);
    
    // Listen for page transitions (Turbo/Livewire)
    document.addEventListener('turbo:render', scrollToActiveSidebarItem);
    document.addEventListener('turbo:load', scrollToActiveSidebarItem);
    
    // For Alpine.js state changes
    document.addEventListener('alpine:initialized', scrollToActiveSidebarItem);
}); 