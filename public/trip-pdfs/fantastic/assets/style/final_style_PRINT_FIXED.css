/* ==========================================
   1. FONTS & RESET
   ========================================== */
@import url(https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap);
@import url('https://fonts.googleapis.com/css2?family=Smooch&display=swap');

:root {
  --primary-blue: #278cca;
  --sceond-primary-blue: #8dd3ff;
  --primary-dark: #000;
  --light-blue: #023e8a;
  --dark-blue: #03045e;
  --bg-color: #ffff;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Cairo", sans-serif;
  font-size: 16px;
  overflow-x: hidden;
  margin: 0;
  background-color: var(--bg-color);
}

/* ==========================================
      2. COMMON STYLES & VARIABLES
      ========================================== */

.hero-banner {
  position: relative;
  height: 100vh;
  background: url('../image/Clip\ path\ group.png') center/cover no-repeat;
  overflow: hidden;
  display: flex;
  align-items: center;
}

/* شعار 4 Seasons */
.logo {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 80px;
  height: 80px;
  background: var(--bg-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.logo-text {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  line-height: 1.2;
}

/* المحتوى الرئيسي */
.hero-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.restaurant-desc {
  color: #000000;
}

/* الجانب الأيسر - المحتوى العربي */
.left-section {
  flex: 1;
  padding: 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: right;
  flex-wrap: wrap;
  align-content: center;
}

/* الجانب الأيمن - النص الإنجليزي */
.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 40px;
  position: relative;
  align-content: center;
  flex-wrap: wrap;
}

.magic-text {
  font-family: 'Cairo', sans-serif;
  font-size: 48px;
  font-weight: 300;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* الشريط البنفسجي */
/* الشريط البنفسجي */
.purple-strip {
  display: flex;
  background: linear-gradient(270deg, var(--dark-blue) 0%, var(--light-blue) 70%, rgba(74, 152, 255, 0.26) 100%);
  padding: 80px 40px 80px 120px;
  border-radius: 50px 0 0 50px;
  position: relative;
  margin-bottom: 30px;
  box-shadow: 0 8px 25px rgba(15, 79, 255, 0.3);
  width: calc(207% + 50px);
  margin-left: -64rem;
  align-content: center;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.program-title {
  color: white;
  font-size: 32px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
}


/* الزر البيضاوي */
.duration-btn {
  background: white;
  padding: 15px 115px;
  border-radius: 50px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border: none;
  font-size: 18px;
  font-weight: 600;
  cursor: none;
  transition: all 0.3s ease;
}



.golden-text {
  color: var(--light-blue);
  font-weight: 800;
}

/* Media Queries - min-width فقط */
@media (min-width: 576px) {
  .logo {
    width: 90px;
    height: 90px;
    right: 30px;
  }

  .logo-text {
    font-size: 13px;
  }

  .magic-text {
    font-size: 52px;
  }

  .georgia-text {
    font-family: 'Pacifico', cursive;
    font-size: 130px;
  }

  .program-title {
    font-size: 34px;
  }
}

@media (min-width: 768px) {
  .logo {
    width: 180px;
    height: 180px;
    top: 40px;
    right: 104rem;
  }

  .logo-text {
    font-size: 14px;
  }

  .magic-text {
    font-size: 58px;
  }

  .georgia-text {
    font-size: 150px;
  }

  .program-title {
    font-size: 36px;
  }

  .duration-btn {
    color: var(--light-blue);
    font-size: 25px;
    padding: 18px 115px;
    font-weight: 800;
  }
}

@media (min-width: 992px) {
  .magic-text {
    font-size: 64px;
  }

  .georgia-text {
    font-family: "Smooch", cursive;
    font-weight: 400;
    font-size: 190px;
  }

  .program-title {
    font-size: 38px;
  }
}

@media (min-width: 1200px) {
  .magic-text {
    font-size: 72px;
  }

  .georgia-text {
    font-size: 190px;
  }

  .program-title {
    font-size: 42px;
  }
}

/* تخصيص للموبايل */
@media (max-width: 575px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .left-section {
    order: 1;
    align-items: center;
    text-align: center;
  }

  .right-section {
    order: 2;
    align-items: center;
  }

  .logo {
    width: 70px;
    height: 70px;
    top: 20px;
    right: 20px;
  }

  .logo-text {
    font-size: 11px;
  }

  .magic-text {
    font-size: 36px;
  }


  .program-title {
    font-size: 24px;
  }

  .purple-strip {
    padding: 20px 50px 20px 30px;
  }

  .duration-btn {
    font-size: 16px;
    padding: 12px 25px;
  }
}

.page {
  padding: 5mm 2mm;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.container-content {
  margin-top: 0;
  padding-top: 0;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

/* ==========================================
      3. TYPOGRAPHY
      ========================================== */
/* 3.1 Headings */
.headline {
  line-height: 40px;
  color: var(--primary-blue);
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

.headline::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60%;
  height: 5px;
  background-color: var(--primary-blue);
}

.page-title {
  color: var(--primary-blue);
  font-weight: 800;
  font-size: 26px;
  text-align: right;
  margin-bottom: 8px;
  position: relative;
}

.page-title::after {
  content: "";
  display: block;
  width: 200px;
  height: 4px;
  background-color: var(--primary-blue);
  margin: 6px 0 20px;
}

.title {
  color: var(--primary-blue);
  font-weight: 700;
  font-size: 2.3rem;
  text-align: right;
  line-height: 60px;
  position: relative;
  margin-bottom: 1.3rem;
}

.title::after {
  content: "";
  display: block;
  width: 130px;
  height: 5px;
  background-color: var(--primary-blue);
  margin-top: 5px;
  margin-left: 0;
}

.program-title,
.secondary-title {
  font-size: 75px;
  font-weight: 900;
  position: relative;
  padding-right: 13px;
  padding-bottom: 15px;
}

.secondary-title {
  color: var(--primary-blue);
}



.program-title::after {
  background-color: var(--primary-blue);
}

.secondary-title::after {
  background-color: var(--primary-blue);
}

.section-title,
.section-title-hotail,
.section-title_gaolh {
  background: linear-gradient(to right, #97d0f3, var(--sceond-primary-blue));
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--light-blue);
  font-weight: 700;
  display: inline-block;
}

.section-title {
  padding: 0.3rem 11rem 0.3rem 2rem;
}

.section-title-hotail {
  font-size: 15px !important;
  padding: 0.3rem 15rem 0.3rem 2rem;
}

.section-title_gaolh {
  font-size: 15px !important;
  padding: 0.3rem 8rem 0.3rem 2rem;
}

/* 3.2 Text Elements */
.tour-description {
  line-height: 2;
  text-align: justify;
  font-size: 1rem;
}

.introduction {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 24px;
  max-width: 50%;
}

.climate {
  width: 40rem;
  margin-top: 15px;
  font-size: 14.5px;
  text-align: justify;
  font-weight: 400;
}

.climate strong {
  color: var(--primary-blue);
  font-weight: 700;
}

.feature-text {
  font-size: 13px;
}

.team-regards {
  color: var(--bg-color) !important;
  font-weight: 700;
  text-align: center;
  font-size: 18px;
}

/* ==========================================
      4. LAYOUT
      ========================================== */
/* 4.1 Containers */
.container-flex {
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  align-items: center;
}

/* 4.2 Grids */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  flex: 1;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem;
}

.info-grid {
  font-weight: 700;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 15px;
  text-align: right;
}

/* ==========================================
      5. COMPONENTS
      ========================================== */
/* 5.1 Features */
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

.icon-circle {
  background-color: var(--primary-blue);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 10px;
}

.icon-img {
  width: 40px;
  height: 40px;
}

/* 5.2 Images */
.image-container {
  position: relative;
  margin: 3rem 5rem 0 0;
}

.image-wrapper {
  position: relative;
  z-index: 2;
}

.image-wrapper img {
  width: 90%;
  max-height: 750px;
  object-fit: cover;
}

.distances>img {
  width: 75%;
  height: auto;
  border-radius: 0.5rem;
}

/* 5.3 Frames */
.orange-frame {
  position: absolute;
  background-color: var(--primary-blue) !important;
  z-index: 1;
  display: block;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.top-frame {
  background-color: var(--primary-blue) !important;
  top: -15px;
  left: 15px;
  width: 120px;
  height: 235px;
}

.bottom-frame {
  background-color: var(--primary-blue) !important;
  bottom: -15px;
  right: -13px;
  width: 234px;
  height: 423px;
}

.orange-box {
  background-color: var(--primary-blue) !important;
  margin-top: auto;
  padding: 15px 15px 35px;
  border-radius: 8px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.team-regards-container {
  background-color: var(--primary-blue) !important;
  padding: 15px 0;
  width: 100%;
  margin-top: 100px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.path {
  border-top: 2px dashed var(--primary-blue);
}

/* 5.4 Lists */
.info-list li,
.info-list_1 li {
  position: relative;
  padding-right: 20px;
  margin-bottom: 5px;
  list-style-type: none;
  font-size: 14px;
}

.info-list li::before {
  content: "•";
  color: #000;
  font-weight: 700;
  display: inline-block;
  width: 20px;
  font-size: 1.2em;
}

.feature-list,
.feature-list li::before {
  color: var(--bg-color);
}

.feature-list li {
  font-size: 20px;
}

/* 5.5 Info Items */
.info-item {
  display: block;
  font-weight: 400;
}

.info-label {
  color: var(--primary-blue);
  font-weight: 800;
  display: inline;
  margin-left: 5px;
  white-space: nowrap;
}

/* ==========================================
      6. SECTIONS
      ========================================== */
/* 6.1 Header */

/* 6.2 Info Sections */
.contat-section-title,
.contat-section-title-2 {
  margin-right: 0;
  transform: translate(0, -50px);
}

.contat-section-title-3,
.contat-section-title_h {
  margin-right: 285px;
  transform: translate(0, -50px);
}

/* 6.3 Team Sections */
.cancellation_policy {
  gap: 1rem;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

/* ==========================================
      7. DECORATIVE ELEMENTS
      ========================================== */
.airplane-icon_1,
.airplane-icon_2 {
  position: absolute;
  width: 237px;
  left: 0;
  top: 65rem;
}

.airplane-icon_2 {
  top: 168rem;
}

.flight-path {
  position: absolute;
  left: 0;
  top: 5%;
  z-index: -1;
  width: 180px;
}


.restaurant-name {
  font-weight: 700;
  color: var(--primary-blue) !important;
}

.restaurant-rating {
  font-weight: 700;
  color: var(--primary-blue) !important;
}


/* ==========================================
      8. RESPONSIVE STYLES
      ========================================== */
/* 8.1 Desktop (768px+) */
@media (min-width: 768px) {
  .container-flex {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: center;
  }

  .headline {
    line-height: 60px;
    font-size: 38px;
  }

  .headline::after {
    width: 25%;
  }

  .container-grid {
    grid-template-columns: 6fr 5fr;
  }

  .tour-description {
    font-weight: 700;
    font-size: 21px;
  }

  .top-frame {
    top: -15px;
    left: -20px;
    width: 234px;
    height: 423px;
  }

  .bottom-frame {
    bottom: -15px;
    right: -13px;
    width: 234px;
    height: 423px;
  }

  .team-regards {
    font-size: 22px;
  }

  .airplane-icon_1,
  .airplane-icon_2 {
    position: absolute;
    width: 237px;
    left: 0;
    top: 65rem;
  }

  .airplane-icon_2 {
    width: 230px;
    top: 168rem;
  }

  .airplane-icon_3 {
    position: absolute;
    width: 317px;
    right: 0;
    top: 200rem;
  }

  .airplane-icon_4,
  .airplane-icon_5 {
    position: absolute;
    width: 405px;
    left: 0;
    top: 232rem;
  }

  .airplane-icon_5 {
    top: 258rem;
  }

  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8 {
    position: absolute;
    width: 235px;
    left: 0;
    top: 284rem;
  }

  .airplane-icon_7,
  .airplane-icon_8 {
    top: 325rem;
  }

  .airplane-icon_8 {
    width: 405px;
    top: 355rem;
  }

  .section-title {
    font-size: 25px !important;
    padding: 0.3rem 7rem 0.3rem 2rem;
  }

  .section-title-hotail,
  .section-title_gaolh {
    font-size: 25px !important;
    padding: 0.3rem 10rem 0.3rem 2rem;
  }

  .section-title_gaolh {
    padding: 0.3rem 8rem 0.3rem 2rem;
  }

  .contat-section-title {
    margin-right: 365px;
  }

  .flight-path {
    top: 5%;
    width: 180px;
  }

  .info-list li {
    padding-right: 11rem;
  }

  .info-list_1 li {
    position: relative;
    padding-right: 1rem;
    margin-bottom: 0;
    list-style-type: none;
    font-size: 14px;
  }

  .info-list_1 li::before {
    content: "▪";
    color: #000;
    font-weight: 700;
    display: inline-block;
    width: 20px;
    margin-right: -20px;
    font-size: 1.2em;
  }

  .section-title {
    font-size: 23px !important;
    padding: 0.3rem 7rem 0.3rem 2rem;
  }

  .flight-path {
    top: 50%;
    width: 300px;
  }

  .info-list_1 li {
    font-size: 20px;
  }
}

@media (max-width: 990px) {
  body {
    font-size: 14px;
  }

  .headline {
    font-size: 24px;
    line-height: 1.4;
  }

  .headline::after {
    width: 40%;
  }

  .feature-list li {
    font-size: 15px;
  }

  .image-container {
    margin: 1rem 0;
  }

  .image-wrapper img {
    width: 100%;
    max-height: 400px;
  }

  .bottom-frame,
  .top-frame {
    display: block;
  }

  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .program-title,
  .secondary-title {
    font-size: 22px;
  }

  .program-title::after,
  .secondary-title::after {
    width: 20%;
  }

  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    margin-right: 0;
    transform: none;
    margin-bottom: 1rem;
  }

  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8 {
    display: none;
  }

  .cancellation_policy {
    gap: 1rem;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .info-list li {
    padding-right: 20px;
  }

  .info-list_1 li {
    padding-right: 10px;
    font-size: 12px;
  }

  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    font-size: 16px !important;
    padding: 0.3rem 2rem 0.3rem 1rem;
    border-radius: 25px 0 0 25px;
  }

  .distances>img {
    width: 100%;
  }

  .grid-container {
    grid-template-columns: 3fr 3fr;
    gap: 3rem;
  }

  .introduction {
    max-width: 100%;
    font-size: 16px;
  }

  .climate {
    width: 100%;
  }

  .flight-path {
    display: none;
  }

  .cancellation_policy {
    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }

  .container-flex {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .title {
    color: var(--primary-blue);
    font-weight: 700;
    font-size: 1.3rem;
    text-align: right;
    line-height: 60px;
    position: relative;
    margin-bottom: 1.3rem;
  }

  .title::after {
    content: "";
    display: block;
    width: 100px;
    height: 5px;
    background-color: var(--primary-blue);
    margin-top: 5px;
    margin-left: 0;
  }

  .headline {
    font-size: 20px;
  }

  .feature-grid {
    grid-template-columns: 2fr 3fr 2fr;
  }

  .icon-circle {
    width: 60px;
    height: 60px;
  }

  .icon-img {
    width: 30px;
    height: 30px;
  }

  .program-title,
  .secondary-title {
    font-size: 20px;
  }

  .tour-description {
    line-height: 2;
    text-align: justify;
    font-size: 0.9rem;
  }

  .orange-box {
    background-color: var(--primary-blue) !important;
    margin-top: auto;
    padding: 14px 15px 1px;
    border-radius: 8px;
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .top-frame {
    top: -15px;
    left: -15px;
    width: 75px;
    height: 108px;
  }

  .bottom-frame {
    background-color: var(--primary-blue) !important;
    bottom: -15px;
    right: -13px;
    width: 70px;
    height: 82px;
  }

  .container-flex {
    display: flex !important;
    flex-wrap: wrap !important;
    align-content: center !important;
    justify-content: center !important;
    align-items: center !important;
    flex-direction: column !important;
  }
}

/* ==========================================
      9. PRINT STYLES
      ========================================== */
@media print {
  @page {
    size: 290mm 200mm;
    margin: 0cm;
  }

  *,
  body,
  html {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  body,
  html {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    font-family: "Cairo", sans-serif !important;
    font-size: 10px;
    background-color: var(--bg-color) !important;
    color: #000 !important;
    overflow: visible !important;
  }

  /* ////////////////////////////////////////////////////////////////////// */

  .hero-banner {
    position: relative;
    width: 100%;
    height: 100 vh;
    background: url('../image/Clip\ path\ group.png') center/cover no-repeat;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  /* شعار 4 Seasons */
  .logo {
    position: absolute;
    top: 30px;
    right: 90rem;
    width: 80px;
    height: 80px;
    background: var(--bg-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }

  .logo-text {
    font-size: 12px;
    font-weight: 600;
    text-align: center;
  }

  .logo {
    width: 140px;
    height: 140px;
  }

  /* المحتوى الرئيسي */
  .hero-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
  }

  /* الجانب الأيسر - المحتوى العربي */
  .left-section {
    flex: 1;
    padding: 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: right;
    flex-wrap: wrap;
    align-content: center;
  }

  /* الجانب الأيمن - النص الإنجليزي */
  .right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding: 20px;
    position: relative;
    align-content: center;
    flex-wrap: wrap;
  }

  .magic-text {
    position: absolute;
    top: 1rem;
    right: 8rem;
    font-family: 'Cairo', sans-serif !important;
    font-size: 5rem !important;
    font-weight: 300 !important;
    margin-bottom: 0px !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  /* الشريط البنفسجي */

  .purple-strip {
    display: flex;
    background: linear-gradient(270deg, var(--dark-blue) 0%, var(--light-blue) 70%, rgba(74, 152, 255, 0.26) 100%);
    padding: 15px 35px 15px 150px !important;
    border-radius: 50px 0 0 50px !important;
    position: relative !important;
    margin-bottom: 10px !important;
    box-shadow: 0 8px 25px rgba(44, 65, 255, 0.3) !important;
    width: calc(207% + 50px) !important;
    margin-left: -61rem !important;
    align-content: center;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }

  .program-title {
    color: white;
    font-size: 32px;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
  }



  /* الزر البيضاوي */
  .duration-btn {
    background: white;
    padding: 15px 115px;
    border-radius: 50px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border: none;
    font-size: 18px;
    font-weight: 800;
    color: var(--light-blue);
    cursor: none;
    transition: all 0.3s ease;
  }



  .golden-text {
    color: var(--light-blue);
    font-weight: 800;
  }

  /* /////////////////////////////////////////////////////////////////////// */
  /* Container elements */
  .container,
  .container-fluid,
  .page {
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;
  }

  .container {
    padding: 3rem;
  }

  /* Section handling */
  section {
    page-break-before: always;
    page-break-inside: avoid !important;
    page-break-after: auto;
    break-inside: avoid;
    width: 100% !important;
    box-sizing: border-box;
  }

  section.home_page_image {
    page-break-before: avoid;
    padding: 0;
    margin: 0;
    height: 100%;
  }

  section.home_page_image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 9.2 Typography for Print */
  .page-title,
  .program-title,
  .secondary-title,
  .title,
  h1,
  h2,
  h3,
  h4 {
    font-size: 20px !important;
    font-weight: 700 !important;
    page-break-after: avoid;
    margin-top: 0.8em;
    margin-bottom: 0.5em;
  }


  .headline {

    line-height: 30px !important;
    color: var(--primary-blue) !important;
    margin-bottom: 1rem;
    page-break-after: avoid;
    font-size: 3rem !important;
    font-weight: 800 !important;
  }

  .headline::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 29%;
    height: 5px;
    background-color: var(--primary-blue) !important;
    display: block;
  }

  /* Title styles */
  .title {
    color: var(--primary-blue) !important;
    font-weight: 700;
    font-size: 30px !important;
    text-align: right;
    line-height: 30px;
    position: relative;
    margin-bottom: 0.3rem;
    font-family: "Cairo", sans-serif !important;
  }

  .title::after {
    content: "" !important;
    display: block !important;
    width: 100px !important;
    height: 3px !important;
    background-color: var(--primary-blue) !important;
    margin-top: 5px !important;
    margin-left: 0;
  }

  .page-title {
    color: var(--primary-blue) !important;
    font-size: 29px !important;
    margin-bottom: 15px;
    text-align: start;
  }


  .program-title,
  .secondary-title {
    margin-top: 50px !important;
    position: relative;
    padding-bottom: 10px;
    font-size: 50px !important;
    margin-bottom: 0.8rem;
  }

  .secondary-title {
    color: var(--primary-blue) !important;
  }

  .secondary-title::after {
    width: 13%;
    background-color: var(--primary-blue) !important;
  }

  /* Section titles */
  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    font-size: 30px !important;
    font-weight: 600 !important;
    background: linear-gradient(to right, var(--primary-blue), var(--sceond-primary-blue)) !important;
    display: inline-block;
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    color: var(--dark-blue) !important;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    page-break-after: avoid;
  }

  .section-title {
    padding: 0.2rem 4rem 0.2rem 1rem;
  }

  .section-title-hotail {
    padding: 0.2rem 6rem 0.2rem 1rem;
  }

  .section-title_gaolh {
    padding: 0.2rem 3rem 0.2rem 1rem;
  }

  /* Content text */
  .climate,
  .introduction,
  p {
    font-size: 10px !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .tour-description {
    font-size: 2rem !important;
    line-height: 1.6 !important;
    margin-left: 1rem;
    line-height: 1.5;
    page-break-inside: avoid;
  }

  .introduction {
    font-size: 20px !important;
    font-weight: 600 !important;
    line-height: 1.8 !important;
    text-align: justify !important;
    margin-bottom: 24px !important;
    max-width: 70% !important;
  }

  .climate {
    margin-top: 15px !important;
    font-size: 20px !important;
    text-align: justify !important;
    font-weight: 400 !important;
  }

  /* 9.3 Components for Print */
  /* Lists */
  ol,
  ul {
    padding-right: 20px;
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }

  li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    font-size: 10px !important;
  }

  .feature-list {
    color: var(--bg-color) !important;
    list-style: none;
    padding-right: 0;
  }

  .info-list li,
  .info-list_1 li {
    list-style-type: none;
    position: relative;
    padding-right: 25px;
    font-size: 10px !important;
  }

  .info-list li {
    font-size: 17px !important;
    font-weight: 600 !important;
    padding-right: 8rem !important;
  }

  .info-list_1 li {
    padding-right: 1rem !important;
    margin-bottom: 0 !important;
    font-size: 18px !important;
  }


  .info-list_1 li::before {
    content: "▪";
  }

  /* Features */
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
  }

  .icon-circle {
    background-color: var(--primary-blue) !important;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    margin-bottom: 5px;
    justify-content: center;
    display: flex;
    align-items: center;
  }

  .icon-img {
    width: 40px;
    height: 40px;
  }

  /* Information items */
  .info-item {
    font-size: 10px !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.3rem;
  }

  .info-label {
    color: var(--primary-blue) !important;
    font-weight: 700 !important;
    font-size: 10px !important;
    display: inline;
    margin-left: 5px;
  }

  /* Orange elements */
  .orange-box {
    background-color: var(--primary-blue) !important;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    page-break-inside: avoid;
    margin-bottom: 1rem;
  }

  .orange-box .feature-list {
    margin-bottom: 0;
  }

  .feature-list li {
    font-size: 19px !important;
  }

  .orange-box .feature-list li {
    margin-bottom: 0.3rem !important;
    ;
  }

  .content-section {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: flex-start;
    align-items: center;
  }

  .image-wrapper img {
    height: auto;
    width: 100%;
    object-fit: contain;
    display: block;
  }

  .top-frame {
    display: block;
    top: -5px;
    left: 60px;
    width: 50% !important;
    height: 60% !important;
    max-width: 80px;
    max-height: 150px;
  }

  .bottom-frame {
    display: block;
    bottom: -10px;
    right: 60px;
    width: 30%;
    height: 35%;
    max-width: 100px;
    max-height: 120px;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4rem;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
    page-break-inside: avoid;
  }

  .info-grid {
    font-weight: 700 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    font-size: 20px !important;
    text-align: right !important;
    display: block !important;
    margin-bottom: 1rem !important;
    page-break-inside: avoid !important;
  }

  /* Features */
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
  }

  .feature-text {
    font-size: 16px !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .container {
    padding: 3rem !important;
  }

  /* Information items */
  .info-item {
    font-size: 20px !important;
    line-height: 1.6 !important;
    text-align: justify !important;
    margin-bottom: 0.3rem !important;
  }

  .info-label {
    color: var(--primary-blue) !important;
    font-weight: 700 !important;
    font-size: 20px !important;
    display: inline;
    margin-left: 5px;
  }

  /* Orange elements */
  .orange-box {
    background-color: var(--primary-blue) !important;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    page-break-inside: avoid;
    margin-bottom: 1rem;
  }

  .orange-box .feature-list {
    margin-bottom: 0;
  }

  .orange-frame {
    position: absolute;
    background-color: var(--primary-blue) !important;
    z-index: 1;
    display: none;
  }

  /* Images */
  .image-container {
    width: 100%;
    position: relative;
    page-break-inside: avoid;
  }

  .image-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    height: auto;
  }



  .distances img {
    width: 90%;
    max-width: 200px;
    height: auto;
  }

  /* Restaurant items */
  .restaurant-item {
    font-size: 23px !important;
    margin-bottom: 0.4rem;
    page-break-inside: avoid;
  }

  .restaurant-name {
    font-weight: 700;
    color: var(--primary-blue) !important;
  }

  .restaurant-rating {
    font-weight: 700;
    color: var(--primary-blue) !important;
  }

  .restaurant-desc {
    color: var(--primary-blue);
    font-size: 24px !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  /* Contact and team sections */
  .cancellation_policy {
    gap: 1rem;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .cancellation_policy .info-list {
    margin-top: 0.5rem;
  }

  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    transform: none !important;
    margin-right: 0 !important;
    margin-bottom: 1rem;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    margin-bottom: 1.5rem !important;
  }

  .contat-section-title,
  .contat-section-title_h {
    margin-right: 13rem;
    transform: translate(0, -50px);
  }

  .contat-section-title {
    margin-right: 23rem;
    transform: translate(0, -50px);
  }

  .team-regards-container {
    background-color: var(--primary-blue) !important;
    color: var(--bg-color) !important;
    padding: 0.5cm 1cm;
    width: 100%;
    margin-top: 1cm;
    page-break-before: auto;
    page-break-inside: avoid;
    box-sizing: border-box;
  }

  .team-regards {
    color: var(--bg-color) !important;
    font-weight: 600 !important;
    text-align: center;
    font-size: 11px !important;
    margin: 0;
  }

  /* Hide elements */
  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8,
  .flight-path {
    display: none !important;
  }

  .content-section {
    order: 1;
  }

  .distances {
    text-align: center;
  }

  .container-fluid {
    width: 80%;
  }

  .orange-frame {
    display: block;
    position: absolute;
    background-color: var(--primary-blue) !important;
    z-index: 1;
  }



}