@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap");

:root {
  --primary-green: #8cc63f;
  --primary-orange: #ff914d;
  --primary-dark: #000;
  --bg-color: #fff;
}

/* Reset and Base Styles */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  padding: 0;
  font-family: "Cairo", sans-serif;
  font-size: 16px;
  overflow-x: hidden;
}

/* Common Element Styles */
.headline {
  line-height: 40px;
  color: #7cb518;
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

.headline::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60%;
  height: 5px;
  background-color: #7cb518;
}

.tour-description {
  line-height: 2;
  text-align: justify;
  font-size: 1rem;
}

/* Program and Section Styles */
.program-title {
  color: #7cb518;
  font-size: 26px;
}

.program-title::after {
  background-color: #7cb518;
  display: block;
}

.secondary-title {
  color: #ff7e3d;
  font-size: 26px;
}

.secondary-title::after {
  background-color: #ff7e3d;
  display: block;
}

/* Feature and Grid Layouts */
.container-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  justify-items: end;
  align-items: center;
  justify-content: center;
  align-content: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  flex: 1;
}

.feature-text {
  font-size: 13px;
}

.feature-list {
  color: white;
}

.feature-list li {
  font-size: 14px;
}

.feature-list li::before {
  color: white;
}

/* Icon and Visual Elements */
.icon-circle {
  background-color: #8bc12a;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 10px;
}

.icon-img {
  width: 40px;
  height: 40px;
}

.path {
  border-top: 2px dashed #ff9a56;
}

/* Image Container and Frames */
.image-container {
  position: relative;
  margin: 3rem 5rem 0rem 0rem;
}

.image-wrapper {
  position: relative;
  z-index: 2;
}

.image-wrapper img {
  width: 90%;
  max-height: 750px;
  object-fit: cover;
}

.orange-frame {
  position: absolute;
  background-color: #ff7e3d !important;
  z-index: 1;
  display: block;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.top-frame {
  top: -15px;
  left: 15px;
  width: 120px;
  height: 235px;
}

.bottom-frame {
  bottom: -12px;
  right: -11px;
  width: 135px;
  height: 175px;
}

/* Orange Box and Team Section */
.orange-box {
  background-color: #ff9a56 !important;
  margin-top: auto;
  padding: 15px 15px 35px 15px;
  border-radius: 8px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.team-regards-container {
  background-color: #ff7e3d !important;
  padding: 15px 0;
  width: 100%;
  margin-top: 100px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.team-regards {
  color: white !important;
  font-weight: 700;
  text-align: center;
  font-size: 18px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

/* Contact Section Styles */
.contat-section-title {
  margin-right: 0rem;
  transform: translate(0px, -50px);
}

.contat-section-title-2 {
  margin-right: 0rem;

  transform: translate(0px, -50px);
}

.contat-section-title-3 {
  margin-right: 30rem;

  transform: translate(0px, -50px);
}

.contat-section-title_h {
  margin-right: 30rem;
  transform: translate(0px, -50px);
}

/* Airplane Icon */
.airplane-icon_1 {
  position: absolute;
  width: 237px;
  left: 0;
  top: 65rem;
}

/* Airplane Icon */
.airplane-icon_2 {
  position: absolute;
  width: 237px;
  left: 0;
  top: 168rem;
}

/* Feature Item Styles */
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

/* Desktop/Tablet Media Query */
@media (min-width: 768px) {
  .headline {
    line-height: 60px;
    font-size: 38px;
  }

  .headline::after {
    width: 25%;
  }

  .container-grid {
    grid-template-columns: 6fr 5fr;
    align-items: center;
  }

  .tour-description {
    font-weight: 700;
    font-size: 21px;
  }

  .top-frame {
    top: -15px;
    left: 35px;
    width: 234px;
    height: 423px;
  }

  .bottom-frame {
    bottom: -15px;
    right: -13px;
    width: 234px;
    height: 423px;
  }



  .team-regards {
    font-size: 22px;
  }

  .contat-section-title {
    margin-right: 40rem;
  }


  /* Airplane Icon */
  .airplane-icon_1 {
    position: absolute;
    width: 237px;
    left: 0;
    top: 65rem;
  }

  /* Airplane Icon */
  .airplane-icon_2 {
    position: absolute;
    width: 230px;
    left: 0;
    top: 168rem;
  }

  .airplane-icon_3 {
    position: absolute;
    width: 317px;
    right: 0px;
    top: 200rem;
  }

  .airplane-icon_4 {
    position: absolute;
    width: 405px;
    left: 0px;
    top: 232rem;
  }

  .airplane-icon_5 {
    position: absolute;
    width: 405px;
    left: 0px;
    top: 258rem;
  }

  .airplane-icon_6 {
    position: absolute;
    width: 235px;
    left: 0px;
    top: 284rem;
  }

  .airplane-icon_7 {
    position: absolute;
    width: 235px;
    left: 0px;
    top: 325rem;
  }

  .airplane-icon_8 {
    position: absolute;
    width: 405px;
    left: 0px;
    top: 355rem;
  }

  .cancellation_policy {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
  }

  .program-title {
    color: #7cb518;
    font-size: 26px;
    position: relative;
    padding-bottom: 10px;
  }

  .program-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 6%;
    height: 4px;
    background-color: #7cb518;
  }

  .secondary-title {
    color: #ff7e3d;
    font-size: 26px;
    position: relative;
    padding-bottom: 10px;
  }

  .secondary-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 6%;
    height: 4px;
    background-color: #ff7e3d;
  }

}

.page {
  width: 100%;
  height: 100%;
  padding: 20mm 25mm;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.page-title {
  color: var(--primary-green);
  font-weight: 800;
  font-size: 26px;
  text-align: right;
  margin-bottom: 8px;
  position: relative;
}

.page-title::after {
  content: "";
  display: block;
  width: 200px;
  height: 4px;
  background-color: var(--primary-green);
  margin: 6px 0 20px;
}

.introduction {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 24px;
  max-width: 50%;
}

.info-grid {
  font-weight: bold;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 15px;
  text-align: right;
}

.info-item {
  display: block;
  font-weight: 400;
}

.info-label {
  color: var(--primary-orange);
  font-weight: 800;
  display: inline;
  margin-left: 5px;
  white-space: nowrap;
}

.climate {
  width: 40rem;
  margin-top: 15px;
  font-size: 14.5px;
  text-align: justify;
  font-weight: 400;
}

.climate strong {
  color: var(--primary-orange);
  font-weight: bold;
}



.section-title {
  color: #6ba123;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  border-bottom: 3px solid #6ba123;
  display: inline-block;
  /* font-family: 'Cairo', sans-serif; */
  /* Font already set */
}

.distances>img {
  width: 75%;
  height: auto;
  border-radius: 0.5rem;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem;
}

.title {
  color: #74a100;
  font-weight: bold;
  font-size: 2.3rem;
  text-align: right;
  line-height: 60px;
  position: relative;
  margin-bottom: 1.3rem;
  /* font-family: 'Cairo', sans-serif; */
  /* Font already set */
}

.title::after {
  content: "";
  display: block;
  width: 240px;
  height: 5px;
  background-color: #74a100;
  margin-top: 5px;
  margin-left: 0;
}

.section-title {
  /* font-size: 15px !important; */
  /* Potential conflict, review */
  font-weight: 500;
  background: linear-gradient(to right, #ffa771, #ffe8dc);
  display: inline-block;
  padding: 0.3rem 11rem 0.3rem 2rem;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: bold;
  color: #000;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.section-title-hotail {
  font-size: 15px !important;
  /* Using !important, be careful */
  font-weight: 500;
  background: linear-gradient(to right, #ffa771, #ffe8dc);
  display: inline-block;
  padding: 0.3rem 15rem 0.3rem 2rem;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: bold;
  color: #000;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.section-title_gaolh {
  font-size: 15px !important;
  /* Using !important, be careful */
  font-weight: 500;
  background: linear-gradient(to right, #ffa771, #ffe8dc);
  display: inline-block;
  padding: 0.3rem 8rem 0.3rem 2rem;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: bold;
  color: #000;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.cancellation_policy {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.cancellation_policy .info-list {
  margin-top: 5px;
}

.flight-path {
  position: absolute;
  left: 0;
  top: 5%;
  z-index: -1;
  width: 180px;
}

/* Adding bullet points for list items */
.info-list li {
  position: relative;
  padding-right: 20px;
  margin-bottom: 5px;
  list-style-type: none;
  font-size: 14px;
}

.info-list li::before {
  content: "•";
  color: #000;
  font-weight: bold;
  display: inline-block;
  width: 20px;
  margin-right: -20px;
  font-size: 1.2em;
}

.container-content {
  margin-top: 0;
  padding-top: 0;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

/* === Media query (min-width: 768px) from Important_notes_1.css === */
@media (min-width: 768px) {
  /* .title { ... } */
  /* Potential conflict, review */
  /* .title::after { ... } */
  /* Potential conflict, review */

  .section-title {
    font-size: 25px !important;
    /* Using !important, be careful */
    /* Other properties potentially conflict, review */
    padding: 0.3rem 11rem 0.3rem 2rem;
  }

  .section-title-hotail {
    font-size: 25px !important;
    /* Using !important, be careful */
    /* Other properties potentially conflict, review */
    padding: 0.3rem 15rem 0.3rem 2rem;
  }

  .section-title_gaolh {
    font-size: 25px !important;
    /* Using !important, be careful */
    /* Other properties potentially conflict, review */
    padding: 0.3rem 8rem 0.3rem 2rem;
  }

  /* .cancellation_policy { ... } */
  /* Potential conflict, review */
  /* .cancellation_policy .info-list { ... } */
  /* Potential conflict, review */

  .contat-section-title {
    margin-right: 22rem;
    /* Potential conflict, review */
    /* transform: translate(0px, -50px); */
    /* Potential conflict, review */
  }

  .flight-path {
    /* position: absolute; */
    /* Already set */
    /* left: 0; */
    /* Already set */
    top: 5%;
    /* z-index: -1; */
    /* Already set */
    width: 180px;
  }
}



@media (min-width: 768px) {

  /* Adding bullet points for list items */
  .info-list li {
    /* position: relative; */
    /* Already set */
    padding-right: 11rem;
    /* Potential conflict, review */
    /* margin-bottom: 5px; */
    /* Already set */
    /* list-style-type: none; */
    /* Already set */
    /* font-size: 14px; */
    /* Already set */
  }

  /* Adding bullet points for list items */
  .info-list_1 li {
    position: relative;
    padding-right: 1rem;
    margin-bottom: 0px;
    list-style-type: none;
    font-size: 14px;
  }

  .info-list_1 li::before {
    content: "▪";
    color: #000;
    font-weight: bold;
    display: inline-block;
    width: 20px;
    margin-right: -20px;
    font-size: 1.2em;
  }

  /* .info-list li::before { ... } */
  /* Potential conflict, review */
  /* .container-content { ... } */
  /* Potential conflict, review */
}

.cancellation_policy {
  flex-direction: column;
  align-content: flex-start;
  justify-content: center;
}

@media (min-width: 768px) {
  /* .title { ... } */
  /* Potential conflict, review */
  /* .title::after { ... } */
  /* Potential conflict, review */

  .section-title {
    font-size: 40px !important;
    /* Potential conflict, review */
    /* Other properties potentially conflict, review */
    padding: 0.3rem 7rem 0.3rem 2rem;
  }

  .cancellation_policy {
    /* display: flex; */
    /* Already set */
    /* flex-wrap: wrap; */
    /* Already set */
    /* align-items: center; */
    /* Already set */
    flex-direction: column;
    align-content: flex-start;
    justify-content: center;
  }

  /* .cancellation_policy .info-list { ... } */
  /* Potential conflict, review */

  .flight-path {
    /* position: absolute; */
    /* Already set */
    /* left: 0; */
    /* Already set */
    top: 50%;
    /* Potential conflict, review */
    /* z-index: -1; */
    /* Already set */
    width: 300px;
    /* Potential conflict, review */
  }

  /* .info-list li { ... } */
  /* Potential conflict, review */

  .info-list_1 li {
    /* position: relative; */
    /* Already set */
    /* padding-right: 1rem; */
    /* Already set */
    /* margin-bottom: 0px; */
    /* Already set */
    /* list-style-type: none; */
    /* Already set */
    font-size: 20px;
    /* Potential conflict, review */
  }

  /* .info-list_1 li::before { ... } */
  /* Potential conflict, review */
  /* .info-list li::before { ... } */
  /* Potential conflict, review */
  /* .container-content { ... } */
  /* Potential conflict, review */
}


/* === End of appended content from Important_notes_3.css === */

/* === Adding General Responsive Styles === */
@media (max-width: 767px) {

  /* Adjustments for smaller screens */
  body {
    font-size: 14px;
    /* Slightly smaller base font */
  }

  .headline {
    font-size: 24px;
    /* Adjust headline size */
    line-height: 1.4;
  }

  .headline::after {
    width: 40%;
    /* Adjust underline */
  }

  .container-grid {
    grid-template-columns: 1fr;
    /* Stack grid items */
    gap: 1rem;
  }

  .image-container {
    margin: 1rem 0;
    /* Adjust image margins */
  }

  .image-wrapper img {
    width: 100%;
    /* Make image full width */
    max-height: 400px;
    /* Limit image height */
  }

  .top-frame,
  .bottom-frame {
    display: none;
    /* Hide decorative frames on small screens */
  }

  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    /* 2 columns for features */
    gap: 10px;
  }

  .program-title,
  .secondary-title {
    font-size: 22px;
    /* Adjust title sizes */
  }

  .program-title::after,
  .secondary-title::after {
    width: 20%;
    /* Adjust underline */
  }

  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    margin-right: 0;
    /* Remove large margins */
    transform: none;
    /* Remove transforms */
    margin-bottom: 1rem;
  }

  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8 {
    display: none;
    /* Hide decorative airplanes */
  }

  .cancellation_policy {
    gap: 1rem;
    /* Reduce gap */
    flex-direction: column;
    /* Stack items */
    align-items: flex-start;
  }

  .info-list li {
    padding-right: 20px;
    /* Reset padding */
  }

  .info-list_1 li {
    padding-right: 10px;
    /* Reset padding */
    font-size: 12px;
  }

  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    font-size: 16px !important;
    /* Adjust font size */
    padding: 0.3rem 2rem 0.3rem 1rem;
    /* Adjust padding */
    border-radius: 25px 0 0 25px;
    /* Simplify radius */
  }

  .distances>img {
    width: 100%;
    /* Full width image */
  }

  .grid-container {
    grid-template-columns: 1fr;
    /* Stack grid items */
    gap: 1rem;
  }

  .introduction {
    max-width: 100%;
    /* Full width intro */
    font-size: 16px;
  }

  .climate {
    width: 100%;
    /* Full width climate info */
  }

  .flight-path {
    display: none;
    /* Hide flight path */
  }
}

/* Further adjustments for very small screens if needed */
@media (max-width: 480px) {
  .headline {
    font-size: 20px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    /* Single column for features */
  }

  .icon-circle {
    width: 60px;
    height: 60px;
  }

  .icon-img {
    width: 30px;
    height: 30px;
  }

  .program-title,
  .secondary-title {
    font-size: 20px;
  }
}


@media print {

  /* Ensure exact colors are printed */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Page setup: A4 Landscape with 1cm margins */
  @page {
    size: A4 landscape;
    margin: 1cm;
  }

  /* Base body styles for print */
  body,
  html {
    margin: 0 !important;
    /* Override any conflicting margins */
    padding: 0 !important;
    /* Override any conflicting padding */
    width: 100% !important;
    height: 100% !important;
    font-family: "Cairo", sans-serif !important;
    /* Ensure correct font */
    font-size: 10pt;
    /* Base font size for print - adjust as needed */
    background-color: #ffffff !important;
    /* Ensure white background */
    color: #000 !important;
    /* Default text color */
    overflow: visible !important;
    /* Show all content */
  }

  /* Hide elements not suitable for print */
  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8,
  .flight-path {
    display: none !important;
    /* Hide decorative elements */
  }

  /* General Layout & Spacing */
  section {
    page-break-before: always;
    /* Start each section on a new page */
    page-break-inside: avoid;
    /* Try to keep sections together */
    padding: 0.5cm 0;
    /* Add some padding top/bottom */
    width: 100% !important;
    /* Ensure full width */
    box-sizing: border-box;
  }

  section.home_page_image {
    /* Special handling for cover page */
    page-break-before: avoid;
    /* Don't break before the first page */
    padding: 0;
    margin: 0;
    height: 100%;
    /* Try to make cover fill page */
  }

  section.home_page_image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* Cover the page */
  }

  .container,
  .container-fluid,
  .page {
    padding: 0 !important;
    /* Remove default container padding */
    margin: 0 auto !important;
    /* Center content */
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;
  }

  /* Typography */
  h1,
  h2,
  h3,
  h4,
  .title,
  .page-title,
  .headline,
  .program-title,
  .secondary-title {
    page-break-after: avoid;
    /* Keep headings with content */
    margin-top: 0.8em;
    margin-bottom: 0.5em;
    font-weight: 700 !important;
    /* Consistent bold headings */
  }

  .title {
    /* Main titles like 'Distances', 'Restaurants' */
    color: #74a100 !important;
    font-size: 16pt !important;
    text-align: right;
    position: relative;
    padding-bottom: 5px;
    margin-bottom: 1rem;
  }

  .title::after {
    content: "";
    display: block;
    width: 150px;
    /* Adjust width as per ideal PDF */
    height: 3px;
    background-color: #74a100 !important;
    margin-top: 5px;
    margin-left: auto;
    /* Align right for RTL */
    margin-right: 0;
  }

  .page-title {
    /* e.g., 'General Information' */
    color: #7cb518 !important;
    /* Match color */
    font-size: 18pt !important;
    text-align: right;
    padding-bottom: 5px;
    margin-bottom: 1rem;
  }

  .page-title::after {
    content: "";
    display: block;
    width: 180px;
    /* Adjust width */
    height: 3px;
    background-color: #7cb518 !important;
    margin-top: 5px;
    margin-left: auto;
    /* Align right for RTL */
    margin-right: 0;
  }

  .program-title,
  .secondary-title {
    /* 'Includes', 'Excludes' */
    font-size: 14pt !important;
    padding-bottom: 5px;
    margin-bottom: 0.8rem;
    position: relative;
  }

  .program-title {
    color: #7cb518 !important;
  }

  .secondary-title {
    color: #ff7e3d !important;
  }

  .program-title::after,
  .secondary-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    height: 3px;
    width: 80px;
    /* Adjust width */
  }

  .program-title::after {
    background-color: #7cb518 !important;
  }

  .secondary-title::after {
    background-color: #ff7e3d !important;
  }

  .headline {
    /* 'Day 1: Tbilisi City Tours' */
    color: #7cb518 !important;
    font-size: 14pt !important;
    line-height: 1.4;
    padding-bottom: 5px;
    margin-bottom: 0.8rem;
    position: relative;
    width: auto;
    /* Allow natural width */
    display: inline-block;
    /* Fit content */
  }

  .headline::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50%;
    /* Adjust width */
    height: 3px;
    background-color: #7cb518 !important;
  }

  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    /* Restaurant sections */
    font-size: 12pt !important;
    font-weight: 600 !important;
    background: linear-gradient(to right, #ffa771, #ffe8dc) !important;
    display: inline-block;
    padding: 0.2rem 4rem 0.2rem 1rem;
    /* Adjust padding */
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    color: #000 !important;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    page-break-after: avoid;
  }

  /* Adjust padding based on specific class if needed */
  .section-title {
    padding: 0.2rem 4rem 0.2rem 1rem;
  }

  .section-title-hotail {
    padding: 0.2rem 6rem 0.2rem 1rem;
  }

  .section-title_gaolh {
    padding: 0.2rem 3rem 0.2rem 1rem;
  }

  p,
  .tour-description,
  .introduction,
  .climate,
  .info-item,
  .feature-text,
  .restaurant-desc {
    font-size: 10pt !important;
    /* Consistent paragraph font size */
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .tour-description {
    margin-left: 0;
  }

  /* Remove margin for print */
  .introduction {
    max-width: 100%;
  }

  /* Allow full width */
  .climate {
    width: 100%;
  }

  /* Allow full width */

  /* Lists */
  ul,
  ol {
    padding-right: 20px;
    /* Indentation for RTL */
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }

  li {
    font-size: 10pt !important;
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  .feature-list {
    color: white !important;
    list-style: none;
    padding-right: 0;
  }

  .feature-list li {
    font-size: 10pt !important;
  }

  .info-list li,
  .info-list_1 li {
    list-style-type: none;
    position: relative;
    padding-right: 25px;
    /* Space for bullet */
    font-size: 10pt !important;
  }

  .info-list li::before,
  .info-list_1 li::before {
    content: "•";
    color: #000 !important;
    font-weight: bold;
    display: inline-block;
    position: absolute;
    right: 0;
    /* Position bullet for RTL */
    top: 0;
    width: 20px;
    text-align: center;
    font-size: 1.2em;
  }

  .info-list_1 li::before {
    content: "▪";
  }

  /* Square bullet */

  /* Specific Sections & Layouts */

  /* Features Section ('Includes') */
  .feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* 4 columns */
    gap: 1rem;
    /* Adjust gap */
    margin-bottom: 1.5rem;
    page-break-inside: avoid;
  }

  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
    /* Remove bottom margin */
  }

  .icon-circle {
    background-color: #8bc12a !important;
    width: 50px;
    /* Smaller icons */
    height: 50px;
    border-radius: 50%;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-img {
    width: 25px;
    /* Smaller icon image */
    height: 25px;
  }

  .feature-text {
    font-size: 8pt !important;
    /* Smaller text */
    line-height: 1.4 !important;
  }

  /* Excludes Section */
  .orange-box {
    background-color: #ff9a56 !important;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    page-break-inside: avoid;
  }

  .orange-box .feature-list {
    margin-bottom: 0;
    /* Remove list bottom margin */
  }

  .orange-box .feature-list li {
    margin-bottom: 0.3rem;
    /* Adjust spacing */
  }

  /* Day Description Section (Text + Image) */
  .container-grid {
    display: grid;
    grid-template-columns: 60% 35%;
    /* Adjust ratio as per ideal PDF */
    gap: 5%;
    align-items: start;
    /* Align items to the top */
    page-break-inside: avoid;
    width: 100%;
    max-width: 100%;
  }

  .content-section {
    order: 1;
  }

  /* Text first */
  .image-container {
    order: 2;
    margin: 0;
    padding: 0;
    position: relative;
  }

  /* Image second */

  .image-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    height: auto;
  }

  .image-wrapper img {
    width: 100%;
    /* Make image fill its container */
    max-width: 100%;
    height: auto;
    object-fit: contain;
    display: block;
    border: 1px solid #eee;
    /* Optional border */
  }

  .orange-frame {
    display: none;
    /* Hide frames in print */
  }

  /* General Info Section */
  .page {
    /* Container for general info */
    display: block;
    /* Reset flex */
    padding: 0 !important;
  }

  .info-grid {
    display: block;
    /* Reset grid/flex */
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }

  .info-item {
    font-size: 10pt !important;
    line-height: 1.6;
    margin-bottom: 0.3rem;
  }

  .info-label {
    font-weight: 700 !important;
    color: #ff914d !important;
    /* Use orange from ideal PDF */
    display: inline;
    margin-left: 5px;
    font-size: 10pt !important;
  }

  /* Distances Section */
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 3 columns */
    gap: 1rem;
    /* Adjust gap */
    page-break-inside: avoid;
  }

  .distances {
    text-align: center;
  }

  .distances img {
    width: 90%;
    /* Adjust image size */
    max-width: 200px;
    /* Limit max width */
    height: auto;
    border-radius: 0.5rem;
    border: 1px solid #eee;
    /* Optional border */
  }

  /* Restaurants Section */
  .restaurant-item {
    font-size: 10pt !important;
    margin-bottom: 0.4rem;
    page-break-inside: avoid;
  }

  .restaurant-name {
    font-weight: 600;
    color: #333;
  }

  .restaurant-rating {
    font-weight: 600;
    color: #74a100 !important;
  }

  .restaurant-desc {
    color: #555;
  }

  /* Cancellation Policy / Important Notes */
  .cancellation_policy {
    display: block;
    /* Simple block layout */
    page-break-inside: avoid;
  }

  .cancellation_policy .info-list {
    margin-top: 0.5rem;
  }

  /* Team Regards Footer */
  .team-regards-container {
    background-color: #ff7e3d !important;
    color: white !important;
    padding: 0.5cm 1cm;
    width: 100%;
    margin-top: 1cm;
    page-break-before: auto;
    /* Allow break before if needed */
    page-break-inside: avoid;
    box-sizing: border-box;
  }

  .team-regards {
    color: white !important;
    font-weight: 600 !important;
    text-align: center;
    font-size: 11pt !important;
    margin: 0;
  }

  /* Remove transforms used for screen layout */
  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    transform: none !important;
    margin-right: 0 !important;
    margin-bottom: 1rem;
    page-break-inside: avoid !important;
  }


}