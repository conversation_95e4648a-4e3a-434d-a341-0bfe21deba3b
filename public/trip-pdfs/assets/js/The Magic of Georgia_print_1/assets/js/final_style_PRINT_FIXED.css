/* ==========================================
   1. FONTS & RESET
   ========================================== */
@import url(https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap);

:root {
  --primary-green: #8cc63f;
  --primary-orange: #ff914d;
  --primary-dark: #000;
  --secondary-green: #7cb518;
  --secondary-orange: #ff7e3d;
  --light-orange: #ff9a56;
  --bg-color: #fff;
}

*,
body {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Cairo", sans-serif;
  font-size: 16px;
  overflow-x: hidden;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--primary-dark);
}

/* ==========================================
      2. COMMON STYLES & VARIABLES
      ========================================== */
.page {
  padding: 5mm 2mm;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.container-content {
  margin-top: 0;
  padding-top: 0;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

/* ==========================================
      3. TYPOGRAPHY
      ========================================== */
/* 3.1 Headings */
.headline {
  line-height: 40px;
  color: var(--secondary-green);
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

.headline::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60%;
  height: 5px;
  background-color: var(--secondary-green);
}

.page-title {
  color: var(--secondary-green);
  font-weight: 800;
  font-size: 26px;
  text-align: right;
  margin-bottom: 8px;
  position: relative;
}

.page-title::after {
  content: "";
  display: block;
  width: 200px;
  height: 4px;
  background-color: var(--secondary-green);
  margin: 6px 0 20px;
}

.title {
  color: #74a100;
  font-weight: 700;
  font-size: 2.3rem;
  text-align: right;
  line-height: 60px;
  position: relative;
  margin-bottom: 1.3rem;
}

.title::after {
  content: "";
  display: block;
  width: 240px;
  height: 5px;
  background-color: #74a100;
  margin-top: 5px;
  margin-left: 0;
}

.program-title,
.secondary-title {
  font-size: 26px;
  position: relative;
  padding-bottom: 10px;
}

.program-title {
  color: var(--secondary-green);
}

.secondary-title {
  color: var(--secondary-orange);
}

.program-title::after,
.secondary-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 6%;
  height: 4px;
}

.program-title::after {
  background-color: var(--secondary-green);
}

.secondary-title::after {
  background-color: var(--secondary-orange);
}

.section-title,
.section-title-hotail,
.section-title_gaolh {
  background: linear-gradient(to right, #ffa771, #ffe8dc);
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  color: #000;
  font-weight: 700;
  display: inline-block;
}

.section-title {
  padding: 0.3rem 11rem 0.3rem 2rem;
}

.section-title-hotail {
  font-size: 15px !important;
  padding: 0.3rem 15rem 0.3rem 2rem;
}

.section-title_gaolh {
  font-size: 15px !important;
  padding: 0.3rem 8rem 0.3rem 2rem;
}

/* 3.2 Text Elements */
.tour-description {
  line-height: 2;
  text-align: justify;
  font-size: 1rem;
}

.introduction {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 24px;
  max-width: 50%;
}

.climate {
  width: 40rem;
  margin-top: 15px;
  font-size: 14.5px;
  text-align: justify;
  font-weight: 400;
}

.climate strong {
  color: var(--primary-orange);
  font-weight: 700;
}

.feature-text {
  font-size: 13px;
}

.team-regards {
  color: #fff !important;
  font-weight: 700;
  text-align: center;
  font-size: 18px;
}

/* ==========================================
      4. LAYOUT
      ========================================== */
/* 4.1 Containers */
.container-grid {
  display: grid;
  grid-template-columns: 6fr 5fr;
  gap: 2rem;
  justify-items: center;
  align-items: center;
  justify-content: center;
  align-content: center;
}

/* 4.2 Grids */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  flex: 1;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem;
}

.info-grid {
  font-weight: 700;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 15px;
  text-align: right;
}

/* ==========================================
      5. COMPONENTS
      ========================================== */
/* 5.1 Features */
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

.icon-circle {
  background-color: #8bc12a;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 10px;
}

.icon-img {
  width: 40px;
  height: 40px;
}

/* 5.2 Images */
.image-container {
  position: relative;
  margin: 3rem 5rem 0 0;
}

.image-wrapper {
  position: relative;
  z-index: 2;
}

.image-wrapper img {
  width: 90%;
  max-height: 750px;
  object-fit: cover;
}

.distances>img {
  width: 75%;
  height: auto;
  border-radius: 0.5rem;
}

/* 5.3 Frames */
.orange-frame {
  position: absolute;
  background-color: var(--secondary-orange) !important;
  z-index: 1;
  display: block;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.top-frame {
  background-color: #ff7e3d !important;
  top: -15px;
  left: 15px;
  width: 120px;
  height: 235px;
}

.bottom-frame {
  background-color: #ff7e3d !important;
  bottom: -15px;
  right: -13px;
  width: 234px;
  height: 423px;
}

.orange-box {
  background-color: var(--light-orange) !important;
  margin-top: auto;
  padding: 15px 15px 35px;
  border-radius: 8px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.team-regards-container {
  background-color: var(--secondary-orange) !important;
  padding: 15px 0;
  width: 100%;
  margin-top: 100px;
  print-color-adjust: exact !important;
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
}

.path {
  border-top: 2px dashed var(--light-orange);
}

/* 5.4 Lists */
.info-list li,
.info-list_1 li {
  position: relative;
  padding-right: 20px;
  margin-bottom: 5px;
  list-style-type: none;
  font-size: 14px;
}

.info-list li::before {
  content: "•";
  color: #000;
  font-weight: 700;
  display: inline-block;
  width: 20px;
  margin-right: -20px;
  font-size: 1.2em;
}

.feature-list,
.feature-list li::before {
  color: #fff;
}

.feature-list li {
  font-size: 14px;
}

/* 5.5 Info Items */
.info-item {
  display: block;
  font-weight: 400;
}

.info-label {
  color: var(--primary-orange);
  font-weight: 800;
  display: inline;
  margin-left: 5px;
  white-space: nowrap;
}

/* ==========================================
      6. SECTIONS
      ========================================== */
/* 6.1 Header */

/* 6.2 Info Sections */
.contat-section-title,
.contat-section-title-2 {
  margin-right: 0;
  transform: translate(0, -50px);
}

.contat-section-title-3,
.contat-section-title_h {
  margin-right: 285px;
  transform: translate(0, -50px);
}

/* 6.3 Team Sections */
.cancellation_policy {
  gap: 1rem;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

/* ==========================================
      7. DECORATIVE ELEMENTS
      ========================================== */
.airplane-icon_1,
.airplane-icon_2 {
  position: absolute;
  width: 237px;
  left: 0;
  top: 65rem;
}

.airplane-icon_2 {
  top: 168rem;
}

.flight-path {
  position: absolute;
  left: 0;
  top: 5%;
  z-index: -1;
  width: 180px;
}

/* ==========================================
      8. RESPONSIVE STYLES
      ========================================== */
/* 8.1 Desktop (768px+) */
@media (min-width: 768px) {
  .headline {
    line-height: 60px;
    font-size: 38px;
  }

  .headline::after {
    width: 25%;
  }

  .container-grid {
    grid-template-columns: 6fr 5fr;
  }

  .tour-description {
    font-weight: 700;
    font-size: 21px;
  }

  .top-frame {
    top: -15px;
    left: -20px;
    width: 234px;
    height: 423px;
  }

  .bottom-frame {
    bottom: -15px;
    right: -13px;
    width: 234px;
    height: 423px;
  }

  .team-regards {
    font-size: 22px;
  }

  .airplane-icon_1,
  .airplane-icon_2 {
    position: absolute;
    width: 237px;
    left: 0;
    top: 65rem;
  }

  .airplane-icon_2 {
    width: 230px;
    top: 168rem;
  }

  .airplane-icon_3 {
    position: absolute;
    width: 317px;
    right: 0;
    top: 200rem;
  }

  .airplane-icon_4,
  .airplane-icon_5 {
    position: absolute;
    width: 405px;
    left: 0;
    top: 232rem;
  }

  .airplane-icon_5 {
    top: 258rem;
  }

  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8 {
    position: absolute;
    width: 235px;
    left: 0;
    top: 284rem;
  }

  .airplane-icon_7,
  .airplane-icon_8 {
    top: 325rem;
  }

  .airplane-icon_8 {
    width: 405px;
    top: 355rem;
  }

  .section-title {
    font-size: 25px !important;
    padding: 0.3rem 7rem 0.3rem 2rem;
  }

  .section-title-hotail,
  .section-title_gaolh {
    font-size: 25px !important;
    padding: 0.3rem 10rem 0.3rem 2rem;
  }

  .section-title_gaolh {
    padding: 0.3rem 8rem 0.3rem 2rem;
  }

  .contat-section-title {
    margin-right: 365px;
  }

  .flight-path {
    top: 5%;
    width: 180px;
  }

  .info-list li {
    padding-right: 11rem;
  }

  .info-list_1 li {
    position: relative;
    padding-right: 1rem;
    margin-bottom: 0;
    list-style-type: none;
    font-size: 14px;
  }

  .info-list_1 li::before {
    content: "▪";
    color: #000;
    font-weight: 700;
    display: inline-block;
    width: 20px;
    margin-right: -20px;
    font-size: 1.2em;
  }

  .section-title {
    font-size: 23px !important;
    padding: 0.3rem 7rem 0.3rem 2rem;
  }

  .flight-path {
    top: 50%;
    width: 300px;
  }

  .info-list_1 li {
    font-size: 20px;
  }
}

/* 8.2 Tablet (767px and below) */
@media (max-width: 767px) {
  body {
    font-size: 14px;
  }

  .headline {
    font-size: 24px;
    line-height: 1.4;
  }

  .headline::after {
    width: 40%;
  }


  .image-container {
    margin: 1rem 0;
  }

  .image-wrapper img {
    width: 100%;
    max-height: 400px;
  }

  .bottom-frame,
  .top-frame {
    display: block;
  }

  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .program-title,
  .secondary-title {
    font-size: 22px;
  }

  .program-title::after,
  .secondary-title::after {
    width: 20%;
  }

  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    margin-right: 0;
    transform: none;
    margin-bottom: 1rem;
  }

  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8 {
    display: none;
  }

  .cancellation_policy {
    gap: 1rem;
    flex-direction: column;
    align-items: flex-start;
  }

  .info-list li {
    padding-right: 20px;
  }

  .info-list_1 li {
    padding-right: 10px;
    font-size: 12px;
  }

  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    font-size: 16px !important;
    padding: 0.3rem 2rem 0.3rem 1rem;
    border-radius: 25px 0 0 25px;
  }

  .distances>img {
    width: 100%;
  }

  .grid-container {
    grid-template-columns: 3fr 3fr;
    gap: 3rem;
  }

  .introduction {
    max-width: 100%;
    font-size: 16px;
  }

  .climate {
    width: 100%;
  }

  .flight-path {
    display: none;
  }

  .cancellation_policy {
    flex-direction: column;
    align-content: flex-start;
    justify-content: center;
  }
}

/* 8.3 Mobile (480px and below) */
@media (max-width: 480px) {
  .title {
    color: #74a100;
    font-weight: 700;
    font-size: 1.3rem;
    text-align: right;
    line-height: 60px;
    position: relative;
    margin-bottom: 1.3rem;
  }

  .title::after {
    content: "";
    display: block;
    width: 100px;
    height: 5px;
    background-color: #74a100;
    margin-top: 5px;
    margin-left: 0;
  }

  .headline {
    font-size: 20px;
  }

  .feature-grid {
    grid-template-columns: 2fr 3fr 2fr;
  }

  .icon-circle {
    width: 60px;
    height: 60px;
  }

  .icon-img {
    width: 30px;
    height: 30px;
  }

  .program-title,
  .secondary-title {
    font-size: 20px;
  }

  .tour-description {
    line-height: 2;
    text-align: justify;
    font-size: 0.9rem;
  }

  .orange-box {
    background-color: var(--light-orange) !important;
    margin-top: auto;
    padding: 14px 15px 1px;
    border-radius: 8px;
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .top-frame {
    top: -15px;
    left: -20px;
    width: 234px;
    height: 423px;
  }
}

/* ==========================================
      9. PRINT STYLES
      ========================================== */
@media print {
  @page {
    size: A4 landscape;
    margin: 1cm;
  }

  *,
  body,
  html {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  body,
  html {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    font-family: "Cairo", sans-serif !important;
    font-size: 10pt;
    background-color: #fff !important;
    color: #000 !important;
    overflow: visible !important;
  }

  /* Container elements */
  .container,
  .container-fluid,
  .page {
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;
  }

  .container {
    padding: 1rem;
  }

  .container,
  .container-fluid,
  body,
  html {
    padding: 0 !important;
  }

  /* Section handling */
  section {
    padding: 10px 0 30px;
    page-break-before: always;
    page-break-inside: avoid !important;
    page-break-after: auto;
    break-inside: avoid;
    width: 100% !important;
    box-sizing: border-box;
  }

  section.home_page_image {
    page-break-before: avoid;
    padding: 0;
    margin: 0;
    height: 100%;
  }

  section.home_page_image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 9.2 Typography for Print */
  .page-title,
  .program-title,
  .secondary-title,
  .title,
  h1,
  h2,
  h3,
  h4 {
    font-size: 20pt !important;
    font-weight: 700 !important;
    page-break-after: avoid;
    margin-top: 0.8em;
    margin-bottom: 0.5em;
  }


  .headline {

    width: 26rem !important;
    line-height: 30px !important;
    color: var(--secondary-green) !important;
    margin-bottom: 1rem;
    page-break-after: avoid;
    font-size: 15pt !important;
    font-weight: 800 !important;
  }

  .headline::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 29%;
    height: 5px;
    background-color: var(--secondary-green) !important;
    display: block;
  }

  /* Title styles */
  .title {
    color: #74a100 !important;
    font-weight: 700;
    font-size: 16pt !important;
    text-align: right;
    line-height: 30px;
    position: relative;
    margin-bottom: 0.3rem;
    font-family: "Cairo", sans-serif !important;
  }

  .title::after {
    content: "" !important;
    display: block !important;
    width: 100px !important;
    height: 3px !important;
    background-color: #74a100 !important;
    margin-top: 5px !important;
    margin-left: 0;
  }

  .page-title {
    color: var(--secondary-green) !important;
    font-size: 18pt !important;
    margin-bottom: 15px;
    text-align: start;
  }

  .page-title::after {
    content: "";
    display: block;
    width: 180px;
    height: 3px;
    background-color: var(--secondary-green) !important;
    margin-top: 5px;
    margin-left: auto;
    margin-right: 0;
  }

  .program-title,
  .secondary-title {
    position: relative;
    padding-bottom: 10px;
    font-size: 14pt !important;
    margin-bottom: 0.8rem;
  }

  .program-title {
    color: var(--secondary-green) !important;
  }

  .secondary-title {
    color: var(--secondary-orange) !important;
  }

  .program-title::after,
  .secondary-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    height: 3px;
  }

  .program-title::after {
    width: 54%;
    background-color: var(--secondary-green) !important;
  }

  .secondary-title::after {
    width: 13%;
    background-color: var(--secondary-orange) !important;
  }

  /* Section titles */
  .section-title,
  .section-title-hotail,
  .section-title_gaolh {
    font-size: 12pt !important;
    font-weight: 600 !important;
    background: linear-gradient(to right, #ffa771, #ffe8dc) !important;
    display: inline-block;
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    color: #000 !important;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    page-break-after: avoid;
  }

  .section-title {
    padding: 0.2rem 4rem 0.2rem 1rem;
  }

  .section-title-hotail {
    padding: 0.2rem 6rem 0.2rem 1rem;
  }

  .section-title_gaolh {
    padding: 0.2rem 3rem 0.2rem 1rem;
  }

  /* Content text */
  .climate,
  .introduction,
  .tour-description,
  p {
    font-size: 10pt !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .tour-description {
    margin-left: 1rem;
    line-height: 1.5;
    page-break-inside: avoid;
  }

  .introduction {
    font-size: 11px;
    font-weight: 600;
    line-height: 1.8;
    text-align: justify;
    margin-bottom: 24px;
    max-width: 70%;
  }

  .climate {
    width: 100%;
  }

  /* 9.3 Components for Print */
  /* Lists */
  ol,
  ul {
    padding-right: 20px;
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }

  li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    font-size: 10pt !important;
  }

  .feature-list {
    color: #fff !important;
    list-style: none;
    padding-right: 0;
  }

  .info-list li,
  .info-list_1 li {
    list-style-type: none;
    position: relative;
    padding-right: 25px;
    font-size: 10pt !important;
  }

  .info-list li {
    font-size: 13pt;
    font-weight: 600;
    padding-right: 8rem;
  }

  .info-list_1 li {
    padding-right: 1rem;
    margin-bottom: 0;
    font-size: 14px;
  }

  .info-list li::before,
  .info-list_1 li::before {
    content: "•";
    color: #000 !important;
    font-weight: 700;
    display: inline-block;
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    text-align: center;
    font-size: 1.2em;
  }

  .info-list_1 li::before {
    content: "▪";
  }

  /* Features */
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
  }

  .feature-text {
    font-size: 8pt !important;
    line-height: 1.4 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .icon-circle {
    background-color: #8bc12a !important;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-bottom: 5px;
    justify-content: center;
    display: flex;
    align-items: center;
  }

  .icon-img {
    width: 25px;
    height: 25px;
  }

  /* Information items */
  .info-item {
    font-size: 10pt !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.3rem;
  }

  .info-label {
    color: var(--primary-orange) !important;
    font-weight: 700 !important;
    font-size: 10pt !important;
    display: inline;
    margin-left: 5px;
  }

  /* Orange elements */
  .orange-box {
    background-color: var(--light-orange) !important;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    page-break-inside: avoid;
    margin-bottom: 1rem;
  }

  .orange-box .feature-list {
    margin-bottom: 0;
  }

  .orange-box .feature-list li {
    margin-bottom: 0.3rem;
  }


  /* Grids */
  .container-grid {
    display: grid;
    grid-template-columns: 5fr 6fr;
    gap: 20px;
    align-items: center;
    direction: ltr !important;
    padding: 0 2cm;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4rem;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
    page-break-inside: avoid;
  }

  .info-grid {
    font-weight: 700;
    display: flex;
    flex-direction: column;
    gap: 10px;
    font-size: 15px;
    text-align: right;
    display: block;
    margin-bottom: 1rem;
    page-break-inside: avoid;
  }

  /* Features */
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
  }

  .feature-text {
    font-size: 8pt !important;
    line-height: 1.4 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  .icon-circle {
    background-color: #8bc12a !important;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-bottom: 5px;
    justify-content: center;
    display: flex;
    align-items: center;
  }

  .icon-img {
    width: 25px;
    height: 25px;
  }

  /* Information items */
  .info-item {
    font-size: 10pt !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.3rem;
  }

  .info-label {
    color: #ff914d !important;
    font-weight: 700 !important;
    font-size: 10pt !important;
    display: inline;
    margin-left: 5px;
  }

  /* Orange elements */
  .orange-box {
    background-color: #ff9a56 !important;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    margin-top: 0.5rem;
    page-break-inside: avoid;
    margin-bottom: 1rem;
  }

  .orange-box .feature-list {
    margin-bottom: 0;
  }

  .orange-box .feature-list li {
    margin-bottom: 0.3rem;
  }

  .orange-frame {
    position: absolute;
    background-color: #ff7e3d !important;
    z-index: 1;
    display: none;
  }

  /* Images */
  .image-container {
    width: 100%;
    position: relative;
    page-break-inside: avoid;
  }

  .image-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    height: auto;
  }

  .image-wrapper img {
    max-width: 100%;
    height: auto;
    width: 80%;
    object-fit: contain;
    display: block;
    border: 1px solid #eee;
  }

  .distances>img {
    width: 75%;
    height: auto;
    border-radius: 0.5rem;
  }

  .distances img {
    width: 90%;
    max-width: 200px;
    height: auto;
    border-radius: 0.5rem;
    border: 1px solid #eee;
  }

  /* Restaurant items */
  .restaurant-item {
    font-size: 10pt !important;
    margin-bottom: 0.4rem;
    page-break-inside: avoid;
  }

  .restaurant-name {
    font-weight: 600;
    color: #333;
  }

  .restaurant-rating {
    font-weight: 600;
    color: #74a100 !important;
  }

  .restaurant-desc {
    color: #555;
    font-size: 10pt !important;
    line-height: 1.6 !important;
    text-align: justify;
    margin-bottom: 0.8rem;
  }

  /* Contact and team sections */
  .cancellation_policy {
    gap: 3rem;
    display: block;
    page-break-inside: avoid;
    break-inside: avoid !important;
    margin-bottom: 1.5rem !important;
  }

  .cancellation_policy .info-list {
    margin-top: 0.5rem;
  }

  .contat-section-title,
  .contat-section-title-2,
  .contat-section-title-3,
  .contat-section-title_h {
    transform: none !important;
    margin-right: 0 !important;
    margin-bottom: 1rem;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    margin-bottom: 1.5rem !important;
  }

  .contat-section-title,
  .contat-section-title_h {
    margin-right: 13rem;
    transform: translate(0, -50px);
  }

  .contat-section-title {
    margin-right: 23rem;
    transform: translate(0, -50px);
  }

  .team-regards-container {
    background-color: #ff7e3d !important;
    color: #fff !important;
    padding: 0.5cm 1cm;
    width: 100%;
    margin-top: 1cm;
    page-break-before: auto;
    page-break-inside: avoid;
    box-sizing: border-box;
  }

  .team-regards {
    color: #fff !important;
    font-weight: 600 !important;
    text-align: center;
    font-size: 11pt !important;
    margin: 0;
  }

  /* Hide elements */
  .airplane-icon_1,
  .airplane-icon_2,
  .airplane-icon_3,
  .airplane-icon_4,
  .airplane-icon_5,
  .airplane-icon_6,
  .airplane-icon_7,
  .airplane-icon_8,
  .flight-path {
    display: none !important;
  }

  .content-section {
    order: 1;
  }

  .distances {
    text-align: center;
  }


  /* Orange Frames for Images */
  .orange-frame {
    display: block;
    position: absolute;
    background-color: var(--color-orange-primary, #ff7e3d) !important;
    z-index: 1;
  }

  .top-frame {
    display: none;
    top: 10px;
    left: 25px;
    width: 25%;
    height: 40%;
    max-width: 80px;
    max-height: 150px;
  }

  .bottom-frame {
    display: none;
    bottom: -10px;
    right: -10px;
    width: 30%;
    height: 35%;
    max-width: 100px;
    max-height: 120px;
  }

}