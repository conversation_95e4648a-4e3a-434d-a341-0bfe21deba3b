/* [Master <PERSON>] */
/* ----------------------------------------------------------
    :: Template
    :: Author: Turbo
    :: Author URL:www.boo-code.com
    :: Version: 1.0
    :: Created: 10 2023
    :: Last Updated: 10 2023
    ---------------------------------------------------------- */
/* -------------------------------------------------
    ============ PLACE YOUR CUSTOM CSS HERE ============
    ------------------------------------------------- */
@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300..700&display=swap");
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: "Cairo", sans-serif;
}

a {
  text-decoration: unset;
}

.bill {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.bill .big-container {
  flex: 1 1 0%;
}
.bill .logo-box {
  display: flex;
  align-items: center;
  justify-content: end;
}
.bill .logo-box img {
  width: 8rem;
  margin-bottom: 0.5rem;
}
.bill .detials {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.bill .detials h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 2rem;
}
.bill .detials h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #000;
}
.bill .detials h4 {
  font-size: 1.2rem;
  font-weight: 500;
  color: #646464;
}
.bill .detials .company-logo {
  width: 10rem;
}
.bill .detials .alert-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: #EE5352;
  line-height: 1.8;
}
.bill .table > :not(caption) > * > * {
  padding: 1rem;
}
.bill .table thead {
  background: #000000;
}
.bill .table thead th {
  color: #fff;
  font-weight: 700;
}
.bill .dir-rtl {
  direction: rtl;
}
.bill .main-gap {
  row-gap: 1rem;
}
.bill footer {
  margin-top: 6rem;
}
.bill footer img {
  width: 100%;
}

@media print {
  html {
    font-size: 10px;
  }
}
/* Responsive */
/* Extra small devices (phones, 600px and down) */
/* galaxy S5 */
/* iphone x, 6/7/8 */
/* iphone 6/7/8 plus */
/* ipad */
/*  large screen  */
/* 17 inch *//*# sourceMappingURL=style.css.map */
