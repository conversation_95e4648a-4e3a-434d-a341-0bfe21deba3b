/* [Master <PERSON>] */

/* ----------------------------------------------------------
    :: Template
    :: Author: Turbo
    :: Author URL:www.boo-code.com
    :: Version: 1.0
    :: Created: 10 2023
    :: Last Updated: 10 2023
    ---------------------------------------------------------- */

/* -------------------------------------------------
    ============ PLACE YOUR CUSTOM CSS HERE ============
    ------------------------------------------------- */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300..700&display=swap');


* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Cairo', sans-serif;
}

a {
  text-decoration: unset;
}

.bill {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .big-container {
    flex: 1 1 0%;
  }

  .logo-box {
    display: flex;
    align-items: center;
    justify-content: end;

    img {
      width: 8rem;
      margin-bottom: .5rem;
    }
  }

  .detials {
    margin-top: 1rem;
    margin-bottom: 1rem;

    h2 {
      font-size: 1.5rem;
      font-weight: 700;
      color: #000;
      margin-bottom: 2rem;
    }

    h3 {
      font-size: 1.2rem;
      font-weight: 700;
      color: #000;
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 500;
      color: #646464;

    }

    .company-logo {
      width: 10rem;
    }

    .alert-text {
      font-size: 1.2rem;
      font-weight: 700;
      color: #EE5352;
      line-height: 1.8;


    }
  }


  .table>:not(caption)>*>* {
    padding: 1rem;
  }

  .table thead {
    background: #000000;

    th {
      color: #fff;
      font-weight: 700;
    }
  }

  .dir-rtl {
    direction: rtl;
  }

  .main-gap {
    row-gap: 1rem;
  }

  footer {
    margin-top: 6rem;

    img {
      width: 100%;
    }

    // img {
    //   height: 7rem;
    //   margin-left: auto;
    //   margin-right: auto;
    //   display: block;
    // }
  }
}

@media print {
  html {
    font-size: 10px;
  }
}

/* Responsive */
/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {}

/* galaxy S5 */
@media only screen and (min-width: 359px) and (max-width: 361px) {}

/* iphone x, 6/7/8 */
@media only screen and (min-width: 375px) and (max-width: 380px) {}

/* iphone 6/7/8 plus */
@media only screen and (min-width: 410px) and (max-width: 416px) {}

/* ipad */
@media only screen and (min-width: 768px) and (max-width: 992px) {}

/*  large screen  */
@media only screen and (min-width: 1024px) and (max-width: 1290px) {}

// special screen
@media only screen and (min-width: 1366px) and (max-width: 1919px) {}

/* 17 inch */
@media only screen and (min-width: 1920px) and (max-width: 2500px) {}
