.filament-sidebar-nav > ul {
    padding-bottom: 200px !important;
}

/* Enhance sidebar scrolling */
.filament-sidebar-nav {
    scroll-behavior: smooth !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Ensure active items are more visible */
.filament-sidebar-item-active {
    position: relative !important;
}

.filament-sidebar-item-active::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--primary-500, #3b82f6);
    border-radius: 2px;
    opacity: 0.8;
}

/* Better scrollbar styling for sidebar */
.filament-sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.filament-sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.filament-sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.filament-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* Dark mode scrollbar */
.dark .filament-sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
}

.dark .filament-sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Firefox scrollbar */
.filament-sidebar-nav {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

.dark .filament-sidebar-nav {
    scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}
