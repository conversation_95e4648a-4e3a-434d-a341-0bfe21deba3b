<?php
/**
 * Find the correct Pusher cluster for app key
 * Usage: php find_pusher_cluster.php
 */

// Updated Pusher credentials
$appKey = '309d0f1beaad790cf00e';
$appId = '2003427';
$appSecret = '59e4e31f3b7f27f82220';
$cluster = 'eu'; // Your specified cluster

$clusters = ['mt1', 'us2', 'us3', 'eu', 'ap1', 'ap2', 'ap3', 'ap4'];

echo "🔍 Finding correct Pusher cluster for app key: {$appKey}\n";
echo "====================================================\n\n";

foreach ($clusters as $cluster) {
    echo "Testing cluster: {$cluster}... ";
    
    $url = "https://api-{$cluster}.pusherapp.com/apps/{$appKey}/events";
    
    // Test with curl
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ DNS/Connection error: {$error}\n";
    } elseif ($httpCode == 401) {
        echo "✅ FOUND! Cluster '{$cluster}' recognizes this app key\n";
        echo "   Use: PUSHER_APP_CLUSTER={$cluster}\n";
        break;
    } elseif ($httpCode == 404) {
        echo "❌ App key not in this cluster\n";
    } else {
        echo "⚠️  HTTP {$httpCode} - Unexpected response\n";
    }
}

echo "\n🔍 Testing default cluster (no cluster specified)...\n";
$url = "https://api.pusherapp.com/apps/{$appKey}/events";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ DNS/Connection error: {$error}\n";
} elseif ($httpCode == 401) {
    echo "✅ FOUND! Default cluster works - don't specify PUSHER_APP_CLUSTER\n";
} elseif ($httpCode == 404) {
    echo "❌ App key not found in default cluster\n";
} else {
    echo "⚠️  HTTP {$httpCode} - Unexpected response\n";
}

echo "\n🏁 Cluster detection completed.\n"; 