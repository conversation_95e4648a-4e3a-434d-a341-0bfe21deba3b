<?php

use App\Models\Country;
use App\Models\CurrencyRate;
use App\Models\Hotel;
use App\Models\HotelBooking;
use App\Models\Payment;
use App\Models\Reservation;
use App\Enums\SalesCostStatus;
use App\Models\TransportationCompanyPayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use App\Models\TransportationCompanyBooking;
use App\Http\Controllers\KidsInsuranceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotelRateOptions(), database_path('excels/hotel-rates/hotels_Azerbaijan (1) (1).xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotelRateOptions(), database_path('excels/hotel-rates/hotels_Georgia (1) (4).xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotelRateOptions(), database_path('excels/hotel-rates/hotels_Russia (1) (1).xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotelRateOptions(), database_path('excels/hotel-rates/hotels_Turkey (1) (1).xlsx'));
});


Route::get('/trip-pdf/{id?}', function ($id = null) {
    app()->setLocale('ar');

    $tripPdf = \App\Models\TripPdf::query()->find($id);

    $brand = $tripPdf->brand?->key ?? null;

    $view = $brand ? "trip-pdfs.{$brand}" : 'trip-pdf';

    $view = 'trip-pdf';

    return view($view, compact('tripPdf'));
})->name('trip-pdf');

Route::get('/invoice/{id?}', function ($id = null) {
    $invoice = Payment::query()->withoutGlobalScopes()->whereNull('deleted_at')->find($id);

    return view('invoice', compact('invoice'));
})->name('invoice');

Route::get('/driver-pdf/{reservationId}', function ($reservationId = null) {
    $reservation = Reservation::query()->find($reservationId);
    $view = request()->get('english') == '1' ? 'driver-pdf2-english' : 'driver-pdf2';
    return view($view, compact('reservation'));
})->name('driver-pdf');

Route::get('/voucher/voucher/{reservationId}', function ($reservationId = null) {
    $reservation = Reservation::query()->find($reservationId);

    $brand = \App\Models\Brand::query()->firstWhere('key', request()->get('brand'));

    return view('voucher.voucher', compact('reservation', 'brand'));
})->name('voucher.voucher');

Route::get('/voucher/invoice/{reservationId}', function ($reservationId = null) {
    $reservation = Reservation::query()->find($reservationId);

    $brand = \App\Models\Brand::query()->firstWhere('key', request()->get('brand'));

    return view('voucher.invoice', compact('reservation', 'brand'));
})->name('voucher.invoice');

Route::get('/banner/{reservationId}', function ($reservationId = null) {
    $reservation = Reservation::query()->find($reservationId);

    if ($reservation->is_b2b) {
        $view = 'b2b';
    }else{
        $brand = $reservation->brand;

        $view = $brand->key;
    }
    return view("banners.$view", compact('reservation'));
})->name('banner');

Route::get('/change-locale/{locale}', function ($locale) {
    session()->put('locale', $locale);
    return redirect()->back();
})->name('change-locale');

Route::get('/driver-tours', function () {
    return view('driver-tours');
})->name('driver-tours');

Route::get('update', function () {

    dd(
        Payment::query()
        ->where('user_id', 38)
            ->where('status', '=' , 'confirmed')
        ->get()
    );
    return TransportationCompanyBooking::query()
        ->whereHas('reservation', function ($query) {
            $query->where('transportation_cost_status', SalesCostStatus::CONFIRMED);
        })
        ->get()
        ->filter(function ($transportationCompanyBooking) {
            return $transportationCompanyBooking->getFinalPrice() != $transportationCompanyBooking->price_lari;
        });
});

Route::get('update-trans-payments', function () {
});

Route::get('login-by-email', function () {
    $email = request()->get('email');
    /** @var \App\Models\User $user */
    $user = \App\Models\User::query()->where('email', $email)->first();

    if ($user) {
        auth()->login($user);
    }

    return redirect('/admin');
});

Route::get('test', function () {
    dd(
        \App\Models\HotelBooking::query()
            ->find(52)
            ->getPaymentsByIndex(2)
            ->toArray()
    );
});

Route::get('import', function () {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    \App\Models\Hotel::query()->truncate();
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');

    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotels(7), database_path('excels/Copy of Hotels Info AZ-1(1).xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotels(9), database_path('excels/hOTELS FOR CRM russia.xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotels(8), database_path('excels/hotels_Turkey (1) CRM (1).xlsx'));
    \Maatwebsite\Excel\Facades\Excel::import(new \App\Imports\ImportHotels(6), database_path('excels/mariooooooo.xlsx'));
});

Route::get('export', function () {
    /**
     * 0 => 6
     * 1 => 7
     * 2 => 8
     * 3 => 9
     * 4 => 22
     * 5 => 24
     */
    $country = Country::query()->find(request()->get('country_id'));
    return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ExportHotels($country->id), 'hotels_'.$country->name.'.xlsx');
});

Route::get('/client-review/{reservationId}', \App\Http\Livewire\ClientReview::class)->name('client-review');

Route::get('/insurance/azerbaijan', function () {
    return view('insurance.azerbaijan');
})->name('insurance.azerbaijan');

Route::get('/insurance/georgia', function () {
    return view('insurance.georgia');
})->name('insurance.georgia');

// Insurance PDF Routes
Route::get('/insurance/download-all/{reservation}/{type?}', [App\Http\Controllers\InsurancePdfController::class, 'downloadAllPeople'])
    ->name('insurance.download-all-people')
    ->middleware('auth');

Route::get('/insurance/download-all-kids/{reservation}', [App\Http\Controllers\InsurancePdfController::class, 'downloadAllKids'])
    ->name('insurance.download-all-kids')
    ->middleware('auth');

Route::get('/insurance/download-single/{reservation}/{person}', [App\Http\Controllers\InsurancePdfController::class, 'downloadSinglePerson'])
    ->name('insurance.download-single-person')
    ->middleware('auth');

Route::get('/insurance/preview/{reservation}/{person?}/{type?}', [App\Http\Controllers\InsurancePdfController::class, 'preview'])
    ->name('insurance.preview')
    ->middleware('auth');

Route::get('/insurance/preview-all/{reservation}', [App\Http\Controllers\InsurancePdfController::class, 'previewAllPeople'])
    ->name('insurance.preview-all')
    ->middleware('auth');

// AJAX endpoint for fetching people data
Route::get('/admin/reservations/{reservation}/people', function (Reservation $reservation) {
    $people = $reservation->people()->orderBy('order')->get(['id', 'name', 'order']);
    return response()->json([
        'people' => $people,
        'count' => $people->count()
    ]);
})->middleware('auth');

// My Instructions page
Route::get('/my-instructions', \App\Http\Livewire\MyInstructions::class)->name('my-instructions');

// Test Pusher Chat - Clean Implementation
Route::get('/test-pusher', function () {
    return view('test-pusher');
})->name('test-pusher');

// Test sending message via Laravel backend
Route::post('/test-send-message', function () {
    $messageId = rand(1000, 9999);
    $fromUserId = request('from_user_id', 1);
    $toUserId = request('to_user_id', 2);
    $content = request('message', 'Test message from Laravel backend');

    // Broadcast the message using the existing MessageSent event
    broadcast(new \App\Events\MessageSent(
        $messageId,
        $fromUserId,
        $toUserId,
        $content
    ));

    return response()->json([
        'success' => true,
        'message' => 'Message sent successfully via Pusher',
        'data' => [
            'message_id' => $messageId,
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
            'content' => $content
        ]
    ]);
})->name('test-send-message');

// Add routes for kids insurance
Route::get('kids/{reservation}/download-single/{kid}', [KidsInsuranceController::class, 'downloadSingle'])
    ->name('kids.download-single')
    ->middleware('auth');

Route::get('kids/{reservation}/preview/{kid}', [KidsInsuranceController::class, 'preview'])
    ->name('kids.preview')
    ->middleware('auth');

// Test routes for real-time functionality
Route::get('/test-pusher', function () {
    return view('test-pusher');
})->name('test-pusher');

Route::post('/test-send-message', function () {
    $messageId = rand(1000, 9999);
    $fromUserId = request('from_user_id', 1);
    $toUserId = request('to_user_id', 2);
    $content = request('message', 'Test message from Laravel backend');

    // Broadcast the message using the existing MessageSent event
    broadcast(new \App\Events\MessageSent(
        $messageId,
        $fromUserId,
        $toUserId,
        $content
    ));

    return response()->json([
        'success' => true,
        'message' => 'Message sent successfully via Pusher',
        'data' => [
            'message_id' => $messageId,
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
            'content' => $content
        ]
    ]);
})->name('test-send-message');

// Test routes for instruction notifications
Route::get('/test-instruction-notifications', function () {
    return view('test-instruction-notifications');
})->name('test-instruction-notifications')->middleware('auth');

Route::post('/test-send-instruction', function () {
    $data = request()->all();

    try {
        // Create a test instruction
        $instruction = \App\Models\Instruction::create([
            'title' => $data['title'] ?? 'Test Instruction',
            'content' => $data['content'] ?? 'Test instruction content',
            'priority' => $data['priority'] ?? 'normal',
            'target_roles' => $data['target_roles'] ?? ['admin'],
            'created_by' => auth()->id(),
            'is_active' => true,
            'expires_at' => now()->addDays(7), // Expire in 7 days
        ]);

        // Broadcast the instruction via Pusher
        broadcast(new \App\Events\InstructionSent($instruction));

        return response()->json([
            'success' => true,
            'message' => 'Instruction sent successfully via Pusher',
            'data' => [
                'instruction_id' => $instruction->id,
                'title' => $instruction->title,
                'content' => $instruction->content,
                'priority' => $instruction->priority,
                'target_roles' => $instruction->target_roles,
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send instruction: ' . $e->getMessage()
        ], 500);
    }
})->name('test-send-instruction')->middleware('auth');

// Test route for Pusher financial notifications (remove in production)
Route::get('/test-financial-pusher', function () {
    if (!auth()->check()) {
        return redirect('/login');
    }

    return view('test-financial-pusher');
})->name('test.financial.pusher');

// Simple Pusher test page (following documentation pattern)
Route::get('/pusher-simple-test', function () {
    return view('pusher-simple-test');
})->name('pusher.simple.test');

// Test route for simple Pusher event (following documentation pattern)
Route::get('/test-pusher-simple', function () {
    event(new \App\Events\MyEvent('hello world'));
    return 'Event fired! Check your Pusher test page.';
});

// Test routes for "Other payment methods" notifications
Route::get('/test-other-payment-notifications', function () {
    if (!auth()->check()) {
        return redirect('/login');
    }

    return view('test-other-payment-notifications');
})->name('test.other.payment.notifications')->middleware('auth');

Route::post('/test-create-other-payment', function () {
    try {
        // Get a random reservation for testing
        $reservation = \App\Models\Reservation::inRandomOrder()->first();

        if (!$reservation) {
            return response()->json([
                'success' => false,
                'message' => 'No reservations found for testing'
            ], 404);
        }

        // Find or create "Other payment methods" payment method
        $paymentMethod = \App\Models\PaymentMethod::firstOrCreate([
            'name' => 'Other payment methods'
        ], [
            'is_active' => true
        ]);

        // Create a test payment
        $payment = \App\Models\Payment::create([
            'payable_type' => \App\Models\Reservation::class,
            'payable_id' => $reservation->id,
            'payment_method_id' => $paymentMethod->id,
            'amount' => rand(100, 1000),
            'status' => 'pending',
            'user_id' => auth()->id(),
            'notes' => 'Test payment created for notification testing'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test payment created successfully',
            'payment_id' => $payment->id,
            'reservation_code' => $reservation->trip_code,
            'amount' => $payment->amount
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to create test payment: ' . $e->getMessage()
        ], 500);
    }
})->name('test.create.other.payment')->middleware('auth');
