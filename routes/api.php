<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test API route for financial notifications (remove in production)
Route::post('/test-financial-notification', function (Request $request) {
    if (!auth()->check()) {
        return response()->json(['error' => 'Unauthorized'], 401);
    }
    
    $userId = $request->input('user_id', auth()->id());
    
    $notification = app(\App\Services\NotificationService::class)->sendNotificationWithImmediateUpdate(
        userId: $userId,
        type: 'test_financial_api',
        title: '🧪 API Test Financial Notification',
        message: 'This is a test financial notification sent via API! Real-time broadcasting should work immediately.',
        data: [
            'test' => true,
            'sent_via' => 'api_test',
            'timestamp' => now()->toISOString()
        ],
        isImportant: true,
        icon: '💰'
    );
    
    return response()->json([
        'success' => true,
        'notification_id' => $notification->id,
        'message' => 'Test financial notification sent successfully'
    ]);
})->middleware('auth:sanctum');
