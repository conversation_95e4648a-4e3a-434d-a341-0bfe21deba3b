<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Instructions channel - allow all authenticated users to listen
Broadcast::channel('instructions', function ($user) {
    return $user !== null; // Any authenticated user can listen to instructions
});

// Chat channel - allow all authenticated users to listen
Broadcast::channel('chat', function ($user) {
    return $user !== null; // Any authenticated user can listen to chat messages
});

// Financial notifications are now using public channels, no authorization needed
