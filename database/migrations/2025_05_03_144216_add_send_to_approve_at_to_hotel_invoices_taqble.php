<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dropColumn('send_to_approve');
        });

        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dateTime('send_to_approve_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dropColumn('send_to_approve_at');
        });

        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->boolean('send_to_approve')->default(false);
        });
    }
};
