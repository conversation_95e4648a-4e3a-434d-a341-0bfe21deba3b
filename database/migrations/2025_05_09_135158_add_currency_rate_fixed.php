<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * The tables that need the currency_rate_id column.
     *
     * @var array
     */
    protected $tables = [
        'transportation_company_payments',
        'transportation_company_bookings',
        'transportation_company_fines',
        'hotel_payments',
        'hotel_bookings',
        'transportation_company_refund_payments',
        'hotel_refund_payments',
        'transportation_company_extra_services',
        'common_payments',
        'reservations',
        'transportation_company_prices',
        'hotel_rooms',
        'hotel_booking_rooms',
        'extra_service_bookings',
        'expenses',
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach ($this->tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->double('currency_rate_fixed')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach ($this->tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->dropConstrainedForeignId('currency_rate_fixed');
            });
        }
    }
};
