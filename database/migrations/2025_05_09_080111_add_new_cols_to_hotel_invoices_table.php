<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dateTime('paid_at')->nullable();
            $table->dateTime('status_at')->nullable();
            $table->dateTime('cancelled_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dropColumn('paid_at');
            $table->dropColumn('status_at');
            $table->dropColumn('cancelled_at');
        });
    }
};
