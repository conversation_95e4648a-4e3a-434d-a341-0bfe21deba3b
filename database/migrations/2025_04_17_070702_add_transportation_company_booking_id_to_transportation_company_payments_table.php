<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transportation_company_payments', function (Blueprint $table) {
            $table->unsignedBigInteger('transportation_company_booking_id')->nullable()->index('transportation_company_booking_id_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transportation_company_payments', function (Blueprint $table) {
            $table->dropColumn('transportation_company_booking_id');
        });
    }
};
