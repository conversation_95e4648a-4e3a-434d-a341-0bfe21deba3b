<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->double('amount')->nullable();
            $table->double('amount_lari')->nullable();

            $table->double('paid_amount')->nullable();
            $table->double('paid_amount_lari')->nullable();

            $table->string('remaining_amount')->nullable();
            $table->string('remaining_amount_lari')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hotel_invoices', function (Blueprint $table) {
            $table->dropColumn('amount');
            $table->dropColumn('amount_lari');

            $table->dropColumn('paid_amount');
            $table->dropColumn('paid_amount_lari');

            $table->dropColumn('remaining_amount');
            $table->dropColumn('remaining_amount_lari');
        });
    }
};
