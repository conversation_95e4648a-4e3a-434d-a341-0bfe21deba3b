<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('hotel_refunds', function (Blueprint $table) {
            $table->foreignId('hotel_booking_id')->nullable()->constrained('hotel_bookings')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('hotel_refunds', function (Blueprint $table) {
            $table->dropForeign(['hotel_booking_id']);
            $table->dropColumn('hotel_booking_id');
        });
    }
};
