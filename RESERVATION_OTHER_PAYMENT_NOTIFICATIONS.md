# Real-Time Notifications for Reservations with Other Payment Methods

## Overview

This system sends real-time notifications to admin users when a reservation is created with payments using "Other payment methods" (payment methods that are not cash and not card-based, such as bank transfers, online payments, etc.).

## System Components

### 1. Event: `ReservationWithOtherPaymentCreated`
**File:** `app/Events/ReservationWithOtherPaymentCreated.php`

This event is triggered when a reservation is created with an "other" payment method.

**Properties:**
- `$reservation` - The reservation instance
- `$payment` - The payment instance with other payment method

### 2. Listener: `SendReservationOtherPaymentNotification`
**File:** `app/Listeners/SendReservationOtherPaymentNotification.php`

This listener handles the event and sends notifications to appropriate users.

**Recipients:**
- Admin users (`is_admin = true`)
- Users with `ROLE_ACCOUNTANT` role
- Users with `ACCOUNTANT_BR` role  
- Users with `ROLE_SALES` role

**Notification Details:**
- **Type:** `reservation_other_payment`
- **Title:** 🔔 حجز جديد بطريقة دفع أخرى
- **Icon:** 💳
- **Action URL:** `/admin/pending-payments`
- **Priority:** Important (true)

### 3. Observer: `PaymentObserver`
**File:** `app/Observers/PaymentObserver.php`

This observer monitors Payment model creation and triggers the event when:
- Payment is for a Reservation (not other payable types)
- Payment method is "other" (not cash and not card)

**Logic for "Other Payment Methods":**
```php
private function isOtherPaymentMethod(Payment $payment): bool
{
    if (!$payment->paymentMethod) {
        return false;
    }
    
    // If it's cash or card, it's not "other"
    if ($payment->paymentMethod->is_cash || $payment->paymentMethod->is_card) {
        return false;
    }
    
    // If it's not cash and not card, it's considered "other"
    return true;
}
```

### 4. Event Registration
**File:** `app/Providers/EventServiceProvider.php`

The event and listener are registered in the EventServiceProvider:

```php
ReservationWithOtherPaymentCreated::class => [
    SendReservationOtherPaymentNotification::class,
],
```

### 5. Observer Registration
**File:** `app/Providers/AppServiceProvider.php`

The PaymentObserver is registered in the AppServiceProvider:

```php
Payment::observe(PaymentObserver::class);
```

## How It Works

### Flow Diagram:
```
1. User creates reservation with payment
   ↓
2. Payment model is created
   ↓
3. PaymentObserver::created() is triggered
   ↓
4. Observer checks if payment method is "other"
   ↓
5. If yes, ReservationWithOtherPaymentCreated event is fired
   ↓
6. SendReservationOtherPaymentNotification listener handles event
   ↓
7. Real-time notifications sent to admin users
   ↓
8. Notifications appear in admin dashboard with sound/visual alerts
```

### Payment Method Classification:
- **Cash:** `is_cash = true`
- **Card:** `is_card = true`  
- **Other:** `is_cash = false AND is_card = false`

Examples of "Other" payment methods:
- Bank transfers
- Online payment gateways
- Mobile payments
- Cryptocurrency
- Wire transfers
- Any custom payment method

## Testing

### Test Command
**File:** `app/Console/Commands/TestReservationOtherPaymentNotificationCommand.php`

Run the test command to verify the system:

```bash
# Basic test
php artisan test:reservation-other-payment-notification

# Test with specific reservation
php artisan test:reservation-other-payment-notification --reservation-id=123

# Test with specific payment method
php artisan test:reservation-other-payment-notification --payment-method-id=5
```

### Manual Testing Steps:

1. **Create a reservation with "other" payment method:**
   - Go to Reservations → Create New
   - Add payment with a non-cash, non-card payment method
   - Save the reservation

2. **Check notifications:**
   - Admin users should receive real-time notifications
   - Check notification bell icon in admin dashboard
   - Verify notification appears with correct details

3. **Verify logs:**
   - Check `storage/logs/laravel.log` for processing logs
   - Look for entries with "Payment created with other payment method"

## Configuration

### Notification Recipients
To modify who receives notifications, edit the listener:

```php
// In SendReservationOtherPaymentNotification.php
$notificationUsers = User::where(function ($query) {
    $query->where('is_admin', true)
          ->orWhereJsonContains('roles', User::ROLE_ACCOUNTANT)
          ->orWhereJsonContains('roles', User::ACCOUNTANT_BR)
          ->orWhereJsonContains('roles', User::ROLE_SALES);
})
->where('is_active', true)
->get();
```

### Notification Content
To modify notification content, edit the listener:

```php
$message = "تم إنشاء حجز جديد برقم {$reservationCode} للعميل {$customerName} بمبلغ {$amount}$ باستخدام طريقة دفع: {$paymentMethodName}";

$this->notificationService->sendNotificationWithImmediateUpdate(
    userId: $user->id,
    type: 'reservation_other_payment',
    title: '🔔 حجز جديد بطريقة دفع أخرى',
    message: $message,
    // ... other parameters
);
```

### Payment Method Classification
To modify what constitutes "other" payment methods, edit the observer:

```php
// In PaymentObserver.php
private function isOtherPaymentMethod(Payment $payment): bool
{
    // Add your custom logic here
    // Current logic: not cash AND not card = other
    return !$payment->paymentMethod->is_cash && !$payment->paymentMethod->is_card;
}
```

## Troubleshooting

### Common Issues:

1. **Notifications not being sent:**
   - Check if queue workers are running: `php artisan queue:work`
   - Verify event registration in EventServiceProvider
   - Check observer registration in AppServiceProvider

2. **Wrong users receiving notifications:**
   - Review recipient logic in the listener
   - Check user roles and admin status

3. **Observer not triggering:**
   - Verify Payment model observer registration
   - Check if payment is for Reservation (not other payable types)
   - Ensure payment method is classified as "other"

### Debug Commands:

```bash
# Check if events are registered
php artisan event:list

# Test notification system
php artisan test:reservation-other-payment-notification

# Check queue jobs
php artisan queue:failed

# Clear cache if needed
php artisan config:clear
php artisan cache:clear
```

### Log Monitoring:

Monitor these log entries:
- `Payment created with other payment method, triggering notification`
- `Processing reservation with other payment method notification`
- `Reservation other payment notification sent successfully`

## Security Considerations

1. **Authorization:** Only active users with appropriate roles receive notifications
2. **Data Privacy:** Sensitive payment information is not included in notifications
3. **Rate Limiting:** Notifications are queued to prevent spam
4. **Error Handling:** Failed notifications are logged and can be retried

## Performance Impact

- **Minimal:** Observer only triggers for "other" payment methods
- **Asynchronous:** Notifications are sent via queue jobs
- **Efficient:** Database queries are optimized with proper indexing
- **Scalable:** System can handle high volume of reservations

## Future Enhancements

Potential improvements:
1. **Configurable Recipients:** Admin panel to configure who receives notifications
2. **Notification Preferences:** Users can choose notification types
3. **Email Notifications:** Send email alerts in addition to real-time notifications
4. **SMS Notifications:** Send SMS alerts for critical payments
5. **Slack Integration:** Send notifications to Slack channels
6. **Dashboard Widgets:** Show statistics of other payment method usage

## Maintenance

### Regular Tasks:
1. Monitor notification delivery rates
2. Review and update recipient lists
3. Clean up old notification records
4. Update payment method classifications as needed
5. Test system after major updates

### Monitoring Metrics:
- Number of notifications sent per day
- Notification delivery success rate
- Average notification processing time
- User engagement with notifications 