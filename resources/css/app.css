@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Notification Navbar Styles */
.notification-navbar {
    direction: rtl;
}

/* Notification bell animation */
.notification-bell {
    transition: all 0.3s ease;
}

.notification-bell:hover {
    transform: scale(1.1);
}

.notification-bell.animate-bounce {
    animation: bounce 1s infinite;
}

/* Custom badge styles for better visibility */
.notification-badge {
    background-color: #dc2626 !important; /* Strong red background */
    box-shadow: 0 0 0 2px white, 0 2px 4px rgba(0, 0, 0, 0.2);
    font-weight: 700;
    font-size: 0.75rem;
    line-height: 1;
    min-width: 20px;
    height: 20px;
    z-index: 10;
}

/* Notification dropdown styles */
.notification-dropdown {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(0, 0, 0, 0.1);
    height: 480px !important;
    max-height: 480px !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden;
}

/* Notification item hover effects */
.notification-item {
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateX(-2px);
}

/* Enhanced unread notification styling */
.notification-item.unread {
    background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%) !important;
    
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
    position: relative;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    opacity: 0.7;
}

.notification-item.unread:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #dbeafe 100%) !important;
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.15);
}

/* Enhanced scrollbar for notification list */
.notification-list {
    flex: 1;
    overflow-y: auto !important;
    overflow-x: hidden;
    max-height: 320px !important;
    min-height: 320px !important;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.notification-list::-webkit-scrollbar {
    width: 8px;
}

.notification-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
    margin: 4px 0;
}

.notification-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    border: 1px solid #f1f5f9;
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.notification-list::-webkit-scrollbar-thumb:active {
    background: #64748b;
}

/* Dark mode support for notifications */
@media (prefers-color-scheme: dark) {
    .notification-dropdown {
        background-color: #374151;
        border-color: #4b5563;
    }
    
    .notification-item:hover {
        background-color: rgba(59, 130, 246, 0.1);
    }
    
    .notification-item.unread {
        background-color: rgba(59, 130, 246, 0.1);
    }
    
    .notification-badge {
        background-color: #ef4444 !important; /* Brighter red for dark mode */
        box-shadow: 0 0 0 2px #374151, 0 2px 4px rgba(0, 0, 0, 0.4);
    }
}

/* Filament Dark Mode Support for Notifications */
.dark .notification-dropdown {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.dark .notification-item {
    border-color: #374151 !important;
    color: #f9fafb !important;
}

.dark .notification-item:hover {
    background-color: rgba(59, 130, 246, 0.15) !important;
}

.dark .notification-item.unread {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: #f9fafb !important;
}

.dark .notification-item.unread:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
}

.dark .notification-item .text-gray-900 {
    color: #f9fafb !important;
}

.dark .notification-item .text-gray-600 {
    color: #d1d5db !important;
}

.dark .notification-item .text-gray-700 {
    color: #e5e7eb !important;
}

.dark .notification-item .text-blue-900 {
    color: #dbeafe !important;
}

.dark .notification-item .text-gray-500 {
    color: #9ca3af !important;
}

.dark .notification-dropdown .bg-gray-50 {
    background-color: #374151 !important;
}

.dark .notification-dropdown .border-gray-200 {
    border-color: #4b5563 !important;
}

.dark .notification-dropdown .text-gray-900 {
    color: #f9fafb !important;
}

.dark .notification-dropdown .hover\:text-blue-800:hover {
    color: #93c5fd !important;
}

.dark .notification-dropdown .hover\:border-blue-200:hover {
    border-color: #3b82f6 !important;
}

.dark .notification-list::-webkit-scrollbar-track {
    background: #374151 !important;
}

.dark .notification-list::-webkit-scrollbar-thumb {
    background: #6b7280 !important;
    border-color: #374151 !important;
}

.dark .notification-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af !important;
}

.dark .notification-list::-webkit-scrollbar-thumb:active {
    background: #d1d5db !important;
}

.dark .notification-badge {
    background-color: #ef4444 !important;
    box-shadow: 0 0 0 2px #1f2937, 0 2px 4px rgba(0, 0, 0, 0.6) !important;
}

/* Empty state styling for dark mode */
.dark .text-gray-300 {
    color: #9ca3af !important;
}

.dark .text-gray-500 {
    color: #6b7280 !important;
}

/* Button hover effects in dark mode */
.dark .notification-item button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .notification-dropdown .flex-shrink-0 button:hover {
    background-color: #374151 !important;
    color: #93c5fd !important;
}

/* RTL layout adjustments */
[dir="rtl"] .notification-navbar {
    direction: rtl;
}

[dir="rtl"] .notification-dropdown {
    right: 0;
    left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 90vw;
        max-width: 350px;
        right: 5vw;
    }
}

/* Toast notification styles */
.toast-notification {
    backdrop-filter: blur(8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Animation for new notifications */
@keyframes notificationPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.notification-pulse {
    animation: notificationPulse 0.6s ease-in-out;
}

/* Filament topbar integration */
.filament-main-topbar .notification-navbar {
    margin: 0 8px;
}

.filament-main-topbar .notification-navbar .notification-dropdown {
    min-width: 400px;
    max-width: 500px;
    width: auto;
}

/* Sidebar footer link styling */
.filament-sidebar .sidebar-footer-link {
    width: 100%;
}

/* Ensure proper spacing in topbar */
.filament-main-topbar [class*="space-x"] > * + * {
    margin-left: 0.75rem;
}

.filament-main-topbar [class*="space-x-reverse"] > * + * {
    margin-right: 0.75rem;
    margin-left: 0;
}

/* Success notifications use blue instead of green */
.notification-success {
    background-color: #3b82f6 !important;
    border-color: #2563eb !important;
    color: white !important;
}
