<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Kid Insurance for {{ $kid->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .content {
            margin-bottom: 30px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .info-table th {
            background-color: #f2f2f2;
            text-align: left;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Child Insurance Certificate</h1>
        <p>Trip Code: {{ $reservation->trip_code }}</p>
    </div>

    <div class="content">
        <h2>Child Information</h2>
        <table class="info-table">
            <tr>
                <th>Full Name:</th>
                <td>{{ $kid->name }}</td>
            </tr>
            <tr>
                <th>Passport Number:</th>
                <td>{{ $kid->passport_number }}</td>
            </tr>
            <tr>
                <th>Birth Date:</th>
                <td>{{ $kid->birth_date ? $kid->birth_date->format('d/m/Y') : '' }}</td>
            </tr>
            <tr>
                <th>Country:</th>
                <td>{{ $kid->country_name }}</td>
            </tr>
        </table>

        <h2>Trip Details</h2>
        <table class="info-table">
            <tr>
                <th>Leader Name:</th>
                <td>{{ $reservation->customer_name }}</td>
            </tr>
            <tr>
                <th>Trip Code:</th>
                <td>{{ $reservation->trip_code }}</td>
            </tr>
            <tr>
                <th>Arrival Date:</th>
                <td>{{ $reservation->arrival_date ? $reservation->arrival_date->format('d/m/Y') : '' }}</td>
            </tr>
            <tr>
                <th>Departure Country:</th>
                <td>{{ $reservation->countryFrom->name ?? '' }}</td>
            </tr>
            <tr>
                <th>Arrival Country:</th>
                <td>{{ $reservation->branch->country->name ?? '' }}</td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>This is an official insurance document for children under 6 years of age.</p>
        <p>Issued on {{ now()->format('d/m/Y') }}</p>
    </div>
</body>
</html> 