<!DOCTYPE html>
<html>
<head>
    <title>Chat Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; white-space: pre-wrap; }
        .log { height: 200px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace; }
        select { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Chat Debug Test Page</h1>
        
        <div class="test-section">
            <h3>📊 Available Users</h3>
            @foreach($users as $user)
                <div>ID: {{ $user->id }} - Name: {{ $user->name }}</div>
            @endforeach
        </div>

        <div class="test-section">
            <h3>📤 Send Test Message</h3>
            <select id="fromUser">
                <option value="">Select From User</option>
                @foreach($users as $user)
                    <option value="{{ $user->id }}">{{ $user->name }} (ID: {{ $user->id }})</option>
                @endforeach
            </select>
            
            <select id="toUser">
                <option value="">Select To User</option>
                @foreach($users as $user)
                    <option value="{{ $user->id }}">{{ $user->name }} (ID: {{ $user->id }})</option>
                @endforeach
            </select>
            
            <button class="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="sendResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔊 Pusher Connection Test</h3>
            <button class="button" onclick="testPusherConnection()">Test Pusher Connection</button>
            <button class="button" onclick="subscribeToChatChannel()">Subscribe to Chat Channel</button>
            <div id="pusherResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 Real-time Events Log</h3>
            <button class="button" onclick="clearLog()">Clear Log</button>
            <div id="eventLog" class="log"></div>
        </div>
    </div>

    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <script>
        let pusher;
        let channel;
        
        function log(message) {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        function sendTestMessage() {
            const fromUser = document.getElementById('fromUser').value;
            const toUser = document.getElementById('toUser').value;
            
            if (!fromUser || !toUser) {
                alert('Please select both from and to users');
                return;
            }
            
            const resultDiv = document.getElementById('sendResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Sending message...';
            
            fetch(`/debug/test-message/${fromUser}/${toUser}`)
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    log(`📤 Test message sent: ${JSON.stringify(data)}`);
                })
                .catch(error => {
                    resultDiv.textContent = 'Error: ' + error.message;
                    log(`❌ Error sending message: ${error.message}`);
                });
        }

        function testPusherConnection() {
            const resultDiv = document.getElementById('pusherResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing Pusher connection...';
            
            try {
                pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
                    cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
                });
                
                pusher.connection.bind('connected', function() {
                    resultDiv.textContent = '✅ Pusher connected successfully!';
                    log('✅ Pusher connected successfully');
                });
                
                pusher.connection.bind('error', function(err) {
                    resultDiv.textContent = '❌ Pusher connection error: ' + JSON.stringify(err);
                    log('❌ Pusher connection error: ' + JSON.stringify(err));
                });
                
            } catch (error) {
                resultDiv.textContent = '❌ Pusher initialization error: ' + error.message;
                log('❌ Pusher initialization error: ' + error.message);
            }
        }

        function subscribeToChatChannel() {
            if (!pusher) {
                alert('Please test Pusher connection first');
                return;
            }
            
            try {
                channel = pusher.subscribe('chat');
                
                channel.bind('pusher:subscription_succeeded', function() {
                    log('✅ Successfully subscribed to chat channel');
                });
                
                channel.bind('pusher:subscription_error', function(error) {
                    log('❌ Failed to subscribe to chat channel: ' + JSON.stringify(error));
                });
                
                channel.bind('new-message', function(data) {
                    log('🟢 New message received: ' + JSON.stringify(data));
                });
                
            } catch (error) {
                log('❌ Channel subscription error: ' + error.message);
            }
        }

        // Auto-initialize
        window.addEventListener('load', function() {
            log('🚀 Debug page loaded');
            testPusherConnection();
            setTimeout(() => {
                subscribeToChatChannel();
            }, 1000);
        });
    </script>
</body>
</html> 