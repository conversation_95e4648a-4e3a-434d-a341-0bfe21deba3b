<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الإشعارات - CRM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 font-sans">
    <div class="min-h-screen py-8">
        <div class="max-w-6xl mx-auto px-4">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    🔔 اختبار نظام الإشعارات
                </h1>
                <p class="text-gray-600">
                    صفحة شاملة لاختبار جميع ميزات نظام الإشعارات في تطبيق CRM
                </p>
                <div class="mt-4 flex flex-wrap gap-2">
                    <a href="{{ $testData['test_links']['create_test_notifications'] }}" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        إنشاء إشعارات تجريبية
                    </a>
                    <a href="{{ $testData['test_links']['view_all_notifications'] }}" 
                       class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        عرض جميع الإشعارات
                    </a>
                    <a href="{{ url('/admin') }}" 
                       class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        لوحة التحكم الرئيسية
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- System Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-cogs text-blue-500 mr-2"></i>
                        حالة النظام
                    </h2>
                    
                    <div class="space-y-3">
                        @foreach($testData['system_status'] as $key => $value)
                            @if(is_array($value))
                                <div class="border rounded-lg p-3">
                                    <h3 class="font-medium text-gray-800 mb-2">{{ ucfirst(str_replace('_', ' ', $key)) }}</h3>
                                    @foreach($value as $subKey => $subValue)
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-sm text-gray-600">{{ $subKey }}</span>
                                            <span class="flex items-center">
                                                @if($subValue)
                                                    <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                                    <span class="text-green-600 text-sm">✓</span>
                                                @else
                                                    <i class="fas fa-times-circle text-red-500 mr-1"></i>
                                                    <span class="text-red-600 text-sm">✗</span>
                                                @endif
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-700">{{ ucfirst(str_replace('_', ' ', $key)) }}</span>
                                    <span class="flex items-center">
                                        @if($value)
                                            <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                            <span class="text-green-600">✓ Active</span>
                                        @else
                                            <i class="fas fa-times-circle text-red-500 mr-1"></i>
                                            <span class="text-red-600">✗ Inactive</span>
                                        @endif
                                    </span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- User Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-user text-green-500 mr-2"></i>
                        معلومات المستخدم
                    </h2>
                    
                    <div class="space-y-3">
                        @foreach($testData['user_info'] as $key => $value)
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 font-medium">{{ ucfirst(str_replace('_', ' ', $key)) }}</span>
                                <span class="text-gray-900">{{ $value }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-chart-bar text-purple-500 mr-2"></i>
                    إحصائيات الإشعارات
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $testData['statistics']['total_notifications'] }}</div>
                        <div class="text-sm text-blue-800">إجمالي الإشعارات</div>
                    </div>
                    <div class="bg-red-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-red-600">{{ $testData['statistics']['unread_notifications'] }}</div>
                        <div class="text-sm text-red-800">غير مقروءة</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $testData['statistics']['total_notifications'] - $testData['statistics']['unread_notifications'] }}</div>
                        <div class="text-sm text-green-800">مقروءة</div>
                    </div>
                    <div class="bg-yellow-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-600">{{ count($testData['statistics']['notification_types']) }}</div>
                        <div class="text-sm text-yellow-800">أنواع الإشعارات</div>
                    </div>
                </div>

                <!-- Notification Types -->
                @if(count($testData['statistics']['notification_types']) > 0)
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-3">توزيع أنواع الإشعارات</h3>
                        <div class="space-y-2">
                            @foreach($testData['statistics']['notification_types'] as $type => $count)
                                <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                                    <span class="font-medium text-gray-700">
                                        @switch($type)
                                            @case('chat_message')
                                                💬 رسائل الدردشة
                                                @break
                                            @case('system')
                                                🔔 إشعارات النظام
                                                @break
                                            @case('instruction')
                                                📋 التعليمات
                                                @break
                                            @default
                                                📧 {{ $type }}
                                        @endswitch
                                    </span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">{{ $count }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Recent Notifications -->
                @if(count($testData['statistics']['recent_notifications']) > 0)
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 mb-3">آخر الإشعارات</h3>
                        <div class="space-y-3">
                            @foreach($testData['statistics']['recent_notifications'] as $notification)
                                <div class="border rounded-lg p-4 {{ is_null($notification->read_at) ? 'bg-blue-50 border-blue-200' : 'bg-gray-50' }}">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 space-x-reverse mb-2">
                                                <span class="text-lg">{{ $notification->icon ?? '🔔' }}</span>
                                                <span class="font-medium text-gray-900">{{ $notification->title }}</span>
                                                @if(is_null($notification->read_at))
                                                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">جديد</span>
                                                @endif
                                            </div>
                                            <p class="text-gray-600 text-sm mb-2">{{ $notification->message }}</p>
                                            <div class="text-xs text-gray-500">
                                                {{ $notification->created_at->diffForHumans() }}
                                                @if($notification->from_user)
                                                    • من: {{ $notification->from_user->name }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-bell-slash text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لا توجد إشعارات حتى الآن</p>
                        <a href="{{ $testData['test_links']['create_test_notifications'] }}" 
                           class="inline-block mt-4 bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                            إنشاء إشعارات تجريبية
                        </a>
                    </div>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                    إجراءات سريعة
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testNotificationSound()" 
                            class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                        <i class="fas fa-volume-up mr-2"></i>
                        اختبار صوت الإشعار
                    </button>
                    
                    <button onclick="testBrowserNotification()" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                        <i class="fas fa-desktop mr-2"></i>
                        اختبار إشعار المتصفح
                    </button>
                    
                    <button onclick="refreshPage()" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center">
                        <i class="fas fa-sync-alt mr-2"></i>
                        تحديث الصفحة
                    </button>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-8 text-gray-500">
                <p>© 2024 نظام إدارة علاقات العملاء - تم تطوير نظام الإشعارات بنجاح</p>
            </div>
        </div>
    </div>

    <script>
        function testNotificationSound() {
            // Create a simple audio context for testing
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
                
                alert('تم تشغيل صوت الإشعار التجريبي!');
            } catch (error) {
                alert('فشل في تشغيل الصوت: ' + error.message);
            }
        }

        function testBrowserNotification() {
            if (Notification.permission === 'granted') {
                new Notification('إشعار تجريبي', {
                    body: 'هذا إشعار تجريبي من نظام CRM',
                    icon: '/favicon.ico',
                    tag: 'test-notification'
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('إشعار تجريبي', {
                            body: 'هذا إشعار تجريبي من نظام CRM',
                            icon: '/favicon.ico',
                            tag: 'test-notification'
                        });
                    }
                });
            } else {
                alert('إشعارات المتصفح محجوبة. يرجى تفعيلها من إعدادات المتصفح.');
            }
        }

        function refreshPage() {
            window.location.reload();
        }

        // Auto-refresh statistics every 30 seconds
        setTimeout(() => {
            refreshPage();
        }, 30000);
    </script>
</body>
</html> 