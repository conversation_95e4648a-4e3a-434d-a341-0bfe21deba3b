<div
    style="display: flex; align-items: center; justify-content: center; gap: 5px"
    x-on:keydown.enter
    x-data="{
        sidebarSearch: '',
        showCancel: false,
        callSearch: () => {
            const url = new URL(window.location.href);
            if (sidebarSearch) {
                url.searchParams.set('sidebarSearch', sidebarSearch);
            } else {
                url.searchParams.delete('sidebarSearch');
            }
            window.location = url.href;
        }
    }"
    x-init="() => {
        const urlParams = new URLSearchParams(window.location.search);

        if (urlParams.has('sidebarSearch')) {
            sidebarSearch = urlParams.get('sidebarSearch');
            showCancel = true;
        }
    }"
>
    <div class="flex items-center">
        <input
            x-on:keydown.enter="() => {
                const url = new URL(window.location.href);
                if (sidebarSearch) {
                    url.searchParams.set('sidebarSearch', sidebarSearch);
                } else {
                    url.searchParams.delete('sidebarSearch');
                }
                window.location = url.href;
            }"
            type="text"
            x-model="sidebarSearch"
            placeholder="Search..."
            class="w-full border rounded"
            style="margin: 10px; color: black"
        />
    </div>

    <button
        x-show="!showCancel"
        x-on:click="() => {
            const url = new URL(window.location.href);
            if (sidebarSearch) {
                url.searchParams.set('sidebarSearch', sidebarSearch);
            } else {
                url.searchParams.delete('sidebarSearch');
            }
            window.location = url.href;
        }"
    >
        {{ __('Search') }}
    </button>

{{--    cancel button--}}
    <button
        x-show="showCancel"
        x-on:click="() => {
            sidebarSearch = '';
            // navigate to the search page without the search query
            const url = new URL(window.location.href);
            url.searchParams.delete('sidebarSearch');

            window.location = url.href;
        }"
        x-show="sidebarSearch"
        style="margin-left: 10px"
    >
        {{ __('Cancel') }}
    </button>
</div>
