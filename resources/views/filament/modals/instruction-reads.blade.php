<div class="space-y-4">
    <div class="bg-blue-50 p-4 rounded-lg">
        <div class="flex items-center">
            <svg class="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-blue-800 font-medium">{{ $reads->count() }} user(s) have read this instruction</span>
        </div>
    </div>

    <div class="max-h-96 overflow-y-auto">
        <div class="space-y-3">
            @foreach($reads as $read)
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">
                                        {{ $read->user->name ?? 'Unknown User' }}
                                    </h4>
                                    <p class="text-sm text-gray-500">
                                        Role: {{ $read->user->role ?? 'N/A' }}
                                    </p>
                                    @if($read->user->email)
                                        <p class="text-xs text-gray-400">
                                            {{ $read->user->email }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500">
                                {{ $read->created_at->format('M j, Y') }}
                            </p>
                            <p class="text-xs text-gray-400">
                                {{ $read->created_at->format('g:i A') }}
                            </p>
                            <p class="text-xs text-gray-400 mt-1">
                                {{ $read->created_at->diffForHumans() }}
                            </p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    
    @if($reads->count() > 0)
        <div class="bg-gray-50 p-3 rounded-lg">
            <div class="text-sm text-gray-600">
                <span class="font-medium">Statistics:</span>
                <ul class="mt-2 space-y-1">
                    <li>• First read: {{ $reads->last()->created_at->format('M j, Y g:i A') }}</li>
                    <li>• Latest read: {{ $reads->first()->created_at->format('M j, Y g:i A') }}</li>
                    <li>• Total reads: {{ $reads->count() }}</li>
                </ul>
            </div>
        </div>
    @endif
</div> 