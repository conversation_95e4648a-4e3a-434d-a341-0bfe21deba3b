<!DOCTYPE html>
<head>
  <title>Pusher Test</title>
  <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
  <script>

    // Enable pusher logging - don't include this in production
    Pusher.logToConsole = true;

    var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
      cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}'
    });

    var channel = pusher.subscribe('my-channel');
    channel.bind('my-event', function(data) {
      alert(JSON.stringify(data));
    });
  </script>
</head>
<body>
  <h1>Pusher Test</h1>
  <p>
    Try publishing an event to channel <code>my-channel</code>
    with event name <code>my-event</code>.
  </p>
  <p>
    <a href="/test-pusher-simple" target="_blank">Click here to fire the event: event(new MyEvent('hello world'))</a>
  </p>
  <p>
    Current configuration:
    <br>PUSHER_APP_KEY: {{ env('PUSHER_APP_KEY', 'NOT SET') }}
    <br>PUSHER_APP_CLUSTER: {{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}
    <br>BROADCAST_DRIVER: {{ env('BROADCAST_DRIVER', 'NOT SET') }}
  </p>
</body> 