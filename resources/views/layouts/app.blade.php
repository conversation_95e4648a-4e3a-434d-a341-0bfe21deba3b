<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @livewireStyles
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            {{-- Navigation is handled by Filament for this project --}}
            {{-- @include('layouts.navigation') --}}

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main class="@auth mr-80 @endauth">
                {{ $slot }}
            </main>

            <!-- Chat Sidebar -->
            @auth
                {{-- @include('components.layouts.chat-sidebar') --}}
            @endauth

            <!-- Instruction Notifications -->
            @auth
                @php
                    $user = auth()->user();
                    $primaryRole = $user ? $user->getPrimaryRole() : null;
                    $hasInstructionPermissions = $primaryRole && \App\Models\InstructionPermission::where('to_role', $primaryRole)->where('is_active', true)->exists();
                @endphp
                
                @if($hasInstructionPermissions)
                    @livewire('instruction-notifications')
                @endif
            @endauth
        </div>

        @livewireScripts
        <script>
            // Listen for new messages
            Livewire.on('new-message', (userId, message) => {
                // Play notification sound
                const audio = new Audio('/notification.mp3');
                audio.play();

                // Update messages if chat is open
                if (document.querySelector(`[data-chat-id="${userId}"]`)) {
                    Livewire.emit('message-received', userId, message);
                }
            });
        </script>


    </body>
</html> 