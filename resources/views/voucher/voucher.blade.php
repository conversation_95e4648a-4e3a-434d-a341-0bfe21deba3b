<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <!-- title -->
    <title>{{ $brand?->name ?? $reservation->company?->name }}</title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="/voucher/images/logo.jpg" />
    <!--    first mobile meta-->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!--    internet explorer compatibility meta-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- ## styles -->
    <link rel="stylesheet" href="/voucher/css/assets/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="/voucher/css/style.css" />
</head>

<body>

    <main>
        <section class="bill">
            <div class="row">
                <div class="col-10">
                    <img src="/voucher/images/header-2.jpg" class="w-100" alt="header">
                </div>
                <div class="col-2">
                    <div class="logo-box">
                        <img src="{{ $reservation->company?->fileUrl('logo') ?? $brand?->fileUrl('logo') }}" class="logo" alt="logo">
                    </div>
                </div>
            </div>

            <div class="container big-container">

                <table class="table table-striped table-bordered ">
                    <thead>
                        <tr>
                            <th scope="col">RESERVATION CONFIRMATION VOUCHER</th>
                            <th scope="col" style="text-align: right">تأكيد الحجوزات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th>
                                <span>Voucher Number:</span>
                                <span>{{ $reservation->id }}</span>
                            </th>
                            <td style="text-align: right; direction: rtl">
                                <span style="font-weight: bold">رقم الفوتشر:</span>
                                <span>{{ $reservation->id }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>{{ $reservation->customer_name_en ?? $reservation->company?->name }}</th>
                            <td style="text-align: right">
                               {{ $reservation->customer_name ?? $reservation->company?->name }}</td>
                        </tr>
                        {{--            @if (!$reservation->company) --}}
                        <tr>
                            <th>
                                <span>
                                    Passport.no:
                                </span>
                                <span>
                                    {{ $reservation->passport_number }}
                                </span>
                            </th>
                            <td style="text-align: right; direction: rtl">
                                <span style="font-weight: bold">رقم الباسبور: </span>
                                <span>{{ $reservation->passport_number }}</span>
                            </td>
                        </tr>
                        {{--            @endif --}}
                        <tr>


                        @if ($reservation->people->count() > 0)


                        @foreach ($reservation->people as $key=> $person)
                        @if($person->name && $person->passport_number)
                            <tr>
                                <th>

                                    <span>
                                    {{ $person->name ?? $person->name }}
                                    </span>
                                </th>
                                <td style="text-align: right; direction: rtl">

                                    <span>{{ $person->name ?? $person->name }}</span>
                                </td>

                            </tr>
                            <tr>
                                <th>
                                    <span>{{ __('Passport.no') }} {{ $key+1 }}</span>
                                    <span>{{ $person->passport_number }}</span>
                                </th>
                                <td style="text-align: right; direction: rtl">
                                    <span style="font-weight: bold">رقم الباسبور: </span>
                                    <span>{{ $person->passport_number }}</span>
                                </td>
                            </tr>
                              @endif
                        @endforeach
                        @endif

                        @if ($reservation->kidsUnder6->count() > 0)
                        @if ($reservation->kidsUnder6->contains(function($kid) { return $kid->name && $kid->passport_number; }))
                            <tr>
                                <th colspan="2" >
                                    {{ __('Kids Under 6') }}
                                </th>
                            </tr>
                        @endif
                            @foreach ($reservation->kidsUnder6 as $key => $kid)
                                @if ($kid->name && $kid->passport_number)
                                    <tr>
                                        <th>
                                            <span>
                                                {{ $kid->name ?? $kid->name }}
                                            </span>
                                        </th>
                                        <td style="text-align: right; direction: rtl">

                                            <span>{{ $kid->name ?? $kid->name }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>
                                            <span>{{ __('Passport.no') }} {{ $key + 1 }}</span>
                                            <span>{{ $kid->passport_number }}</span>
                                        </th>
                                        <td style="text-align: right; direction: rtl">
                                            <span style="font-weight: bold">رقم الباسبور: </span>
                                            <span>{{ $kid->passport_number }}</span>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        @endif
                        <th>Arrival Date: {{ \Carbon\Carbon::parse($reservation->arrival_date)->format('d m Y \A\T H:i') }}</th>
                        <td style="text-align: right; direction: rtl">
                            <span style="font-weight: bold">تاريخ الوصول:</span>
                            <span>{{ \Carbon\Carbon::parse($reservation->arrival_date)->format('d m Y \A\T H:i') }}</span>
                        </td>
                        </tr>
                        @php
                            $carType =
                                $reservation->transportationCompanyBookings?->first()?->carType ??
                                $reservation->carType;
                        @endphp
                        <tr>
                            <th>Car Type : <span class="fw-normal">{{ $carType?->name }}</span></th>
                            <td class="fw-bold dir-rtl">نوع السيارة : <span
                                    class="fw-normal">{{ $carType?->name_ar }}</span></td>
                        </tr>
                        <tr>
                            <th>Destination Country : <span
                                    class="fw-normal">{{ $reservation->arrivalCountry?->name }}</span></th>
                            <td class="fw-bold dir-rtl">بلد الوجهة : <span
                                    class="fw-normal">{{ $reservation->arrivalCountry?->name_ar }}</span></td>
                        </tr>
                        <tr>
                            <th>Reference Number : <span class="fw-normal">{{ $reservation->trip_code }}</span></th>
                            <td class="fw-bold dir-rtl">كود الرحلة : <span
                                    class="fw-normal">{{ $reservation->trip_code }}</span></td>
                        </tr>
                    </tbody>
                </table>

                <div class="detials" style="margin-top: 1rem; margin-bottom: 1rem;">

                    <div class="row">
                        <div class="col-6" style="color: red !important;">
                            <p class="alert-text pe-5" style="color: red; text-align: left; font-weight: bold">
                                {!! $brand?->deposit_info_en !!}
                            </p>
                        </div>
                        <div class="col-6" style="color: red !important;">
                            <p class="alert-text text-end" style="color: red; text-align: right; font-weight: bold">
                                {!! $brand?->deposit_info_ar !!}
                            </p>
                        </div>
                    </div>

                </div>

                @if (count($hotelBookings = $reservation->hotelBookings) > 0)
                    <table class="table table-striped table-bordered text-center">
                        <thead>
                            <tr>
                                <th scope="col" colspan="5">Hotels Confirmation / تأكيد الفنادق</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <th>City</th>
                                <th>Hotels</th>
                                <th>Check in</th>
                                <th>Check out</th>
                                <th>Room Types</th>
                            </tr>
                            @foreach ($hotelBookings as $hotelBooking)
                                <tr>
                                    <td>{{ $hotelBooking->city?->name }}</td>
                                    <td>{{ $hotelBooking->hotel?->name }}</td>
                                    <td>{{ \Carbon\Carbon::parse($hotelBooking->check_in)->format('d/m/Y') }}</td>
                                    <td>{{ \Carbon\Carbon::parse($hotelBooking->check_out)->format('d/m/Y') }}</td>
                                    <td>
                                        <ul>
                                            @foreach ($hotelBooking->hotelBookingRooms as $room)
                                                <li>
                                                    {{ $room->getRoomName() }} (X{{ $room->count }})
                                                </li>
                                            @endforeach
                                        </ul>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @endif

                <div class="detials d-flex flex-column gap-2 justify-content-center align-items-center"
                    style="margin-bottom: 0; margin-top: 1rem; text-align: center">
                    {!! $brand?->host_info !!}
                </div>

            </div>
            <div class="container">
                <footer style="margin-top: 1rem">
                    <div style="display: flex; align-items: center; justify-content: space-between">

                        <div style="text-align: left">
                            {!! $brand?->customer_support_en !!}
                        </div>

                        <div>
                            <img src="{{ $reservation->company?->fileUrl('logo') ?? $brand?->fileUrl('logo') }}" style="height: 200px; width: auto" class="logo" alt="logo">
                        </div>

                        <div style="text-align: right">
                            {!! $brand?->customer_support_ar !!}
                        </div>

                    </div>
                </footer>
            </div>

        </section>
    </main>


</body>

</html>
