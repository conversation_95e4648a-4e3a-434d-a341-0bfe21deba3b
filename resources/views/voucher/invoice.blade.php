<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8" />
  <!-- title -->
  <title>4 Seasons Tours</title>
  <!-- Favicon -->
  <link rel="shortcut icon" href="/voucher/images/logo.jpg" />
  <!--    first mobile meta-->
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <!--    internet explorer compatibility meta-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />

  <!-- ## styles -->
  <link rel="stylesheet" href="/voucher/css/assets/bootstrap/bootstrap.min.css" />
  <link rel="stylesheet" href="/voucher/css/style.css" />
</head>

<body>

  <main>
    <section class="bill">
      <img src="/voucher/images/header-1.jpg" class="w-100" alt="header">
      <div class="logo-box">
          @if($reservation->company)
              <img src="{{ $reservation->company?->fileUrl('logo') }}"
                   class="logo" alt="logo">
          @endif
          <img src="{{ $brand?->fileUrl('logo') }}"
               class="logo" alt="logo">
      </div>
      <div class="container big-container">
        <div class="detials mt-0">
          <div class="row">
            <div class="col-10 col-xl-6  d-flex flex-column main-gap">
              <div class="row">
                <div class="col-2">
                  <h3>Invoice Number:</h3>
                </div>
                <div class="col-10">
                  <h4> {{ $reservation->id }}</h4>
                </div>
              </div>

              <div class="row">
                <div class="col-2">
                  <h3>Date:</h3>
                </div>
                <div class="col-10">
                  <h4> {{ now()->toDateString() }}</h4>
                </div>
              </div>

              <div class="row">
                <div class="col-2">
                  <h3> Bill to: </h3>
                </div>
                <div class="col-10">
                  <h4>{{ $reservation->company?->name ?? $reservation->customer_name_en ?? $reservation->customer_name }}</h4>
                </div>
              </div>
            </div>
          </div>

        </div>

        <table class="table table-striped table-bordered ">
          <thead>
            <tr>
              <th scope="col">Item</th>
              <th scope="col">Description</th>
              <th scope="col"> Vat</th>
              <th scope="col">Total</th>
              <th scope="col">Paid</th>
              <th scope="col">Left</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th>{{ $reservation->trip_code }}</th>
              <td>Package</td>
              <td>%18</td>
              <td>{{ $reservation->trip_price }}$</td>
              <td>{{ $reservation->total_paid }}$</td>
              <td>{{ $reservation->total_remaining }}$</td>
            </tr>
          </tbody>

        </table>

        <div class="detials">
          <div class="row justify-content-end">
            <div class="col-10 col-xl-3 d-flex flex-column main-gap ">
              <div class="row">
                <div class="col-5">
                  <h3>Total:</h3>
                </div>
                <div class="col-7">
                  <h4 class="text-end"> ${{ $reservation->trip_price }}</h4>
                </div>
              </div>

              <div class="row">
                <div class="col-5">
                  <h3>Balance due</h3>
                </div>
                <div class="col-7">
                  <h4 class="text-end"> ${{ $reservation->trip_price }}</h4>
                </div>
              </div>

            </div>
          </div>

        </div>

        <div class="detials d-flex flex-column gap-2 text-center">
            {!! $brand?->host_info !!}
        </div>

      </div>
      <div class="container">
          <div style="display: flex; align-items: center; justify-content: space-between">

              <div style="text-align: left">
                  {!! $brand?->customer_support_en !!}
              </div>

              <div style="display: flex; flex-direction: row; gap: 10px">
                  @if($reservation->company)
                      <img src="{{ $reservation->company?->fileUrl('logo') }}"
                           style="height: auto; width: 100px" class="logo" alt="logo">
                  @endif
                  <img src="{{ $brand?->fileUrl('logo') }}"
                       style="height: auto; width: 100px" class="logo" alt="logo">
              </div>

              <div style="text-align: right">
                  {!! $brand?->customer_support_ar !!}
              </div>

          </div>
      </div>

    </section>
  </main>


</body>

</html>
