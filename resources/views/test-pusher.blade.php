<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Pusher Chat Test</title>
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .section {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #007cba;
        }
        input, button {
            padding: 10px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Clean Pusher Chat Test - No Polling!</h1>
    <p>This demonstrates real-time messaging using only Pusher events, without any polling that slows down the website.</p>
    
    <div class="status" id="connectionStatus">
        Connecting to Pusher...
    </div>

    <div class="container">
        <div class="section">
            <h3>📤 Send Message</h3>
            <input type="text" id="messageInput" placeholder="Type your message here..." />
            <button onclick="sendMessage()">Send Message</button>
            <p><small>This will trigger a Laravel event that broadcasts via Pusher</small></p>
        </div>

        <div class="section">
            <h3>📥 Received Messages</h3>
            <div class="messages" id="messagesContainer">
                <p><em>Waiting for messages...</em></p>
            </div>
            <p><small>Messages appear here instantly via Pusher (no polling!)</small></p>
        </div>
    </div>

    <div class="section">
        <h3>🧪 Test Functions</h3>
        <button onclick="testPusherConnection()">Test Pusher Connection</button>
        <button onclick="simulateMessage()">Simulate Incoming Message</button>
        <button onclick="clearMessages()">Clear Messages</button>
    </div>

    <div class="section">
        <h3>🔧 Configuration Info</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace;">
            <strong>Current Pusher Configuration:</strong><br>
            App Key: {{ env('PUSHER_APP_KEY', 'NOT SET') }}<br>
            App ID: {{ env('PUSHER_APP_ID', 'NOT SET') }}<br>
            Cluster: {{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}<br>
            Broadcast Driver: {{ env('BROADCAST_DRIVER', 'NOT SET') }}
        </div>
    </div>

    <script>
        // Enable pusher logging for debugging
        Pusher.logToConsole = true;

        // Initialize Pusher with your credentials
        var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
            cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
        });

        const statusEl = document.getElementById('connectionStatus');
        const messagesEl = document.getElementById('messagesContainer');

        // Connection event handlers
        pusher.connection.bind('connected', function() {
            console.log('✅ Pusher connected successfully');
            console.log('Socket ID:', pusher.connection.socket_id);
            console.log('Connection state:', pusher.connection.state);
            statusEl.textContent = '✅ Connected to Pusher (Socket: ' + pusher.connection.socket_id + ')';
            statusEl.className = 'status connected';
        });

        pusher.connection.bind('error', function(err) {
            console.log('❌ Pusher connection error:', err);
            console.log('Error details:', JSON.stringify(err, null, 2));
            statusEl.textContent = '❌ Pusher connection error: ' + (err.error?.data?.code || err.type || 'Unknown error');
            statusEl.className = 'status error';
            
            // Add detailed error info
            addMessage('Connection Error', JSON.stringify(err, null, 2));
        });

        pusher.connection.bind('disconnected', function() {
            console.log('⚠️ Pusher disconnected');
            statusEl.textContent = '⚠️ Disconnected from Pusher';
            statusEl.className = 'status error';
        });

        pusher.connection.bind('connecting', function() {
            console.log('🔄 Connecting to Pusher...');
            statusEl.textContent = '🔄 Connecting to Pusher...';
            statusEl.className = 'status';
        });

        pusher.connection.bind('unavailable', function() {
            console.log('❌ Pusher unavailable');
            statusEl.textContent = '❌ Pusher service unavailable';
            statusEl.className = 'status error';
        });

        pusher.connection.bind('failed', function() {
            console.log('❌ Pusher connection failed');
            statusEl.textContent = '❌ Pusher connection failed';
            statusEl.className = 'status error';
        });

        // Subscribe to the chat channel
        var channel = pusher.subscribe('chat');
        
        channel.bind('pusher:subscription_succeeded', function() {
            console.log('✅ Successfully subscribed to chat channel');
            addMessage('System', '✅ Successfully subscribed to chat channel');
        });

        channel.bind('pusher:subscription_error', function(error) {
            console.log('❌ Failed to subscribe to chat channel:', error);
            addMessage('System', '❌ Failed to subscribe to chat channel: ' + error);
        });

        // Listen for new messages
        channel.bind('new-message', function(data) {
            console.log('🟢 Received message via Pusher:', data);
            
            const messageText = `From User ${data.senderId} to User ${data.receiverId}: ${data.message}`;
            addMessage('Pusher Event', messageText);
        });

        // Helper function to add messages to the display
        function addMessage(source, text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <strong>${source}</strong> <small>(${new Date().toLocaleTimeString()})</small><br>
                ${text}
            `;
            
            messagesEl.appendChild(messageDiv);
            messagesEl.scrollTop = messagesEl.scrollHeight;
            
            // Remove the "waiting" message if it exists
            const waitingMsg = messagesEl.querySelector('p em');
            if (waitingMsg) {
                waitingMsg.parentElement.remove();
            }
        }

        // Send message function (calls Laravel backend)
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                alert('Please enter a message');
                return;
            }

            try {
                // Call Laravel backend to send the message
                const response = await fetch('/test-send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify({
                        message: message,
                        from_user_id: 1,
                        to_user_id: 2
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    addMessage('Laravel Backend', `✅ Message sent successfully: "${message}"`);
                    addMessage('System', `Broadcasting to Pusher with ID: ${result.data.message_id}`);
                    input.value = '';
                } else {
                    addMessage('Error', `❌ Failed to send message: ${result.message}`);
                }
            } catch (error) {
                console.error('Error sending message:', error);
                addMessage('Error', `❌ Network error: ${error.message}`);
            }
        }

        // Test functions
        function testPusherConnection() {
            addMessage('Test', `Connection state: ${pusher.connection.state}`);
            addMessage('Test', `Socket ID: ${pusher.connection.socket_id || 'Not connected'}`);
            addMessage('Test', `App Key: {{ env('PUSHER_APP_KEY', 'NOT SET') }}`);
            addMessage('Test', `Cluster: {{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}`);
            addMessage('Test', `Pusher Version: ${Pusher.VERSION || 'Unknown'}`);
            
            // Test if we can access Pusher properties
            try {
                addMessage('Test', `Connection options: ${JSON.stringify(pusher.config, null, 2)}`);
            } catch (e) {
                addMessage('Test', `Could not access Pusher config: ${e.message}`);
            }
        }

        function simulateMessage() {
            // Simulate receiving a message (for testing purposes)
            const testData = {
                message_id: Math.floor(Math.random() * 1000),
                senderId: Math.floor(Math.random() * 10) + 1,
                receiverId: Math.floor(Math.random() * 10) + 1,
                message: 'This is a simulated test message - ' + new Date().toLocaleTimeString(),
                filePaths: null
            };
            
            // Manually trigger the event handler
            channel.bind('new-message', function(data) {
                console.log('🧪 Simulated message:', data);
            });
            
            addMessage('Simulation', `Simulated message: ${testData.message}`);
        }

        function clearMessages() {
            messagesEl.innerHTML = '<p><em>Messages cleared...</em></p>';
        }

        // Show instructions
        console.log('🚀 Clean Pusher Chat Test Loaded!');
        console.log('📝 To test real messaging:');
        console.log('1. Open browser console to see Pusher events');
        console.log('2. Use testPusherConnection() to check connection');
        console.log('3. Use simulateMessage() to test message display');
        console.log('4. In Laravel, broadcast events using: broadcast(new MessageSent(...))');
    </script>
</body>
</html> 