<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إشعارات طرق الدفع الأخرى</title>
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 20px 0;
            direction: ltr;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .notification-demo {
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(45deg, #f8f9ff, #fff8f9);
        }
        .event-counter {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            margin: 20px 0;
        }
        .config-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
        }
        h1 { color: #667eea; text-align: center; }
        h2 { color: #764ba2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار إشعارات طرق الدفع الأخرى</h1>
        <p style="text-align: center; font-size: 18px;">هذه الصفحة لاختبار إشعارات "طرق الدفع الأخرى" في شريط التنقل</p>

        <!-- Connection Status -->
        <div id="connection-status" class="status connecting">
            🔄 جاري الاتصال بـ Pusher...
        </div>

        <!-- Event Counter -->
        <div class="notification-demo">
            <h2>📊 عداد الإشعارات المستلمة</h2>
            <div class="event-counter" id="event-count">0</div>
            <div id="last-notification" style="text-align: center; padding: 15px; background: white; border-radius: 8px; margin-top: 15px;">
                <em>لم يتم استلام أي إشعارات بعد...</em>
            </div>
        </div>

        <!-- Configuration -->
        <div class="config-info">
            <h3>⚙️ معلومات التكوين</h3>
            <strong>PUSHER_APP_KEY:</strong> {{ env('PUSHER_APP_KEY', 'غير محدد') }}<br>
            <strong>PUSHER_APP_CLUSTER:</strong> {{ env('PUSHER_APP_CLUSTER', 'غير محدد') }}<br>
            <strong>معرف المستخدم:</strong> {{ auth()->id() ?? 'غير مسجل الدخول' }}<br>
            <strong>القناة:</strong> financial-notifications-{{ auth()->id() ?? 'N/A' }}<br>
            <strong>الحدث:</strong> financial-notification-received
        </div>

        <!-- Test Controls -->
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testConnection()">🔍 اختبار الاتصال</button>
            <button onclick="simulatePayment()">💳 محاكاة دفعة جديدة</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>

        <!-- Log -->
        <div class="log" id="log"></div>

        <!-- Instructions -->
        <div class="notification-demo">
            <h3>📋 التعليمات</h3>
            <ol>
                <li>تأكد من تسجيل الدخول كمستخدم إداري</li>
                <li>افتح شريط التنقل وراقب أيقونة الإشعارات</li>
                <li>اضغط على "محاكاة دفعة جديدة" لإنشاء دفعة تجريبية</li>
                <li>يجب أن تظهر الإشعارات فوراً في شريط التنقل</li>
                <li>يجب أن تسمع صوت إشعار</li>
            </ol>
        </div>
    </div>

    <script>
        let eventCount = 0;
        let pusher = null;
        let channel = null;

        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logEl.innerHTML += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }

        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connection-status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function testConnection() {
            if (pusher) {
                log(`🔍 حالة الاتصال: ${pusher.connection.state}`);
                log(`🆔 معرف المقبس: ${pusher.connection.socket_id || 'غير متصل'}`);
                log(`📡 القناة: financial-notifications-{{ auth()->id() ?? 'N/A' }}`);
            } else {
                log('❌ Pusher غير مهيأ');
            }
        }

        function simulatePayment() {
            log('🚀 إرسال طلب لإنشاء دفعة تجريبية...');
            
            fetch('/test-create-other-payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    test: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    log('✅ تم إنشاء الدفعة التجريبية بنجاح');
                    log(`💳 معرف الدفعة: ${data.payment_id}`);
                } else {
                    log('❌ فشل في إنشاء الدفعة التجريبية: ' + data.message);
                }
            })
            .catch(error => {
                log('❌ خطأ في الطلب: ' + error.message);
            });
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل');
        }

        function playNotificationSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
                
                log('🔊 تم تشغيل صوت الإشعار');
            } catch (error) {
                log('❌ فشل في تشغيل الصوت: ' + error.message);
            }
        }

        // Initialize Pusher when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تهيئة Pusher...');
            
            try {
                // Enable pusher logging
                Pusher.logToConsole = true;

                // Initialize Pusher
                pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
                    cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
                    encrypted: true,
                });

                // Connection event handlers
                pusher.connection.bind('connected', function() {
                    log('✅ تم الاتصال بـ Pusher بنجاح');
                    updateConnectionStatus('connected', '✅ متصل بـ Pusher');
                });

                pusher.connection.bind('error', function(err) {
                    log('❌ خطأ في الاتصال بـ Pusher: ' + JSON.stringify(err));
                    updateConnectionStatus('error', '❌ خطأ في الاتصال');
                });

                pusher.connection.bind('connecting', function() {
                    log('🔄 جاري الاتصال بـ Pusher...');
                    updateConnectionStatus('connecting', '🔄 جاري الاتصال...');
                });

                @auth
                // Subscribe to financial notifications channel
                const channelName = 'financial-notifications-{{ auth()->id() }}';
                channel = pusher.subscribe(channelName);
                
                log(`📡 الاشتراك في القناة: ${channelName}`);

                channel.bind('pusher:subscription_succeeded', function() {
                    log('✅ تم الاشتراك في قناة الإشعارات المالية بنجاح');
                });

                channel.bind('pusher:subscription_error', function(error) {
                    log('❌ فشل الاشتراك في القناة: ' + JSON.stringify(error));
                });

                // Listen for financial notification events
                channel.bind('financial-notification-received', function(data) {
                    eventCount++;
                    document.getElementById('event-count').textContent = eventCount;
                    
                    log('🟢 تم استلام إشعار مالي: ' + JSON.stringify(data, null, 2));
                    
                    // Update last notification display
                    document.getElementById('last-notification').innerHTML = `
                        <strong>📢 ${data.title || 'بدون عنوان'}</strong><br>
                        <em>${data.message || 'بدون رسالة'}</em><br>
                        <small>🕒 ${new Date().toLocaleString('ar-SA')}</small>
                    `;
                    
                    // Play notification sound
                    playNotificationSound();
                    
                    // Show browser notification if supported
                    if ('Notification' in window && Notification.permission === 'granted') {
                        new Notification(data.title || 'إشعار مالي', {
                            body: data.message || '',
                            icon: '/favicon.ico',
                            dir: 'rtl',
                            lang: 'ar'
                        });
                    }
                });
                @else
                log('❌ المستخدم غير مسجل الدخول - لا يمكن الاشتراك في الإشعارات');
                updateConnectionStatus('error', '❌ غير مسجل الدخول');
                @endauth

                log('✅ تم إكمال تهيئة Pusher');
                
            } catch (error) {
                log('❌ خطأ في تهيئة Pusher: ' + error.message);
                updateConnectionStatus('error', '❌ خطأ في التهيئة');
            }
        });

        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                log('🔔 إذن الإشعارات: ' + permission);
            });
        }
    </script>
</body>
</html> 