<div class="pdf-actions">
    @if(isset($reservation) && $reservation)
        @if($showDownloadAll ?? false)
            {{-- <a href="{{ route('insurance.preview-all', $reservation) }}" 
               target="_blank"
               class="inline-flex items-center px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-150 mr-2">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                Preview All (Print)
            </a> --}}
        @endif

        @if($showIndividualDownload ?? false)
            {{-- <button onclick="openIndividualTabs{{ $reservation->id }}()" 
               class="inline-flex items-center px-3 py-2 text-xs font-medium text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-150 mr-2">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                Individual Print (Separate Tabs)
            </button> --}}
        @endif

        @if($showPreview ?? false)
            {{-- <a href="{{ route('insurance.preview', [$reservation, $person ?? null]) }}" 
               target="_blank"
               class="inline-flex items-center px-3 py-2 text-xs font-medium text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-150 mr-2">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 6 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                Preview & Print
            </a> --}}
        @endif

        @if($showDownload ?? false && isset($person) && $person)
            <a href="{{ route('insurance.download-single', [$reservation, $person]) }}" 
               target="_blank"
               class="inline-flex items-center px-3 py-2 text-xs font-medium text-orange-600 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors duration-150">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 6 16 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                Preview & Print
            </a>
        @endif
    @else
        <div class="text-gray-500 text-xs">
            Insurance actions not available - reservation data missing
        </div>
    @endif
</div>

@if($showIndividualDownload ?? false)
<script>
console.log('Setting up Individual Print button for reservation {{ $reservation->id ?? 'unknown' }}');

function openIndividualTabs{{ $reservation->id }}() {
    const reservationId = {{ $reservation->id }};
    console.log('Individual Print button clicked for reservation:', reservationId);
    
    // Show loading message immediately
    alert('Loading people data...');
    
    // Make AJAX request to get people
    fetch(`/admin/reservations/${reservationId}/people`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('People data received:', data);
        const people = data.people || [];
        
        if (people.length === 0) {
            alert('No people found for this reservation.');
            return;
        }
        
        console.log(`Opening ${people.length} tabs...`);
        
        // Open each person's insurance document in a separate tab
        people.forEach((person, index) => {
            setTimeout(() => {
                const url = `/insurance/preview/${reservationId}/${person.id}`;
                console.log(`Opening tab ${index + 1}: ${url}`);
                window.open(url, '_blank');
            }, index * 300);
        });
        
        // Show success message
        alert(`Successfully opened ${people.length} insurance documents in separate tabs!`);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading people data: ' + error.message);
    });
}
</script>
@endif 