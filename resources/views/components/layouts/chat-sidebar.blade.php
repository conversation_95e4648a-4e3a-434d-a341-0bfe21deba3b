{{--<div x-data="{ isOpen: false }"--}}
{{--    class="filament-chat-sidebar fixed mx-8 right-0 bottom-0 w-[430px] bg-white shadow-lg flex flex-col rounded-t-xl"--}}
{{--    style="z-index: 9999;"--}}
{{--    :class="{ 'h-[45rem]': isOpen, 'h-16': !isOpen }">--}}

{{--    <!-- Cha<PERSON>er with Toggle -->--}}
{{--    <div class="p-4 border-b bg-primary-600 text-white flex justify-between items-center cursor-pointer rounded-t-xl w-[430px]" @click="isOpen = !isOpen" style="width: 430px;">--}}
{{--        <h3 class="text-xl font-semibold flex items-center gap-3">--}}
{{--            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">--}}
{{--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>--}}
{{--            </svg>--}}
{{--            Messages--}}
{{--        </h3>--}}
{{--        <button class="text-white hover:text-gray-200 focus:outline-none">--}}
{{--            <svg x-show="!isOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">--}}
{{--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>--}}
{{--            </svg>--}}
{{--            <svg x-show="isOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">--}}
{{--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>--}}
{{--            </svg>--}}
{{--        </button>--}}
{{--    </div>--}}

{{--    <!-- Active Users List -->--}}
{{--    <div x-show="isOpen"--}}
{{--        style="width: 430px;"--}}
{{--        x-transition:enter="transition ease-out duration-300"--}}
{{--        x-transition:enter-start="opacity-0 transform translate-y-4"--}}
{{--        x-transition:enter-end="opacity-100 transform translate-y-0"--}}
{{--        x-transition:leave="transition ease-in duration-300"--}}
{{--        x-transition:leave-start="opacity-100 transform translate-y-0"--}}
{{--        x-transition:leave-end="opacity-0 transform translate-y-4"--}}
{{--        class="flex-1 overflow-y-auto bg-white p-4">--}}
{{--        <livewire:active-users-list wire:poll.30s />--}}
{{--    </div>--}}

{{--    <!-- Chat Windows Container -->--}}
{{--    <div x-show="isOpen" class="fixed bottom-0 right-[430px] flex flex-row-reverse gap-4">--}}
{{--        <livewire:chat-windows />--}}
{{--    </div>--}}
{{--</div>--}}

{{--@push('styles')--}}
{{--<style>--}}
{{--    /* Reset ALL Filament content layouts */--}}
{{--    .filament-main,--}}
{{--    .filament-main.flex-col.gap-y-6,--}}
{{--    .filament-main-content,--}}
{{--    .filament-page {--}}
{{--        width: 100% !important;--}}
{{--        max-width: 100% !important;--}}
{{--        padding-right: var(--padding-right, 1rem) !important;--}}
{{--        padding-left: var(--padding-left, 1rem) !important;--}}
{{--        margin-right: 0 !important;--}}
{{--        margin-left: 0 !important;--}}
{{--    }--}}

{{--    /* Position chat sidebar */--}}
{{--    .filament-chat-sidebar {--}}
{{--        transition: all 0.3s ease-in-out;--}}
{{--        box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);--}}
{{--    }--}}

{{--    /* RTL support */--}}
{{--    [dir="rtl"] .filament-chat-sidebar {--}}
{{--        right: auto;--}}
{{--        left: 8rem;--}}
{{--    }--}}

{{--    [dir="rtl"] .filament-chat-sidebar .fixed.bottom-0.right-[430px] {--}}
{{--        right: auto;--}}
{{--        left: 430px;--}}
{{--    }--}}

{{--    /* Ensure chat windows stay above content */--}}
{{--    .filament-chat-sidebar .fixed.bottom-0 {--}}
{{--        z-index: 9999;--}}
{{--    }--}}

{{--    /* Hover effect for header */--}}
{{--    .filament-chat-sidebar .bg-primary-600:hover {--}}
{{--        opacity: 0.95;--}}
{{--    }--}}

{{--    /* Responsive adjustments */--}}
{{--    @media (max-width: 768px) {--}}
{{--        .filament-chat-sidebar {--}}
{{--            width: calc(100vw - 2rem) !important;--}}
{{--            right: 1rem !important;--}}
{{--        }--}}

{{--        [dir="rtl"] .filament-chat-sidebar {--}}
{{--            right: auto !important;--}}
{{--            left: 1rem !important;--}}
{{--        }--}}

{{--        .filament-chat-sidebar .fixed.bottom-0.right-[430px],--}}
{{--        [dir="rtl"] .filament-chat-sidebar .fixed.bottom-0.right-[430px] {--}}
{{--            right: 1rem !important;--}}
{{--            left: 1rem !important;--}}
{{--            width: calc(100vw - 2rem) !important;--}}
{{--        }--}}
{{--    }--}}
{{--</style>--}}
{{--@endpush--}}

{{--@push('scripts')--}}
{{--<script>--}}
{{--    // Persist chat sidebar state--}}
{{--    document.addEventListener('DOMContentLoaded', function() {--}}
{{--        const sidebar = document.querySelector('.filament-chat-sidebar');--}}
{{--        if (sidebar && window.Alpine) {--}}
{{--            const stored = localStorage.getItem('chat-sidebar-open');--}}
{{--            if (stored !== null) {--}}
{{--                const component = Alpine.$data(sidebar);--}}
{{--                component.isOpen = stored === 'true';--}}
{{--            }--}}

{{--            // Watch for changes and store them--}}
{{--            sidebar.addEventListener('x-data-update', (event) => {--}}
{{--                if (event.detail.isOpen !== undefined) {--}}
{{--                    localStorage.setItem('chat-sidebar-open', event.detail.isOpen);--}}
{{--                }--}}
{{--            });--}}
{{--        }--}}
{{--    });--}}
{{--</script>--}}
{{--@endpush--}}
