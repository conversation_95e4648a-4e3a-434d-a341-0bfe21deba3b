<!-- resources/views/components/chat.blade.php -->
<div class="flex h-screen">
    <!-- Message Input (Left Sidebar) -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col p-4">
        <h2 class="text-lg font-semibold mb-4">New Message</h2>
        <form class="flex flex-col space-y-2">
            <textarea
                class="w-full p-2 border rounded-md resize-none focus:ring-2 focus:ring-blue-500"
                rows="4"
                placeholder="Type your message..."
            ></textarea>
            <button
                type="submit"
                class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
                abanoub
            </button>
        </form>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
        <!-- Active Users (Top) -->
        <div class="bg-gray-100 p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold mb-2">Active Users</h2>
            <div class="flex space-x-4 overflow-x-auto">
                @foreach ($activeUsers as $user)
                    <div class="flex flex-col items-center">
                        <img
                            src="{{ $user->personal_image ?? 'https://via.placeholder.com/50' }}"
                            alt="{{ $user->name }}"
                            class="w-12 h-12 rounded-full border-2 border-green-500"
                        />
                        <span class="text-sm mt-1">{{ $user->name }}</span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="flex-1 p-4 overflow-y-auto">
            @foreach ($messages as $message)
                <div class="mb-4 {{ $message->isSentByCurrentUser ? 'text-right' : 'text-left' }}">
                    <div
                        class="{{ $message->isSentByCurrentUser ? 'bg-blue-100' : 'bg-gray-100' }} inline-block p-3 rounded-lg max-w-md"
                    >
                        <p class="text-sm">{{ $message->content }}</p>
                        <span class="text-xs text-gray-500">{{ $message->created_at->diffForHumans() }}</span>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
