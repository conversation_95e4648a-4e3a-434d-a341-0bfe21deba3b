<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Georgia Kid Insurance for {{ $kid->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f7f7f7;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #da291c;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #da291c;
        }
        .georgia-flag {
            display: block;
            margin: 0 auto;
            width: 100px;
            height: 60px;
            background-color: white;
            background-image: 
                linear-gradient(to right, #da291c, #da291c 20%, transparent 20%, transparent 80%, #da291c 80%, #da291c),
                linear-gradient(to bottom, #da291c, #da291c 20%, transparent 20%, transparent 80%, #da291c 80%, #da291c);
            background-repeat: no-repeat;
            margin-bottom: 15px;
            position: relative;
        }
        .georgia-flag::before, .georgia-flag::after {
            content: '';
            position: absolute;
            background-color: #da291c;
            width: 15px;
            height: 15px;
            border-radius: 50%;
        }
        .georgia-flag::before {
            top: 10px;
            left: 10px;
        }
        .georgia-flag::after {
            bottom: 10px;
            right: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th, .info-table td {
            border: 1px solid #da291c;
            padding: 8px;
        }
        .info-table th {
            background-color: #ffeaea;
            text-align: left;
            color: #da291c;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            margin-top: 50px;
            border-top: 2px solid #da291c;
            padding-top: 10px;
        }
        .stamp {
            width: 150px;
            height: 150px;
            border: 2px dashed #da291c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            transform: rotate(-15deg);
            color: #da291c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="georgia-flag"></div>
        <h1>Georgia Child Insurance Certificate</h1>
        <p>Trip Code: {{ $reservation->trip_code }}</p>
    </div>

    <div class="content">
        <h2 style="color: #da291c;">Child Information</h2>
        <table class="info-table">
            <tr>
                <th>Full Name:</th>
                <td>{{ $kid->name }}</td>
            </tr>
            <tr>
                <th>Passport Number:</th>
                <td>{{ $kid->passport_number }}</td>
            </tr>
            <tr>
                <th>Birth Date:</th>
                <td>{{ $kid->birth_date ? $kid->birth_date->format('d/m/Y') : '' }}</td>
            </tr>
            <tr>
                <th>Country:</th>
                <td>{{ $kid->country_name }}</td>
            </tr>
        </table>

        <h2 style="color: #da291c;">Trip Details</h2>
        <table class="info-table">
            <tr>
                <th>Leader Name:</th>
                <td>{{ $reservation->customer_name }}</td>
            </tr>
            <tr>
                <th>Trip Code:</th>
                <td>{{ $reservation->trip_code }}</td>
            </tr>
            <tr>
                <th>Arrival Date:</th>
                <td>{{ $reservation->arrival_date ? $reservation->arrival_date->format('d/m/Y') : '' }}</td>
            </tr>
            <tr>
                <th>Departure Country:</th>
                <td>{{ $reservation->countryFrom->name ?? '' }}</td>
            </tr>
            <tr>
                <th>Arrival Branch:</th>
                <td>{{ $branch->name ?? 'Georgia' }}</td>
            </tr>
        </table>
        
        <div class="stamp">
            GEORGIA<br>
            INSURANCE<br>
            APPROVED
        </div>
    </div>

    <div class="footer">
        <p>This is an official insurance document for children under 6 years of age traveling to Georgia.</p>
        <p>Issued on {{ now()->format('d/m/Y') }}</p>
    </div>
</body>
</html> 