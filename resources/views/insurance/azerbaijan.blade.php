<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Insurance Policy</title>
    <style>
        body {
            font-family: 'Deja<PERSON>u Sans', Aria<PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.5;
            color: #333;
            font-size: 14px;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .print-button:hover {
            background: #0056b3;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            border: 1px solid #ccc;
            padding: 20px;
        }
        .header {
            width: 100%;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0;
            text-align: center;
            font-weight: bold;
        }
        .section {
            margin: 12px 0;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .row {
            width: 100%;
            margin-bottom: 8px;
            clear: both;
        }
        .row div {
            display: inline-block;
            width: 48%;
            vertical-align: top;
            margin-right: 2%;
        }
        .insured-info {
            border-top: 2px solid #3753de;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        .insured-info .info-row {
            display: inline-block;
            width: 19%;
            text-align: center;
            vertical-align: top;
            margin-right: 1%;
        }
        .services, .medical-institutions {
            margin: 15px 0;
        }
        .services ul, .medical-institutions ul {
            padding-left: 20px;
        }
        .services-header {
            width: 100%;
            border-bottom: 1px solid #333;
            padding: 5px 0;
            margin-bottom: 10px;
        }
        .services-header span {
            display: inline-block;
            font-weight: bold;
        }
        .service-item {
            border-top: 1px solid black;
            padding: 5px;
            margin-bottom: 5px;
        }
        .terms {
            font-size: 12px;
            text-align: justify;
            line-height: 1.4;
            margin-top: 15px;
        }
        .footer {
            font-size: 12px;
            text-align: center;
            margin-top: 20px;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .highlight {
            color: #ff0000;
            font-weight: bold;
        }
        .contact-section {
            font-size: 12px;
            border-top: 2px solid #3753de;
            margin-top: 10px;
            padding-top: 10px;
        }
        .contact-content {
            width: 100%;
            margin-bottom: 10px;
        }
        .contact-text {
            display: inline-block;
            width: 70%;
            vertical-align: top;
        }
        .contact-numbers {
            display: inline-block;
            width: 25%;
            text-align: right;
            vertical-align: top;
        }

        /* Print Styles */
        @media print {
            body {
                background-color: white !important;
                padding: 0 !important;
            }

            .print-button {
                display: none !important;
            }

            .container {
                max-width: none !important;
                border: 1px solid #ccc !important;
                padding: 20px !important;
                margin: 0 !important;
                box-shadow: none !important;
            }

            .insured-info {
                border-top: 2px solid #3753de !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .contact-section {
                border-top: 2px solid #3753de !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .highlight {
                color: #ff0000 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            @page {
                margin: 1cm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">🖨️ Print Document</button>
    
    <div class="container">
        <div class="header">
            <h1>INTERNATIONAL TRAVEL INSURANCE POLICY<br>Daxili Səfər siğortası şəhadətnaməsi</h1>
        </div>

        <div class="section">
            <div class="row" style="display: flex; justify-content: space-between;">
                <div style="flex: 1; margin-right: 20px;">
                    <span class="section-title">DATE OF ISSUE / Buraxılma tarixi</span><br>
                    {{ $reservation->created_at ? $reservation->created_at->format('d.m.Y') : '' }}
                </div>
                <div style="flex: 1;">
                    <span class="section-title">POLICY NUMBER / Şəhadətnamənin nömrəsi</span><br>
                    {{ $reservation->id . rand(1000, 9999) }}
                </div>
            </div>
            <div class="row" style="display: flex; justify-content: space-between;">
                <div style="flex: 1; margin-right: 20px;">
                    <span class="section-title">INSURANCE PERIOD / Sığortanın müddəti</span><br>
                    {{ $reservation->arrival_date ? $reservation->arrival_date->format('d.m.Y') : '' }} - 
                    {{ $reservation->departure_date ? $reservation->departure_date->format('d.m.Y') : '' }} 23:59
                </div>
                <div style="flex: 1;">
                    <span class="section-title">INSURED DAYS / Sığortalanmış günlərin sayı</span><br>
                    {{ $reservation->number_of_days ?? '' }}
                </div>
            </div>
            <div class="row" style="display: flex; justify-content: space-between;">
                <div style="flex: 1; margin-right: 20px;">
                    <span class="section-title">COVERAGE AREA / Sığortanın ərazisi</span><br>
                    Azerbaijan
                </div>
                <div style="flex: 1;">
                    <span class="section-title">PURPOSE OF TRAVEL / Səfərin məqsədi</span><br>
                    TOURISM
                </div>
            </div>
        </div>
        <div class="insured-info" style="display: flex; justify-content: space-between;">
            <div class="info-row" style="flex: 1; margin-right: 10px;">
                <span class="section-title">NAME AND SURNAME OF INSURED PERSON / Sığortalanmış şəxsin adı, soyadı</span><br>
                {{ $person->name ?? $reservation->customer_name ?? '' }}
            </div>
            <div class="info-row" style="flex: 1; margin-right: 10px;">
                <span class="section-title">PASSPORT NUMBER / Pasport nömrəsi</span><br>
                
                {{ $person->passport_number ?? $person->passport_number ?? '' }}
            </div>
            <div class="info-row" style="flex: 1; margin-right: 10px;">
                <span class="section-title">DATE OF BIRTH / Doğum tarixi</span><br>
                {{ $person->birth_date ? $person->birth_date->format('d.m.Y') : '' }}
            </div>
            <div class="info-row" style="flex: 1; margin-right: 10px;">
                <span class="section-title">SUM INSURED / Sığorta məbləği</span><br>
                0 AZN
            </div>
            <div class="info-row" style="flex: 1;">
                <span class="section-title">PREMIUM / Sığorta haqqı</span><br>
                0 AZN
            </div>
        </div>


        <div class="services" style="font-size: 12px;border-top: 2px solid #3753de;margin-top: 10px;padding-top: 10px;">
            <div class="services-header">
                <span style="width: 10%;">No</span>
                <span style="width: 60%; text-align: center;">SERVICES / XİDMƏTLƏR</span>
                <span style="width: 30%;">LIMIT PER SUM INSURED / Sığorta məbləği üzrə LIMIT</span>
            </div>
            <div style="list-style-type: none; padding: 0;">
                <div class="service-item"><span style="margin-right: 10px;">1.</span> LOWER AND UPPER LIMB INJURIES / AŞAĞI VE YUXARI ƏTRAFLARIN TRAVMALARI, ZƏDƏLƏNMƏLƏR</div>
                <div class="service-item"><span style="margin-right: 10px;">1.1.</span> SURGICAL TREATMENT OF WOUNDS / OPEN-CLOSED FRACTURES / JOINTS DISLOCATION / TENDONS RUPTURE / MENISCUS DAMAGE / YARALANM ÇƏRÇİVƏSİNDƏ AÇIQ-QAPALI SINIQLAR / OYNAQLARIN ÇIXIĞI / VƏTƏRLƏRİN QIRILMASI / MENİSKİN ZƏDƏLƏNMƏSİ / ZƏLİLƏR</div>
                <div class="service-item"><span style="margin-right: 10px;">2.</span> HEAD INJURIES / BAŞ TRAVMALARI, ZƏDƏLƏR</div>
                <div class="service-item"><span style="margin-right: 10px;">2.1.</span> CRANIOCEREBRAL TRAUMA / BRAIN CONCUSSION, CONTUSION / MAXILLOFACIAL AREA TRAUMA / KƏLLƏ-BEYİN TRAVMALARI / BEYİN SİLKƏLƏNMƏSİ, ZƏLİMƏSİ / ÜZ-ÇƏNƏ NAHİYƏSİNDƏ ZƏDƏLƏR VƏ S.</div>
                <div class="service-item"><span style="margin-right: 10px;">3.</span> OTHER INJURIES / DİĞƏR ZƏDƏLƏR</div>
                <div class="service-item"><span style="margin-right: 10px;">3.1.</span> DAMAGE OF INTERNAL ORGANS / INTERNAL BLEEDING / RIB FRACTURES / FOREIGN BODY REMOVAL / İ SUTURING OF THE WOUNDS AND ETC. / DAXİLİ ORQANLARIN ZƏDƏLƏNMƏSİ / DAXİLİ QANAXMALAR / DAXİLİ QANAXMALAR VS / QABIRĞA SINIGI / YUNSAĞ YARALARIN TİKİLMƏSİ VƏ S.</div>
            </div>
        </div>

        <div class="medical-institutions" style="border-top: 2px solid #3753de;margin-top: 10px;padding-top: 10px;">
            <p class="section-title" style="text-align: center;">MEDICAL INSTITUTIONS AND PHARMACIES / Tibb müəssisələri və apteklər</p>
            <div style="list-style-type: none; padding: 0;">
                <div class="service-item">Mərkəzi Klinik Xəstəxanası (Bakı) / Mərkəzi Klinik Xəstəxanası (Gəncə)</div>
                <div class="service-item">Mərkəzi Neftçilər Xəstəxanası (Bakı) / Sərgi Tibb Mərkəzi (Naxçıvan)</div>
                <div class="service-item">MEDI STYLE Klinikası (Bakı) / OKI Tibb Mərkəzi (Qusar)</div>
                <div class="service-item">CITY HOSPITAL Tibb Mərkəzi (Bakı) / Evromed Klinikası (Mingəçevir)</div>
                <div class="service-item">Liman Tibb Mərkəzi (Sumqayıt) / Ömürdan pay Tibb Mərkəzi (Bərdə)</div>
                <div class="service-item">REAL Tibb Mərkəzi (Sumqayıt) / Medican Klinikası (Lənkəran)</div>
                <div class="service-item">UNIKAL Klinikası (Sumqayıt) / Referans Klinikası (Qəbələ)</div>
            </div>
        </div>

        <div class="terms">
            <p style="margin-bottom: 10px;">I agree that I do object to collecting and processing my card credit in system for conducting insurance operations. The agreement is valid for the duration of the insurance, and information will be achieved in accordance with the legislation after the expiration of contract or subject. The agreement can be terminated by written appeal-Razılıq verir ki,  sistemində sığortalanmış şəxsin ad, soyadı, doğum tarixi, ünvanı, əlaqə nömrəsi, sığorta məbləği, sığorta haqqı, sığorta müddəti, sığorta polisi nömrəsi, sığorta şirkəti və s. kimi məlumatların saxlanılması, emal edilməsi, ötürülməsi, açıqlanması və digər əməliyyatlar üçün razıyam. Bu razılıq sığorta müqaviləsi qüvvədə olduğu müddət ərzində qüvvədədir. Sığorta (sığorta olunan) razılıq verir ki, bu məlumatlar sığorta müqaviləsi qüvvədə olduğu müddət ərzində və ya müqavilənin ləğvindən sonra müəyyən edilmiş müddətdə saxlanıla bilər.</p>
            <p>An insured (insured person) agrees and accepts that in order to provide him/her with a range of services, by concluding an agreement with the insurance company (insurer) for various insurance sources, as well as to pass any information of a Disclosing Party presenting insurance secret to its employees, representatives, persons providing legal and other services to it, related parties, state bodies that require this information in accordance with the law, as well as to conduct insurance activities, bodies exercising control over financial markets may transfer this kind of information to domestic and internationally, and no further written consent of the Disclosing Party shall be required in this case. The Insurer is responsible to the Insured (insured person) for keeping the information confidential by the persons whom it transfers information. This consent remains valid within 3 years after the expiry of insurance contract / Sığorta (sığorta olunan) razılıq verir ki, ona daha geniş xidmətlər göstərmək üçün Sığortaçı (sığorta olunan) razılıq verir ki, bu məlumatlar sığorta müqaviləsi qüvvədə olduğu müddət ərzində və ya müqavilənin ləğvindən sonra müəyyən edilmiş müddətdə saxlanıla bilər.</p>
        </div>

        <div class="footer">
            Cdt: {{ $reservation->created_at ? $reservation->created_at->format('d.m.Y H:i:s') : '' }} Travel Companies: TK: {{ $reservation->trip_code ?? '' }}
        </div>
    </div>
</body>
</html>