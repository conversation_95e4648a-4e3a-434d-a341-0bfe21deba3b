<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Insurance Policy - Georgia - All People</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .print-button:hover {
            background: #0056b3;
        }

        .policy-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: table;
            width: 100%;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .header-left,
        .header-right {
            display: table-cell;
            vertical-align: top;
        }

        .header-left {
            width: 60%;
            padding-right: 20px;
        }

        .header-right {
            width: 40%;
            text-align: left;
        }

        .policy-number-box {
            background: #f5f5f5;
            border: 1px solid #ccc;
            padding: 8px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
            min-width: 200px;
            border-radius: 4px;
        }

        .date-issue-section {
            margin-bottom: 10px;
        }

        .date-boxes-header {
            display: inline-flex;
            gap: 5px;
            align-items: center;
        }

        .date-box {
            background: #f5f5f5;
            border: 1px solid #ccc;
            width: 40px;
            height: 30px;
            text-align: center;
            line-height: 28px;
            font-weight: bold;
            font-size: 14px;
            border-radius: 4px;
        }

        .date-label {
            font-size: 10px;
            text-align: center;
            margin-top: 2px;
        }

        .form-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .form-row {
            display: table-row;
        }

        .form-label {
            display: table-cell;
            padding: 8px;
            font-weight: bold;
            vertical-align: middle;
            width: 300px;
            border-bottom: 1px solid #ddd;
        }

        .form-field {
            display: table-cell;
            padding: 8px;
            background: #f5f5f5;
            border: 1px solid #ccc;
            margin: 2px;
            font-weight: bold;
            vertical-align: middle;
            border-bottom: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 12px;
        }

        .dob-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .dob-boxes {
            display: flex;
            gap: 5px;
            align-items: flex-start;
        }

        .dob-box-group {
            text-align: center;
        }

        .dob-box {
            background: #f5f5f5;
            border: 1px solid #ccc;
            width: 35px;
            height: 25px;
            text-align: center;
            line-height: 23px;
            font-weight: bold;
            font-size: 12px;
            border-radius: 4px;
        }

        .dob-label {
            font-size: 9px;
            margin-top: 2px;
        }

        .passport-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .passport-box {
            background: #f5f5f5;
            border: 1px solid #ccc;
            padding: 4px 8px;
            font-weight: bold;
            min-width: 100px;
            border-radius: 4px;
        }

        .insurance-period-table {
            width: 100%;
            margin: 15px 0;
        }

        .period-dates {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .period-date-group {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .period-boxes {
            display: flex;
            gap: 3px;
            margin-bottom: 3px;
        }

        .period-box {
            background: #f5f5f5;
            border: 1px solid #ccc;
            width: 30px;
            height: 25px;
            text-align: center;
            line-height: 23px;
            font-weight: bold;
            font-size: 11px;
            border-radius: 4px;
        }

        .period-labels {
            display: flex;
            gap: 3px;
        }

        .period-label {
            font-size: 8px;
            text-align: center;
            width: 30px;
        }

        .period-separator {
            font-weight: bold;
            margin: 0 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .both-dates-note {
            font-size: 10px;
            margin-left: 10px;
            align-self: flex-end;
        }

        .signature-section {
            display: table;
            width: 100%;
            margin-top: 40px;
            border-top: 2px solid #000;
            padding-top: 20px;
        }

        .signature-left,
        .signature-right {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 40px;
            font-size: 11px;
        }

        .signature-line {
            border-bottom: 1px dotted #000;
            height: 1px;
            margin-bottom: 5px;
        }

        .contact-info {
            margin-top: 30px;
            border-top: 1px solid #000;
            padding-top: 15px;
            font-size: 10px;
            line-height: 1.4;
        }

        .attention {
            font-weight: bold;
            margin-bottom: 8px;
        }

        /* Print Styles */
        @media print {
            body {
                background-color: white !important;
                padding: 0 !important;
                font-size: 11px !important;
            }

            .print-button {
                display: none !important;
            }

            .policy-container {
                max-width: none !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                padding: 15px !important;
                margin: 0 !important;
            }

            /* Ensure all input backgrounds are preserved in print */
            .policy-number-box,
            .date-box,
            .form-field,
            .dob-box,
            .passport-box,
            .period-box {
                background: #f5f5f5 !important;
                border: 1px solid #ccc !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                margin: 1cm;
                size: A4;
            }
        }
    </style>
</head>

<body>
    <button class="print-button" onclick="window.print()">🖨️ Print All Documents</button>

    @foreach ($people as $key => $person)
        @if ($key != 0)
            <div style="margin-top: 100px;"></div>
        @endif
        <div class="policy-container">
            <!-- Header Section -->
            <h4 style="text-align: center; font-weight: bold;margin-bottom: 20px;font-size: 20px;">INTERNATIONAL TRAVEL
                INSURANCE POLICY </h4>
            <div class="header">

                <div class="header-left">
                    <div style="margin-bottom: 10px; font-weight: bold;">პოლისის ნომერი/Policy number:</div>
                    <input class="policy-number-box" type="text" value="{{ $reservation->id . rand(1000, 9999) }}">
                </div>
                <div class="header-right">
                    <div style="margin-bottom: 10px; font-weight: bold;">გაცემის თარიღი/Date of Issue:</div>
                    <div class="date-issue-section">
                        <div style="display: inline-block; text-align: center; margin-right: 5px;">
                            <input class="date-box" type="text"
                                value="{{ $reservation->created_at ? $reservation->created_at->format('d') : '07' }}">
                            <div class="date-label">რიცხვი/D</div>
                        </div>
                        <div style="display: inline-block; text-align: center; margin-right: 5px;">
                            <input class="date-box" type="text"
                                value="{{ $reservation->created_at ? $reservation->created_at->format('n') : '6' }}">
                            <div class="date-label">თვე/M</div>
                        </div>
                        <div style="display: inline-block; text-align: center;">
                            <input class="date-box" type="text"
                                value="{{ $reservation->created_at ? $reservation->created_at->format('Y') : '2025' }}">
                            <div class="date-label">წელი/Year</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Policy Details -->
            <div class="form-table">
                <div class="form-row">
                    <div class="form-label" style="width: 40%;">მზღვეველი/Insurer:</div>
                    <textarea class="form-field" style="width: 100%;" type="text" value="">Travel international insurance</textarea>
                </div>

                <div class="form-row">
                    <div class="form-label" style="width: 40%;">პოლისის ტიპი/Policy Type:</div>
                    <input class="form-field" style="width: 100%;" type="text" value="Local Travel Health">
                </div>

                <div class="form-row">
                    <div class="form-label" style="width: 40%;">დაზღვეული/Policy Holder:</div>
                    <input class="form-field" style="width: 100%;" type="text"
                        value="{{ $person->name ?? ($reservation->customer_name ?? 'MOHANAD ALSHUAILABDULAZIZ') }}">
                </div>

                <div class="form-row">
                    <div class="form-label" style="width: 40%;">დაბ.თარიღი/D.O.B.</div>
                    <div class="form-field">
                        <div class="dob-container">
                            <div class="dob-boxes">
                                <div style="text-align: center; margin-right: 10px;">
                                    <input class="dob-box" type="text"
                                        value="{{ $person->birth_date ? $person->birth_date->format('d') : '14' }}">
                                    <div class="dob-label">რიცხვი/D</div>
                                </div>
                                <div style="text-align: center; margin-right: 10px;">
                                    <input class="dob-box" type="text"
                                        value="{{ $person->birth_date ? $person->birth_date->format('m') : '11' }}">
                                    <div class="dob-label">თვე/M</div>
                                </div>
                                <div style="text-align: center; margin-right: 20px;">
                                    <input class="dob-box" type="text"
                                        value="{{ $person->birth_date ? $person->birth_date->format('Y') : '2016' }}">
                                    <div class="dob-label">წელი/Year</div>
                                </div>
                            </div>

                            <div class="passport-section">
                                <span style="margin-right: 10px; font-weight: bold;">პირადი #/ID#</span>
                                <input class="passport-box" style="width: 120px;" type="text"
                                    value="{{ $person->passport_number ?? ($reservation->passport_number ?? 'AP81437') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-label">საცხოვრებელი ქვეყანა/Country of residence:</div>
                    <input class="form-field" style="width: 100%;" type="text"
                        value="{{ $person->country_name ?? 'Saudi Arabia' }}">
                </div>

                <div class="form-row">
                    <div class="form-label">პასპორტი/Passport:</div>
                    <input class="form-field" style="width: 100%;" type="text"
                        value="{{ $person->passport_number ?? ($reservation->passport_number ?? 'AP81437') }}">
                </div>

                <div class="form-row">
                    <div class="form-label">სამოგზაურო ქვეყნები/Countries to be visited:</div>
                    <input class="form-field" style="width: 100%;" type="text" value="{{ 'Georgia' }}">
                </div>

                <div class="form-row">
                    <div class="form-label" style="width: 20%;">საზღვაო პერიოდი/Insurance Period:</div>
                    <div class="form-field">
                        <div class="period-dates">
                            <!-- From Date -->
                            <div class="period-date-group">
                                <div class="period-boxes">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->arrival_date ? $reservation->arrival_date->format('d') : '3' }}">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->arrival_date ? $reservation->arrival_date->format('n') : '6' }}">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->arrival_date ? $reservation->arrival_date->format('Y') : '2025' }}">
                                </div>
                                <div class="period-labels">
                                    <div class="period-label">რიცხვი/D</div>
                                    <div class="period-label">თვე/M</div>
                                    <div class="period-label">წელი/Year</div>
                                </div>
                            </div>

                            <!-- From Label -->
                            <div class="period-separator">
                                -დან<br><small>To</small>
                            </div>

                            <!-- To Date -->
                            <div class="period-date-group">
                                <div class="period-boxes">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->departure_date ? $reservation->departure_date->format('d') : '10' }}">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->departure_date ? $reservation->departure_date->format('n') : '6' }}">
                                    <input class="period-box" type="text"
                                        value="{{ $reservation->departure_date ? $reservation->departure_date->format('Y') : '2025' }}">
                                </div>
                                <div class="period-labels">
                                    <div class="period-label">რიცხვი/D</div>
                                    <div class="period-label">თვე/M</div>
                                    <div class="period-label">წელი/Year</div>
                                </div>
                            </div>


                            <!-- Both dates included note -->
                            <div class="both-dates-note">
                                (ორივე დღის ჩათვლით)
                                <br><small>(both dates included)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-label">დღეების რ-ბა/Number of days:</div>
                    <input class="form-field" style="width: 100%;" type="text"
                        value="{{ $reservation->number_of_days ?? '8' }}">
                </div>

                <div class="form-row">
                    <div class="form-label">საზღვაო პრემია/Insurance Premium:</div>
                    <input class="form-field" style="width: 100%;" type="text" value="0 GEL">
                </div>

                <div class="form-row">
                    <div class="form-label">საზღვაო თანხა/Sum Insured:</div>
                    <input class="form-field" style="width: 100%;" type="text" value="0 GEL">
                </div>

                <div class="form-row">
                    <div class="form-label">საწყისი რ-ბა მზღვ/Deductible for Medical Services:</div>
                    <input class="form-field" style="width: 100%;" type="text" value="0 GEL">
                </div>
            </div>

            <!-- Additional Information -->
            <div class="form-section">

            </div>

            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-left">
                    <div class="signature-title">სოროეული კომპანიის/მონაწილე ხელმომწერა<br>Travel company/Signature of
                        Insurer</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature-right">
                    <div class="signature-title">დაზღვეული ხელმომწერა/<br>Signature of Insured</div>
                    <div class="signature-line"></div>
                </div>
            </div>

            <!-- Contact Information -->

        </div>
    @endforeach
</body>

</html>
