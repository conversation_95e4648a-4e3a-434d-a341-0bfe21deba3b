<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Pdf</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 2cm;
            }
            body {
                background-color: white !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 12pt;
            }
            .container {
                box-shadow: none !important;
                border: none !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
        }
    </style>
</head>
<body style="font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; line-height: 1.4;" id="download-pdf">
    <div class="container" style="background-color: white; padding: 20px; border: 1px solid #ccc; max-width: 800px; margin: 0 auto; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">

        <!-- Header Information -->
        <div style="border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 5px;">{{ __('Tour Code') }}: {{ $reservation->trip_code }}</div>
            <div style="margin-bottom: 5px;"><span style="font-weight: bold">{{ __('For hotel issues please contact this number') }}</span>: {{ $reservation?->bookingHotelUser?->phone }} - {{ $reservation?->bookingHotelUser?->name }}</div>
            <div style="margin-bottom: 5px;"><span style="font-weight: bold">{{ __('For transportation issues please contact this number') }}</span>:
                {{ $reservation?->bookingTransportationUser?->phone }} -
                {{ $reservation?->bookingTransportationUser?->name }}</div>
        </div>

        <!-- Date Information -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div>
                <div style="font-weight: bold;">{{ __('Arrival Airport') }}: {{ $reservation?->arrivalAirport?->name }}</div>
                <div style="font-weight: bold;">{{ __('Arrival Flight Number') }}: {{ $reservation?->arrival_flight_number }}</div>
                <div><span style="font-weight: bold">Date:</span> {{ $reservation?->arrival_date?->format('d-m') }}</div>
                <div><span style="font-weight: bold">Time:</span> {{ $reservation?->arrival_date?->format('H:i') }}</div>
            </div>
            <div style="text-align: right;">
                <div style="font-weight: bold;">{{ __('Departure Airport') }}: {{ $reservation?->departureAirport?->name }}</div>
                <div style="font-weight: bold;">{{ __('Departure Flight Number') }}: {{ $reservation?->departure_flight_number }}</div>
                <div><span style="font-weight: bold">Date:</span> {{ $reservation?->departure_date?->format('d-m') }}</div>
                <div><span style="font-weight: bold">Time:</span> {{ $reservation?->departure_date?->format('H:i') }}</div>
            </div>
        </div>

        <!-- Department Info -->
        <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">
            <div>{{ __('Persons') }} : {{ $reservation?->number_of_people }}</div>
            @if($reservation?->number_of_children)
                <div>{{ __('Children') }} : {{ $reservation?->number_of_children }}</div>
            @endif
            <div>{{ $reservation->tourist_visit_office ? __('Bring to office') : '' }}</div>
        </div>

{{--        @if($reservation->hotelBookings->count())--}}
{{--            <div style="margin-top: 10px; margin-bottom: 10px; border-bottom: 2px solid black; padding-bottom: 10px;">--}}
{{--                @foreach($reservation->hotelBookings as $hotelBooking)--}}
{{--                    <div style="margin-bottom: 2px; display: flex; align-items: center; gap: 5px">--}}
{{--                        <strong>{{ __('Hotel') }}:</strong> ({{ $hotelBooking->hotel?->name }})--}}
{{--                        <strong>{{ __('Check-in') }}:</strong> ({{ $hotelBooking->check_in?->format('d-m') }})--}}
{{--                        <strong>{{ __('Check-out') }}:</strong> ({{ $hotelBooking->check_out?->format('d-m') }})--}}
{{--                    </div>--}}
{{--                @endforeach--}}
{{--            </div>--}}
{{--        @endif--}}

        <!-- Medical Information List -->
        <div style="margin-bottom: 20px;">
            @foreach(collect($reservation->driver_excel)->sortBy(function ($day) {
    return data_get($day, 'date');
}) as $day)
                <div style="margin-bottom: 8px; padding-left: 10px;">
                    <strong>
                        @if(data_get($day, 'date'))
                            {{ Carbon\Carbon::parse($day['date'])->format('d-m') }}
                        @endif
                    </strong>
                    ({{ data_get($day, 'hotel') }}) --
                    {{ data_get($day, 'comment') }}
                </div>
            @endforeach
        </div>

        <!-- Footer Information -->
        <div style="border-top: 1px solid #ccc; padding-top: 10px; margin-top: 20px;">
            @foreach(($reservation->transportationCompanyBooking?->extraServiceBookings ?? []) as $extraService)
                <div style="margin-bottom: 5px;">
                    <strong>{{ $extraService->extraService?->name }}</strong> (X{{ $extraService->count }})
                </div>
            @endforeach
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        var element = document.getElementById('download-pdf');
        html2pdf().set({
            filename: '{{ $reservation->trip_code }}.pdf',
        }).from(element).save();
    </script>

</body>
</html>
