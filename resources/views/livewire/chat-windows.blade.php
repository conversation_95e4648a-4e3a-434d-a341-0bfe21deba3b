<div>
    <div class="fixed bottom-0 right-[430px] flex flex-row-reverse gap-4">
        @foreach($openChats as $userId)
            @if(isset($users[$userId]))
                <div class="w-[430px] bg-white rounded-t-lg shadow-lg flex flex-col" style="height: 500px;">
                    <!-- Chat Header -->
                    <div class="p-3 border-b flex justify-between items-center bg-white rounded-t-lg">
                        <div class="flex items-center gap-3">
                            <div class="relative">
                                @if(isset($users[$userId]->avatar))
                                    <img src="{{ $users[$userId]->avatar }}" 
                                         alt="{{ $users[$userId]->name }}" 
                                         class="w-10 h-10 rounded-full">
                                @else
                                    <div class="w-10 h-10 rounded-full bg-{{ substr($users[$userId]->name, 0, 2) === 'AD' ? 'orange' : 'primary' }}-500 flex items-center justify-center text-white font-semibold">
                                        {{ substr($users[$userId]->name, 0, 2) }}
                                    </div>
                                @endif
                                @if($users[$userId]->is_online)
                                    <div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                                @endif
                            </div>
                            <div>
                                <h3 class="font-semibold">{{ $users[$userId]->name }}</h3>
                                <span class="text-xs text-gray-500">
                                    {{ $users[$userId]->is_online ? 'Active Now' : 'Last seen ' . $users[$userId]->last_seen }}
                                </span>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <button class="p-2 hover:bg-gray-100 rounded-full" wire:click="closeChat({{ $userId }})">
                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="flex-1 overflow-y-auto p-4 space-y-4" style="height: calc(100% - 140px);">
                        @if(isset($messages[$userId]))
                            @foreach($messages[$userId] as $message)
                                <div class="flex {{ $message->from_user_id === 90 ? 'justify-end' : 'justify-start' }}">
                                    <div class="{{ $message->from_user_id === 90 
                                        ? 'bg-primary-500 text-white rounded-l-2xl rounded-tr-2xl' 
                                        : 'bg-gray-100 rounded-r-2xl rounded-tl-2xl' }} 
                                        p-3 max-w-[70%] break-words">
                                        {{ $message->content }}
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <!-- Message Input -->
                    <div class="border-t p-3 bg-white">
                        <form wire:submit.prevent="sendMessage({{ $userId }})" class="flex items-center gap-2">
                            <input type="text" 
                                   wire:model="newMessage" 
                                   class="flex-1 bg-gray-100 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                                   placeholder="Type a message...">
                            <button type="submit" class="p-2 text-primary-500 hover:bg-gray-100 rounded-full">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
</div> 