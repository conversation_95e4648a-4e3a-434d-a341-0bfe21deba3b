<div class="min-h-screen bg-gray-50 py-8" style="direction: rtl;">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                            🔔 Notifications
                        </h1>
                        <p class="text-gray-600 mt-2">
                            Total Notifications: {{ $totalCount }} | Unread: {{ $unreadCount }}
                        </p>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="flex items-center space-x-3 space-x-reverse">
                        @if($unreadCount > 0)
                            <button 
                                wire:click="markAllAsRead" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                وضع علامة على الكل كمقروء
                            </button>
                        @endif
                        
                        <button 
                            wire:click="deleteAllRead" 
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center">
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف المقروء
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search in notifications</label>
                    <div class="relative">
                        <input 
                            type="text" 
                            wire:model.debounce.300ms="search"
                            placeholder="Search in title or content..."
                            class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Read Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة القراءة</label>
                    <select wire:model="filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">All Notifications</option>
                        <option value="unread">Unread</option>
                        <option value="read">Read</option>
                    </select>
                </div>

                <!-- Type Filter -->
<div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع الإشعار</label>
                    <select wire:model="typeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">جميع الأنواع</option>
                        <option value="chat_message">رسائل الدردشة</option>
                        <option value="instruction">التعليمات</option>
                        <option value="system">النظام</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Bulk Actions Bar -->
        @if(count($selectedNotifications) > 0)
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-blue-800 font-medium">
                            تم تحديد {{ count($selectedNotifications) }} إشعار
                        </span>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <button 
                            wire:click="bulkMarkAsRead" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            وضع علامة كمقروء
                        </button>
                        <button 
                            wire:click="bulkMarkAsUnread" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            وضع علامة كغير مقروء
                        </button>
                        <button 
                            wire:click="bulkDelete" 
                            class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            حذف المحدد
                        </button>
                    </div>
                </div>
            </div>
        @endif

        <!-- Notifications List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            @if($notifications->count() > 0)
                <!-- Table Header -->
                <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="selectAll"
                            class="ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="text-sm font-medium text-gray-700">تحديد الكل</span>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="divide-y divide-gray-200">
                    @foreach($notifications as $notification)
                        <div class="px-6 py-4 hover:bg-gray-50 transition-colors 
                                    {{ is_null($notification->read_at) ? 'bg-blue-50 border-r-4 border-r-blue-500' : '' }}"
                             wire:key="notification-{{ $notification->id }}">
                            <div class="flex items-start">
                                <!-- Checkbox -->
                                <input 
                                    type="checkbox" 
                                    wire:model="selectedNotifications"
                                    value="{{ $notification->id }}"
                                    class="mt-1 ml-4 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">

                                <!-- Notification Content -->
                                <div class="flex-1 cursor-pointer" wire:click="openNotification({{ $notification->id }})">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <!-- Icon -->
                                            <span class="text-2xl">{{ $notification->formatted_icon }}</span>
                                            
                                            <!-- Title -->
                                            <h3 class="text-lg font-medium text-gray-900">
                                                {{ $notification->title }}
                                            </h3>
                                            
                                            <!-- Status Badge -->
                                            @if(is_null($notification->read_at))
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    جديد
                                                </span>
                                            @endif
                                            
                                            <!-- Type Badge -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                {{ $notification->type === 'chat_message' ? 'bg-green-100 text-green-800' : 
                                                   $notification->type === 'instruction' ? 'bg-purple-100 text-purple-800' : 
                                                   'bg-gray-100 text-gray-800' }}">
                                                {{ 
                                                    $notification->type === 'chat_message' ? 'رسالة دردشة' : 
                                                    ($notification->type === 'instruction' ? 'تعليمات' : 'نظام')
                                                }}
                                            </span>
                                        </div>
                                        
                                        <!-- Time -->
                                        <span class="text-sm text-gray-500">
                                            {{ $notification->time_ago }}
                                        </span>
                                    </div>
                                    
                                    <!-- Message -->
                                    <p class="text-gray-600 mt-2 text-base">
                                        {{ $notification->message }}
                                    </p>
                                    
                                    <!-- Meta Info -->
                                    <div class="flex items-center justify-between mt-3">
                                        <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                                            @if($notification->fromUser)
                                                <span>من: {{ $notification->fromUser->name }}</span>
                                            @endif
                                            <span>{{ $notification->created_at->format('Y/m/d H:i') }}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-2 space-x-reverse mr-4">
                                    @if(is_null($notification->read_at))
                                        <button 
                                            wire:click.stop="markAsRead({{ $notification->id }})"
                                            class="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-100 rounded-full transition-colors"
                                            title="وضع علامة كمقروء">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </button>
                                    @else
                                        <button 
                                            wire:click.stop="markAsUnread({{ $notification->id }})"
                                            class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
                                            title="وضع علامة كغير مقروء">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    @endif
                                    
                                    <button 
                                        wire:click.stop="deleteNotification({{ $notification->id }})"
                                        class="p-2 text-red-500 hover:text-red-700 hover:bg-red-100 rounded-full transition-colors"
                                        title="حذف الإشعار">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="p-12 text-center">
                    <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12h0a3 3 0 0 0-6 0v12h5z M12 2a3 3 0 0 1 3 3v6l2 2v1H7v-1l2-2V5a3 3 0 0 1 3-3z"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No notifications</h3>
                    <p class="text-gray-500">
                        @if($search || $filter !== 'all' || $typeFilter !== 'all')
                            No notifications found matching the search criteria.
                        @else
                            No notifications received yet.
                        @endif
                    </p>
                    
                    @if($search || $filter !== 'all' || $typeFilter !== 'all')
                        <button 
                            wire:click="$set('search', ''); $set('filter', 'all'); $set('typeFilter', 'all')"
                            class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                            مسح الفلاتر
                        </button>
                    @endif
                </div>
            @endif
        </div>

        <!-- Pagination -->
        @if($notifications->hasPages())
            <div class="mt-6">
                {{ $notifications->links() }}
            </div>
        @endif
    </div>
</div>

<!-- JavaScript for enhanced functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle notification success messages
        window.addEventListener('notification-success', (event) => {
            showToast(event.detail.message, 'success');
        });
        
        // Handle opening chat from notification
        window.addEventListener('open-chat', (event) => {
            const userId = event.detail.userId;
            console.log('💬 Opening chat with user:', userId);
            
            // Find chat widget and trigger selection
            const chatWidget = document.querySelector('#chat-widget-container');
            if (chatWidget && window.Livewire) {
                const componentId = chatWidget.getAttribute('wire:id');
                const component = window.Livewire.find(componentId);
                if (component) {
                    component.set('open', true);
                    setTimeout(() => {
                        component.call('selectUser', userId);
                    }, 100);
                }
            }
        });
        
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' : 
                type === 'error' ? 'bg-red-500 text-white' : 
                'bg-blue-500 text-white'
            }`;
            toast.style.transform = 'translateX(100%)';
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            // Animate out and remove
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    });
</script>
