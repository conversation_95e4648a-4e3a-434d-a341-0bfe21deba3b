<div class="max-w-6xl mx-auto p-6 bg-white">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Chat Role Permission System Test</h1>
        <p class="text-gray-600">This page helps you understand how the role-based chat system works.</p>
    </div>

    <!-- Current User Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-blue-900 mb-4">Your Current Information</h2>
            
            <div class="space-y-3">
                <div>
                    <span class="font-medium text-gray-700">Name:</span>
                    <span class="ml-2 text-gray-900">{{ auth()->user()->name }}</span>
                </div>
                
                <div>
                    <span class="font-medium text-gray-700">Your Roles:</span>
                    <div class="mt-2 flex flex-wrap gap-2">
                        @if(count($currentUserRoles) > 0)
                            @foreach($currentUserRoles as $role)
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                    {{ $role }}
                                </span>
                            @endforeach
                        @else
                            <span class="text-red-600 text-sm">No roles assigned</span>
                        @endif
                    </div>
                </div>
                
                <div>
                    <span class="font-medium text-gray-700">You Can Chat With Roles:</span>
                    <div class="mt-2 flex flex-wrap gap-2">
                        @if(count($allowedToRoles) > 0)
                            @foreach($allowedToRoles as $role)
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    {{ $role }}
                                </span>
                            @endforeach
                        @else
                            <span class="text-red-600 text-sm">No chat permissions</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-green-900 mb-4">Available Users to Chat With</h2>
            
            @if(count($availableUsers) > 0)
                <div class="space-y-2 max-h-64 overflow-y-auto">
                    @foreach($availableUsers as $user)
                        <div class="flex items-center justify-between p-3 bg-white rounded border">
                            <div>
                                <div class="font-medium text-gray-900">{{ $user->name }}</div>
                                <div class="text-sm text-gray-600">
                                    Roles: 
                                    @if($user->roles)
                                        {{ implode(', ', $user->roles) }}
                                    @else
                                        No roles
                                    @endif
                                </div>
                            </div>
                            <div class="flex items-center">
                                @if($user->isOnline())
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-sm text-green-600">Online</span>
                                @else
                                    <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                                    <span class="text-sm text-gray-500">Offline</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-red-600">No users available to chat with based on your role permissions.</p>
            @endif
        </div>
    </div>

    <!-- Role Permission Testing -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Test Role Permissions</h2>
        <p class="text-gray-600 mb-4">Click on any role to see who that role can chat with:</p>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-6">
            @foreach($allRoles as $roleKey => $roleLabel)
                <button 
                    wire:click="testRolePermissions('{{ $roleKey }}')"
                    class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-left
                           {{ $selectedRole === $roleKey ? 'ring-2 ring-blue-500 bg-blue-50' : '' }}">
                    <div class="font-medium text-sm">{{ $roleLabel }}</div>
                </button>
            @endforeach
        </div>

        @if($testResults)
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <h3 class="font-semibold text-lg mb-3">
                    Results for: <span class="text-blue-600">{{ $testResults['role'] }}</span>
                </h3>
                
                <div class="mb-4">
                    <span class="font-medium text-gray-700">Can chat with roles:</span>
                    <div class="mt-2 flex flex-wrap gap-2">
                        @if(count($testResults['can_chat_with']) > 0)
                            @foreach($testResults['can_chat_with'] as $role)
                                <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                                    {{ $role }}
                                </span>
                            @endforeach
                        @else
                            <span class="text-red-600 text-sm">No chat permissions</span>
                        @endif
                    </div>
                </div>

                <div>
                    <span class="font-medium text-gray-700">Users with these roles:</span>
                    @if(count($testResults['users_with_allowed_roles']) > 0)
                        <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                            @foreach($testResults['users_with_allowed_roles'] as $user)
                                <div class="p-2 bg-gray-50 rounded border">
                                    <div class="font-medium text-sm">{{ $user->name }}</div>
                                    <div class="text-xs text-gray-600">
                                        Roles: {{ $user->roles ? implode(', ', $user->roles) : 'No roles' }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-red-600 text-sm mt-2">No users found with the allowed roles.</p>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <!-- Current Permissions Table -->
    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Current Active Permissions</h2>
            <p class="text-gray-600 text-sm mt-1">These are the currently active chat permissions in the system.</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Role</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">→</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Role</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($allPermissions as $permission)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                    {{ $permission->from_role }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="text-gray-400">→</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    {{ $permission->to_role }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                @if($permission->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Instructions -->
    <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 class="text-lg font-semibold text-yellow-900 mb-3">How to Use the Chat Role System</h2>
        <div class="space-y-2 text-yellow-800">
            <p><strong>1. Set User Roles:</strong> Assign roles to users in the user management section.</p>
            <p><strong>2. Configure Permissions:</strong> Use the "Chat Permissions" section in the admin panel to define which roles can chat with which other roles.</p>
            <p><strong>3. Test the Chat:</strong> Users will only see other users they have permission to chat with in the chat widget.</p>
            <p><strong>4. Real-time Updates:</strong> Changes to permissions take effect immediately.</p>
        </div>
    </div>
</div>
