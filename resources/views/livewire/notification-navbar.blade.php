<div class="relative notification-navbar" id="notification-navbar" wire:id="{{ $this->id }}">
    <!-- Notification Bell Icon -->
    <button 
        id="notification-bell-btn"
        wire:click="markAllAsRead"
        class="relative p-2 text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg transition-all duration-200 notification-bell dark:text-gray-300 dark:hover:text-gray-100"
        :class="{ 'animate-pulse': {{ $unreadCount > 0 ? 'true' : 'false' }} }">
        
        <!-- Bell Icon -->
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7C18 6.279 15.726 4 12.857 4h-1.714C8.274 4 6 6.279 6 9.05v.7a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
        </svg>
        
        <!-- Unread Count Badge -->
        @if($unreadCount > 0)
            <span class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none bg-red-500 translate-x-1/2 -translate-y-1/2 border-2 border-red-500 rounded-full min-w-[20px] h-5 notification-badge" style="color:white;background-color:red;">
                {{ $unreadCount > 99 ? '99+' : $unreadCount }}
            </span>
        @endif
    </button>

    <!-- Dropdown Panel -->
    <div id="notification-dropdown" 
         class="absolute top-full right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50 notification-dropdown hidden"
         style="direction: rtl; min-width: 400px; max-width: 500px; height: 480px;">
         
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 rounded-t-lg flex-shrink-0">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    @if($unreadCount > 0)
                        <button 
                            wire:click="markAllAsRead"
                            class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                            Mark all as read
                        </button>
                        <span class="text-gray-300 dark:text-gray-500">|</span>
                    @endif
                    <button 
                        wire:click="deleteAllRead"
                        class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium transition-colors">
                        Delete read
                    </button>
                </div>
            </div>
            
            @if($unreadCount > 0)
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    You have {{ $unreadCount }} unread notifications
                </p>
            @endif
        </div>

        
        <!-- Notifications List -->
        <div class="flex flex-col" style="height: 400px;">
            <!-- Scrollable notifications area with fixed height -->
            <div class="flex-1 overflow-y-auto notification-list" style="max-height: 320px; min-height: 320px;">
                @if(count($recentNotifications) > 0)
                    @foreach($recentNotifications as $notification)
                        <div class="notification-item px-4 py-3 border-b border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors 
                                    {{ is_null($notification['read_at']) ? 'unread bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900 dark:to-blue-800 shadow-sm' : '' }}"
                             wire:key="notification-{{ $notification['id'] }}">
                            <div class="flex items-start justify-between">
                                <!-- Notification Content -->
                                <div class="flex-1 cursor-pointer" wire:click="openNotification({{ $notification['id'] }})">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <!-- Icon -->
                                        <span class="text-lg">
                                            {{ $notification['icon'] ?? '🔔' }}
                                        </span>
                                        
                                        <!-- Title -->
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate {{ is_null($notification['read_at']) ? 'font-bold text-blue-900 dark:text-blue-100' : '' }}">
                                            {{ $notification['title'] }}
                                        </p>
                                        
                                        <!-- Unread Indicator -->
                                        @if(is_null($notification['read_at']))
                                            <span class="w-3 h-3 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse shadow-md"></span>
                                        @endif
                                    </div>
                                    
                                    <!-- Message -->
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2 {{ is_null($notification['read_at']) ? 'text-gray-700 dark:text-gray-200 font-medium' : '' }}">
                                        {{ $notification['message'] }}
                                    </p>
                                    
                                    <!-- Meta Information -->
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ \Carbon\Carbon::parse($notification['created_at'])->diffForHumans() }}
                                        </span>
                                        @if(isset($notification['from_user']) && $notification['from_user'])
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                من: {{ $notification['from_user']['name'] ?? 'النظام' }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-1 space-x-reverse ml-2">
                                    @if(is_null($notification['read_at']))
                                        <button 
                                            wire:click.stop="markAsRead({{ $notification['id'] }})"
                                            class="p-1 text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                                            title="وضع علامة كمقروء">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </button>
                                    @else
                                        <button 
                                            wire:click.stop="markAsUnread({{ $notification['id'] }})"
                                            class="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                            title="وضع علامة كغير مقروء">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    @endif
                                    
                                    <button 
                                        wire:click.stop="deleteNotification({{ $notification['id'] }})"
                                        class="p-1 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                                        title="حذف الإشعار">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Empty State -->
                    <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                        <svg class="w-12 h-12 mx-auto text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12h0a3 3 0 0 0-6 0v12h5z M12 2a3 3 0 0 1 3 3v6l2 2v1H7v-1l2-2V5a3 3 0 0 1 3-3z"></path>
                        </svg>
                        <p class="text-sm">No notifications</p>
                    </div>
                @endif
            </div>
            
            <!-- Footer - Always visible outside the scrollable area -->
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg flex-shrink-0">
                <button 
                    wire:click="viewAllNotifications"
                    onclick="console.log('View all notifications clicked!'); $wire.viewAllNotifications();"
                    class="w-full text-center text-sm text-gray-900 dark:text-gray-100 hover:text-blue-800 dark:hover:text-blue-300 font-bold transition-all duration-200 py-3 px-4 rounded-md border border-transparent hover:border-blue-200 dark:hover:border-blue-600 cursor-pointer">
                    📋 View all notifications →
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Pusher JavaScript Library -->
<script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>

<!-- JavaScript for enhanced functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Find elements
    const bellButton = document.getElementById('notification-bell-btn');
    const dropdown = document.getElementById('notification-dropdown');
    
    if (!bellButton || !dropdown) {
        return;
    }
    
    
    // FORCE dropdown to start hidden with multiple methods
    function forceHideDropdown() {
        dropdown.classList.add('hidden');
        dropdown.style.display = 'none';
        dropdown.style.visibility = 'hidden';
        dropdown.style.opacity = '0';
        console.log('🔒 Dropdown forcefully hidden');
    }
    
    function forceShowDropdown() {
        dropdown.classList.remove('hidden');
        dropdown.style.display = 'flex';
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        console.log('👁️ Dropdown forcefully shown');
    }
    
    // Start hidden
    forceHideDropdown();
    
    let isOpen = false;
    
    // Bell button click
    bellButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (isOpen) {
            // Close dropdown
            forceHideDropdown();
            isOpen = false;
        } else {
            // Open dropdown
            forceShowDropdown();
            isOpen = true;
            
            // Load notifications
            if (window.Livewire) {
                try {
                    const wireId = dropdown.closest('[wire\\:id]').getAttribute('wire:id');
                    const component = window.Livewire.find(wireId);
                    if (component) {
                        component.call('loadNotifications');
                    }
                } catch (e) {
                    console.log('⚠️ Livewire error:', e);
                }
            }
        }
    });
    
    // Click outside to close
    document.addEventListener('click', function(e) {
        if (isOpen) {
            // Check if click is outside both dropdown and bell button
            if (!dropdown.contains(e.target) && !bellButton.contains(e.target)) {
                forceHideDropdown();
                isOpen = false;
            }
        }
    });
    
    // Escape key to close
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isOpen) {
            forceHideDropdown();
            isOpen = false;
        }
    });
    
    // Close dropdown when clicking action buttons
    dropdown.addEventListener('click', function(e) {
        if (e.target.closest('button[wire\\:click]')) {
            setTimeout(function() {
                forceHideDropdown();
                isOpen = false;
            }, 100);
        }
    });
    
    // Debug function
    window.debugDropdown = function() {
        console.log('Dropdown state:', {
            isOpen: isOpen,
            hasHiddenClass: dropdown.classList.contains('hidden'),
            displayStyle: dropdown.style.display,
            visibilityStyle: dropdown.style.visibility,
            opacityStyle: dropdown.style.opacity,
            computedDisplay: window.getComputedStyle(dropdown).display,
            element: dropdown
        });
    };
    
    // Force close on any Livewire update
    document.addEventListener('livewire:update', function() {
        if (isOpen) {
            forceShowDropdown();
        }
    });
    
    try {
        // Enable pusher logging for debugging
        Pusher.logToConsole = true;

        // Initialize Pusher with your credentials
        var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
            cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
            encrypted: true,
        });

        // Connection event handlers
        pusher.connection.bind('connected', function() {
            console.log('✅ Pusher connected for financial notifications');
        });

        pusher.connection.bind('error', function(err) {
            console.log('❌ Pusher connection error for financial notifications:', err);
        });

        // Subscribe to the user's public financial notifications channel
        @auth
        var financialChannel = pusher.subscribe('financial-notifications-{{ auth()->id() }}');
        
        financialChannel.bind('pusher:subscription_succeeded', function() {
            console.log('✅ Successfully subscribed to financial notifications channel');
        });

        financialChannel.bind('pusher:subscription_error', function(error) {
            console.log('❌ Failed to subscribe to financial notifications channel:', error);
        });

        // Listen for financial notification events
        financialChannel.bind('financial-notification-received', function(data) {
            console.log('🟢 Received financial notification via Pusher:', data);
            
            // Trigger Livewire component to handle the notification
            if (window.Livewire) {
                try {
                    const wireId = dropdown.closest('[wire\\:id]').getAttribute('wire:id');
                    const component = window.Livewire.find(wireId);
                    if (component) {
                        component.call('handleNewNotification', data);
                    }
                } catch (e) {
                    console.log('⚠️ Error calling Livewire handleNewNotification:', e);
                }
            }
        });
        @endauth

        console.log('✅ Pusher financial notifications initialized');
    } catch (error) {
        console.log('❌ Error initializing Pusher for financial notifications:', error);
    }
    
    // Other event handlers...
    window.addEventListener('notification-success', (event) => {
        console.log('✅ Success:', event.detail.message);
        showToast(event.detail.message, 'success');
    });
    
    window.addEventListener('show-browser-notification', (event) => {
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification(event.detail.title, {
                    body: event.detail.message,
                    icon: '/favicon.ico',
                    tag: 'crm-notification',
                    requireInteraction: false
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification(event.detail.title, {
                            body: event.detail.message,
                            icon: '/favicon.ico',
                            tag: 'crm-notification',
                            requireInteraction: false
                        });
                    }
                });
            }
        }
    });

    // New event listener for playing notification sound
    window.addEventListener('play-notification-sound', () => {
        console.log('🔊 Playing notification sound...');
        playNotificationSound();
    });

    // New event listener for flashing notification icon
    window.addEventListener('flash-notification-icon', () => {
        console.log('✨ Flashing notification icon...');
        const bell = document.querySelector('.notification-bell');
        if (bell) {
            bell.classList.add('animate-ping');
            setTimeout(() => {
                bell.classList.remove('animate-ping');
            }, 1000);
        }
    });

    // Make playNotificationSound globally available
    window.playNotificationSound = playNotificationSound;
    
    window.addEventListener('open-chat', (event) => {
        const userId = event.detail.userId;
        console.log('💬 Opening chat with user:', userId);
        
        const chatWidget = document.querySelector('#chat-widget-container');
        if (chatWidget && window.Livewire) {
            const componentId = chatWidget.getAttribute('wire:id');
            const component = window.Livewire.find(componentId);
            if (component) {
                component.set('open', true);
                setTimeout(() => {
                    component.call('selectUser', userId);
                }, 100);
            }
        }
    });
    
    function playNotificationSound() {
        try {
            // Create a simple beep sound using Web Audio API
            if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // Resume audio context if suspended (required for autoplay policy)
                if (audioContext.state === 'suspended') {
                    audioContext.resume().then(() => {
                        createBeepSound(audioContext);
                    }).catch(error => {
                        console.log('🔊 Could not resume audio context:', error);
                        // Fallback to HTML5 audio
                        playFallbackSound();
                    });
                } else {
                    // playFallbackSound();
                    // createBeepSound(audioContext);
                }
            } else {
                // Fallback for browsers without Web Audio API
                playFallbackSound();
            }
        } catch (error) {
            console.log('🔊 Could not play notification sound:', error);
            // Try fallback method
            playFallbackSound();
        }
    }
    
    function createBeepSound(audioContext) {
        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
            
            console.log('🔊 Web Audio API sound played');
        } catch (error) {
            console.log('🔊 Web Audio API failed:', error);
            playFallbackSound();
        }
    }
    
    function playFallbackSound() {
        try {
            // Create a data URL for a simple beep sound
            const audioData = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
            const audio = new Audio(audioData);
            audio.volume = 0.3;
            
            // Try to play with user gesture fallback
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('🔊 Fallback audio played');
                }).catch(error => {
                    console.log('🔊 Fallback audio failed:', error.name);
                    // Store the intent to play sound when user interacts
                    window.pendingNotificationSound = true;
                });
            }
        } catch (error) {
            console.log('🔊 All audio methods failed:', error);
        }
    }
    
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-blue-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`;
        toast.style.transform = 'translateX(100%)';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    // Request notification permission on page load
    if ('Notification' in window && Notification.permission === 'default') {
        // Request permission when user first interacts with the page
        document.addEventListener('click', function requestNotificationPermission() {
            Notification.requestPermission().then(permission => {
                console.log('🔔 Notification permission:', permission);
            });
            // Remove the event listener after first click
            document.removeEventListener('click', requestNotificationPermission);
        }, { once: true });
    }
    
    // Handle pending notification sounds when user interacts
    document.addEventListener('click', function() {
        if (window.pendingNotificationSound) {
            window.pendingNotificationSound = false;
            playNotificationSound();
        }
    }, { once: true });
});
</script>

<!-- CSS for enhanced styling -->
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    /* Enhanced scroll styling for notification list */
    .notification-list {
        max-height: 320px !important;
        min-height: 320px !important;
        overflow-y: auto !important;
        overflow-x: hidden;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }
    
    .notification-list::-webkit-scrollbar {
        width: 8px;
    }
    
    .notification-list::-webkit-scrollbar-track {
        background: #f1f5f9;
        
        margin: 4px 0;
    }
    
    .notification-list::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        
        

    }
    
    .notification-list::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }
    
    .notification-list::-webkit-scrollbar-thumb:active {
        background: #64748b;
    }
    
    /* Fixed dropdown container */
    .notification-dropdown {
        height: 480px !important;
        max-height: 480px !important;
        flex-direction: column !important;
    }
    
    /* Show dropdown when not hidden */
    .notification-dropdown:not(.hidden) {
        display: flex !important;
    }
    
    /* Hide dropdown when hidden class is present */
    .notification-dropdown.hidden {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }
    
    /* Ensure proper flex layout */
    .notification-dropdown > div {
        display: flex;
        flex-direction: column;
        
    }
    
    /* Enhanced unread notification styling */
    .notification-item.unread {
        position: relative;
        background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%) !important;
        
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
    }
    
    .notification-item.unread::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        /* background: linear-gradient(90deg, #3b82f6, #60a5fa); */
        opacity: 0.7;
    }
    
    .notification-item.unread:hover {
        background: linear-gradient(135deg, #bfdbfe 0%, #dbeafe 100%) !important;
        transform: translateX(-2px);
        box-shadow: 0 4px 8px rgba(37, 99, 235, 0.15);
    }
    
    /* Smooth transitions for all notification items */
    .notification-item {
        transition: all 0.2s ease-in-out;
        border-radius: 0;
    }
    
    .notification-item:hover {
        transform: translateX(-1px);
    }
    
    /* Enhanced unread indicator */
    .notification-item.unread .w-3.h-3 {
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }
    
    /* Better button hover effects */
    .notification-item button {
        transition: all 0.15s ease;
        border-radius: 4px;
    }
    
    .notification-item button:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(1.1);
    }
    
    /* Footer button enhancement */
    .notification-dropdown .flex-shrink-0 button {
        transition: all 0.2s ease;
        border-radius: 6px;
        position: relative;
        z-index: 10;
        pointer-events: auto;
        display: block !important;
        visibility: visible !important;
    }
    
    .notification-dropdown .flex-shrink-0 button:hover {
        background-color: #eff6ff !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
    }
    
    /* Ensure the footer container is always visible */
    .notification-dropdown .flex-shrink-0 {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative;
        z-index: 10;
    }
    
    /* Notification count badge styling */
    .notification-badge {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 700;
        letter-spacing: -0.025em;
    }
    
    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .notification-item.unread {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
            
        }
        
        .notification-item.unread:hover {
            background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
        }
        
        .notification-list::-webkit-scrollbar-track {
            background: #374151;
        }
        
        .notification-list::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-color: #374151;
        }
        
        .notification-list::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
    }
    
    /* Filament Dark Mode Support */
    .dark .notification-item.unread {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
        color: #f9fafb !important;
    }
    
    .dark .notification-item.unread:hover {
        background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
    }
    
    .dark .notification-list::-webkit-scrollbar-track {
        background: #374151 !important;
    }
    
    .dark .notification-list::-webkit-scrollbar-thumb {
        background: #6b7280 !important;
        border-color: #374151 !important;
    }
    
    .dark .notification-list::-webkit-scrollbar-thumb:hover {
        background: #9ca3af !important;
    }
    
    .dark .notification-list::-webkit-scrollbar-thumb:active {
        background: #d1d5db !important;
    }
    
    /* Enhanced button hover effects for dark mode */
    .dark .notification-item button:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }
    
    .dark .notification-dropdown .flex-shrink-0 button:hover {
        background-color: #374151 !important;
        color: #93c5fd !important;
    }
</style>
