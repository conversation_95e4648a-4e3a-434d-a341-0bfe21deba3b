<div>
    <!-- Instruction Summary Modal (shown on login) -->
    @if($showSummaryModal && $this->unreadCount > 0)
        <div class="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center p-4" aria-labelledby="summary-modal-title" role="dialog" aria-modal="true">
            <!-- Enhanced background overlay with blur effect -->
            <div class="fixed inset-0 bg-gradient-to-br from-black/30 via-gray-900/20 to-black/30 transition-all duration-500 backdrop-blur-md" aria-hidden="true"></div>

            <!-- Modal panel - Enhanced with better shadows and animations -->
            <div class="relative bg-white rounded-3xl shadow-2xl transform transition-all duration-500 max-w-lg w-full mx-4 border border-gray-100/50 backdrop-blur-sm animate-in zoom-in-95 fade-in-0">
                <!-- Enhanced Header with better gradient and padding -->
                <div class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-t-3xl px-8 py-8 relative overflow-hidden">
                    <!-- Decorative background pattern -->
                    <div class="absolute inset-0 bg-white/5 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-white/20 via-transparent to-transparent"></div>
                    
                    <div class="flex items-center justify-between relative">
                        <div class="flex items-center space-x-4">
                            <!-- Enhanced icon with animation -->
                            {{-- <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 shadow-lg">
                                <svg class="h-7 w-7  animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 17h5l-5 5-5-5h5v-12h5v12z" />
                                </svg>
                            </div> --}}
                            <div>
                                <h3 class="text-2xl font-bold  drop-shadow-sm" id="summary-modal-title">
                                    📋 New Instructions
                                </h3>
                                <p class="text-blue-100 text-sm font-medium">You have unread messages</p>
                            </div>
                        </div>
                        <!-- Enhanced close button -->
                       
                    </div>
                </div>
                
                <!-- Enhanced Content -->
                <div class="">
                    <!-- Enhanced Stats Row with better visual hierarchy -->
                    <div class="flex items-center justify-center space-x-12 mb-8">
                        <!-- Unread Instructions with enhanced styling -->
                        <div class="text-center group">
                            <div class="relative">
                                <div class="text-4xl font-black bg-gradient-to-br from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-200">
                                    {{ $this->unreadCount }}
                                </div>
                                <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg blur opacity-20 group-hover:opacity-30 transition duration-200"></div>
                            </div>
                            <div class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Unread</div>
                        </div>
                        
                        @if($this->sentCount > 0)
                        <!-- Enhanced Divider -->
                        <div class="h-16 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                        
                        <!-- Sent Instructions with enhanced styling -->
                        <div class="text-center group">
                            <div class="relative">
                                <div class="text-4xl font-black bg-gradient-to-br from-green-600 to-emerald-600 bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-200">
                                    {{ $this->sentCount }}
                                </div>
                                <div class="absolute -inset-1 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg blur opacity-20 group-hover:opacity-30 transition duration-200"></div>
                            </div>
                            <div class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Sent This Week</div>
                        </div>
                        @endif
                    </div>

                    <!-- Enhanced Recent Unread Instructions Preview -->
                    @if($this->unreadCount > 0)
                        <div class="border-t border-gray-100 pt-6">
                            <h4 class="text-sm font-bold text-gray-900 mb-4 flex items-center">
                                <span class="h-2 w-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
                                📬 Recent Instructions
                            </h4>
                            <div class="space-y-3 max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                @foreach($unreadInstructions->take(3) as $instruction)
                                    <div class="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-4 text-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:scale-[1.02] group">
                                        <div class="flex items-center justify-between">
                                            <span class="font-semibold truncate text-sm text-gray-800 group-hover:text-gray-900">{{ Str::limit($instruction->title, 25) }}</span>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold shadow-sm 
                                                @if($instruction->priority === 'urgent') bg-gradient-to-r from-red-500 to-red-600
                                                @elseif($instruction->priority === 'high') bg-gradient-to-r from-orange-500 to-orange-600
                                                @elseif($instruction->priority === 'medium') bg-gradient-to-r from-blue-500 to-blue-600
                                                @else bg-gradient-to-r from-green-500 to-green-600
                                                @endif">
                                                {{ ucfirst($instruction->priority) }}
                                            </span>
                                        </div>
                                        <div class="text-gray-500 mt-2 text-xs flex items-center">
                                            <svg class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            From: {{ $instruction->creator->name ?? 'System' }}
                                        </div>
                                    </div>
                                @endforeach
                                
                                @if($this->unreadCount > 3)
                                    <div class="text-center text-xs text-gray-500 pt-3 border-t border-gray-100">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-600 font-medium">
                                            + {{ $this->unreadCount - 3 }} more instructions
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
                
                <!-- Enhanced Actions with better padding -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-b-3xl px-8 py-8 flex justify-end space-x-4">
                    @if($this->unreadCount > 0)
                        <button type="button" 
                                wire:click="markAllAsRead"
                                class="inline-flex items-center px-6 py-3 border border-blue-200 text-sm font-semibold rounded-2xl text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 hover:scale-105 active:scale-95 shadow-sm hover:shadow-md">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Mark All Read
                        </button>
                        <button type="button" 
                                wire:click="closeSummaryModal"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl ">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            View Instructions
                        </button>
                    @else
                        <button type="button" 
                                wire:click="closeSummaryModal"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl ">
                            Continue
                        </button>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Individual Instruction Modal -->
    @if($showModal && $currentInstruction)
        <div class="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center p-4 space-2" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <!-- Enhanced background overlay -->
            <div class="fixed inset-0 bg-gradient-to-br from-black/30 via-gray-900/20 to-black/30 transition-all duration-500 backdrop-blur-md" aria-hidden="true"></div>

            <!-- Enhanced Modal panel -->
            <div class="relative bg-white rounded-3xl shadow-2xl transform transition-all duration-500 max-w-2xl w-full mx-4 border border-gray-100/50 backdrop-blur-sm animate-in zoom-in-95 fade-in-0 space-2">
                <!-- Enhanced Header with priority-based gradients and better padding -->
                <div class="bg-gradient-to-br 
                    @if($currentInstruction->priority === 'urgent') from-red-600 via-red-700 to-red-800
                    @elseif($currentInstruction->priority === 'high') from-orange-600 via-orange-700 to-orange-800
                    @elseif($currentInstruction->priority === 'medium') from-blue-600 via-blue-700 to-blue-800
                    @else from-green-600 via-green-700 to-green-800
                    @endif
                    rounded-t-3xl px-8 py-8 relative overflow-hidden">
                    <!-- Decorative background pattern -->
                    <div class="absolute inset-0 bg-white/5 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-white/20 via-transparent to-transparent"></div>
                    
                    <div class="flex items-center justify-between relative" style="    padding: 0px 18px;">
                        <div class="flex items-center space-x-4">
                            <!-- Enhanced priority icon -->
                            <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 shadow-lg">
                                <svg class="h-7 w-7 " fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    @if($currentInstruction->priority === 'urgent')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    @elseif($currentInstruction->priority === 'high')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    @else
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    @endif
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold  drop-shadow-sm" id="modal-title">
                                    📋 {{ $currentInstruction->title }}
                                </h3>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-white/20 border border-white/30 backdrop-blur-sm shadow-sm ">
                                    {{ ucfirst($currentInstruction->priority) }} Priority
                                </span>
                            </div>
                        </div>
                        <!-- Enhanced close button -->
                        
                    </div>
                </div>
                
                <!-- Enhanced Content -->
                <div class="">
                    <!-- Content with better typography -->
                    <div class="prose prose-lg max-w-none">
                        <div class="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-100 shadow-sm">
                            <p class="text-gray-800 whitespace-pre-wrap leading-relaxed font-medium text-base">{{ $currentInstruction->content }}</p>
                        </div>
                    </div>
                    
                    <!-- Enhanced Creator and Date section -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="bg-gray-50 rounded-2xl p-4 space-y-3">
                            <p class="flex items-center text-sm font-medium text-gray-700">
                                <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <span class="font-semibold">From:</span> {{ $currentInstruction->creator->name ?? 'System' }}
                            </p>
                            <p class="flex items-center text-sm font-medium text-gray-700">
                                <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <svg class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <span class="font-semibold">Created:</span> {{ $currentInstruction->created_at->format('M j, Y g:i A') }}
                            </p>
                            @if($currentInstruction->expires_at)
                                <p class="flex items-center text-sm font-medium text-orange-700">
                                    <div class="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center mr-3">
                                        <svg class="h-4 w-4 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <span class="font-semibold">Expires:</span> {{ $currentInstruction->expires_at->format('M j, Y g:i A') }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- Enhanced Actions with better padding -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-b-3xl px-8 py-8 flex justify-center space-4" >
                    
                    <button type="button" 
                            wire:click="markAsRead"
                            class="flex items-center justify-center bg-green-600 text-white px-4 py-2 rounded-md" style="background-color: black;color: white;">
                        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    Got it 
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Enhanced Floating notification badge -->
    @if($this->unreadCount > 0 && !$showModal && !$showSummaryModal)
        <div class="fixed bottom-6 right-6 z-40">
            
        </div>
    @endif

    <!-- Auto-refresh and interaction scripts -->
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Pusher for real-time instruction notifications
            console.log('📋 Initializing real-time instruction notifications...');
            
            // Initialize Pusher with your credentials
            var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
                cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
            });

            // Connection event handlers
            pusher.connection.bind('connected', function() {
                console.log('✅ Pusher connected for instruction notifications');
            });

            pusher.connection.bind('error', function(err) {
                console.log('❌ Pusher connection error for instructions:', err);
            });

            // Subscribe to the instructions channel
            var instructionsChannel = pusher.subscribe('instructions');
            
            instructionsChannel.bind('pusher:subscription_succeeded', function() {
                console.log('✅ Successfully subscribed to instructions channel');
            });

            instructionsChannel.bind('pusher:subscription_error', function(error) {
                console.log('❌ Failed to subscribe to instructions channel:', error);
            });

            // Listen for new instruction events
            instructionsChannel.bind('new-instruction', function(data) {
                console.log('🟢 Received new instruction via Pusher:', data);
                
                // Check if this instruction is relevant to the current user
                const currentUserRole = getCurrentUserRole();
                if (data.target_roles && data.target_roles.includes(currentUserRole)) {
                    console.log('📋 New instruction is relevant to current user role:', currentUserRole);
                    
                    // Refresh instructions and show popup if there are unread instructions
                    @this.call('loadInstructionsAndShowPopups').then(() => {
                        console.log('📋 Instructions refreshed after Pusher event');
                    });
                } else {
                    console.log('📋 New instruction not relevant to current user role:', currentUserRole);
                }
            });

            // Helper function to get current user role
            function getCurrentUserRole() {
                // This should match the logic in your backend for determining user role
                const userIsAdmin = {{ auth()->user()->is_admin ? 'true' : 'false' }};
                if (userIsAdmin) {
                    return 'admin';
                }
                
                const userRoles = @json(auth()->user()->roles ?? []);
                return userRoles.length > 0 ? userRoles[0] : null;
            }
            
            // Delay initial check to ensure everything is loaded
            setTimeout(function() {
                console.log('📋 Initial load - Checking for instructions...');
                
                // Load instructions and show popups if there are unread instructions
                @this.call('loadInstructionsAndShowPopups').then(() => {
                    console.log('📋 Initial instructions loaded, unread count:', @this.unreadCount);
                });
            }, 1000);

            // Check when user becomes active (focus/click) - but only if no modals are shown
            ['focus', 'click', 'mousemove'].forEach(event => {
                window.addEventListener(event, function() {
                    if (!@this.showModal && !@this.showSummaryModal) {
                        @this.call('loadInstructions'); // No parameter = no auto-popup
                    }
                }, { once: true, passive: true });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Press 'I' to show instruction summary
                if (e.key === 'i' || e.key === 'I') {
                    if (!@this.showModal && !@this.showSummaryModal) {
                        @this.call('showSummary');
                    }
                }
                
                // Press Escape to close modals
                if (e.key === 'Escape') {
                    if (@this.showSummaryModal) {
                        @this.call('closeSummaryModal');
                    } else if (@this.showModal) {
                        @this.call('closeModal');
                    }
                }
                
                // Press Enter to mark as read (when instruction modal is open)
                if (e.key === 'Enter' && @this.showModal && !@this.showSummaryModal) {
                    @this.call('markAsRead');
                }
            });
        });

        // Listen for custom events
        window.addEventListener('instruction-marked-as-read', function() {
            @this.call('loadInstructions'); // No parameter = no auto-popup
        });
    </script>

    <!-- Custom CSS for enhanced styling -->
    <style>
        @keyframes animate-in {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .animate-in {
            animation: animate-in 0.5s ease-out;
        }

        .zoom-in-95 {
            animation: zoom-in-95 0.3s ease-out;
        }

        @keyframes zoom-in-95 {
            from {
                transform: scale(0.95);
            }
            to {
                transform: scale(1);
            }
        }

        .fade-in-0 {
            animation: fade-in-0 0.3s ease-out;
        }

        @keyframes fade-in-0 {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Custom scrollbar */
        .scrollbar-thin {
            scrollbar-width: thin;
        }

        .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 0.375rem;
        }

        .scrollbar-track-gray-100::-webkit-scrollbar-track {
            background-color: #f3f4f6;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
    </style>
</div>
