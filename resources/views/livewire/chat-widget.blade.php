<div
    id="chat-widget-container"
    wire:id="{{ $this->id }}"
    style="position: fixed; bottom: 0px; right: 0px; z-index: 9999; font-family: 'Segoe UI', 'Cairo', '<PERSON><PERSON>', 'Noto Sans Arabic', Tahoma, Geneva, Verdana, sans-serif; display: flex; flex-direction: column; align-items: flex-end; gap: 10px; direction: rtl;">
    <!-- زر الدردشة العائم مع عداد الرسائل غير المقروءة -->
    {{-- <button wire:click="toggle"
            style="background: #374151; color: white; border: none; border-radius: 50%; width: 60px; height: 60px; font-size: 24px; cursor: pointer; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; position: relative;">
        💬
        @if($this->totalUnreadCount > 0)
            <div style="position: absolute; top: -8px; left: -8px; background: #ef4444; color: white; border-radius: 50%; width: 20px; height: 20px; font-size: 11px; font-weight: bold; display: flex; align-items: center; justify-content: center; border: 2px solid white;">
                {{ $this->totalUnreadCount > 99 ? '99+' : $this->totalUnreadCount }}
            </div>
        @endif
    </button> --}}

    @if($open)
        <!-- Admin status indicator -->
        {{-- @if(is_admin())
            <div style="background: linear-gradient(135deg, #7c3aed, #a855f7); color: white; padding: 8px 12px; border-radius: 8px; font-size: 12px; font-weight: 600; text-align: center; margin-bottom: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                👑 Admin Mode - You can chat with all users
            </div>
        @endif --}}
        <input type="text" wire:model.debounce.300ms="search" placeholder="Search for a user..."
               style="width: 100%; padding: 6px 8px; border-radius: 8px; border: 1px solid #e5e7eb; color: black; text-align: right; direction: rtl; margin-bottom: -8px;">
        <div style="display: flex; gap: 5px; direction: rtl;">
            <!-- قائمة المستخدمين -->
            <div
                style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 12px; width: 120px; max-height: 400px; overflow-y: auto; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); direction: rtl;">
                @if(count($users) > 0)
                    @foreach($users as $user)
                        @if($user->id !== auth()->id())
                        <div wire:click="selectUser({{ $user->id }})"
                                 style="display: flex; flex-direction: column; align-items: center; margin-bottom: 12px; cursor: pointer; padding: 4px; border-radius: 8px; transition: background 0.2s ease; position: relative;
                                 {{ $selectedUser && $selectedUser->id === $user->id ? 'background: #fef3c7;' : '' }}
                                 {{ $user->is_admin ? '' : '' }}"
                                 onmouseover="this.style.background='{{ $user->is_admin ? '#f3f4f6' : '#f3f4f6' }}'" 
                                 onmouseout="this.style.background='{{ $selectedUser && $selectedUser->id === $user->id ? '#fef3c7' : ($user->is_admin ? '#f3f4f6' : '#f3f4f6') }}'">
                                
                                <!-- Admin Crown Badge -->
                                {{-- @if($user->is_admin)
                                    <div style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; padding: 2px 6px; border-radius: 12px; font-size: 8px; font-weight: bold; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); z-index: 10;">
                                        👑 ADMIN
                                    </div>
                                @endif --}}
                                
                                <div style="position: relative;">
                            <img
                                src="{{ $user->profile_photo_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&background=374151&color=fff' }}"
                                alt="{{ $user->name }}"
                                style="width: 56px; height: 56px; border-radius: 50%; object-fit: cover; border: {{ $user->is_admin ? '3px solid #fbbf24' : '2px solid #e5e7eb' }};">
                                    <!-- Online status indicator -->
                                    @if($user->last_seen_at && \Carbon\Carbon::parse($user->last_seen_at)->gt(now()->subMinutes(5)))
                                        <div style="position: absolute; bottom: 2px; left: 2px; width: 12px; height: 12px; background: #22c55e; border: 2px solid white; border-radius: 50%;"></div>
                                    @else
                                        <div style="position: absolute; bottom: 2px; left: 2px; width: 12px; height: 12px; background: #6b7280; border: 2px solid white; border-radius: 50%;"></div>
                                    @endif
                                    
                                    <!-- Unread message count -->
                                    @if(isset($unreadCounts[$user->id]) && $unreadCounts[$user->id] > 0)
                                        <div style="position: absolute; top: -5px; right: -5px; background: #ef4444; color: white; border-radius: 50%; width: 18px; height: 18px; font-size: 10px; font-weight: bold; display: flex; align-items: center; justify-content: center; border: 2px solid white;">
                                            {{ $unreadCounts[$user->id] > 99 ? '99+' : $unreadCounts[$user->id] }}
                                        </div>
                                    @endif
                                </div>
                            <span
                                    style="margin-top: {{ $user->is_admin ? '12px' : '6px' }}; font-weight: {{ $user->is_admin ? '700' : '600' }}; color: {{ $user->is_admin ? '#92400e' : '#333' }}; text-align: center; font-size: 11px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 90px;">
                            {{ $user->name }}
                            @if($user->is_admin)
                                <div style="font-size: 8px; color: #92400e; font-weight: 500; margin-top: 1px;">
                                    Administrator
                                </div>
                            @endif
                        </span>
                        
                        <!-- Show user roles for admin -->
                        @if(is_admin())
                            @php
                                // Safely handle roles data - ensure it's an array
                                $userRoles = [];
                                if (isset($user->roles)) {
                                    if (is_array($user->roles)) {
                                        $userRoles = $user->roles;
                                    } elseif (is_string($user->roles)) {
                                        // Try to decode if it's a JSON string
                                        $decoded = json_decode($user->roles, true);
                                        $userRoles = is_array($decoded) ? $decoded : [$user->roles];
                                    }
                                }
                                $userRoles = array_filter($userRoles); // Remove empty values
                            @endphp
                            
                            @if(!empty($userRoles))
                                <div style="font-size: 8px; color: #6b7280; text-align: center; margin-top: 2px; max-width: 90px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ implode(', ', $userRoles) }}">
                                    {{ count($userRoles) > 1 ? '(' . count($userRoles) . ') roles' : (count($userRoles) == 1 ? $userRoles[0] : 'no role') }}
                                </div>
                            @else
                                <div style="font-size: 8px; color: #6b7280; text-align: center; margin-top: 2px; max-width: 90px;">
                                no role
                                </div>
                            @endif
                        @endif
                        </div>
                        @endif
                    @endforeach
                @else
                    <p style="text-align:center; font-size:12px; color:#999;">لا يوجد مستخدم بهذا الاسم</p>
                @endif
            </div>

            <!-- نافذة الدردشة -->
            @if($selectedUser)
                <div
                    style="width: 320px; height: 400px; background: white; border: 1px solid #e5e7eb; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); display: flex; flex-direction: column; color: #333; direction: rtl;">
                    <!-- رأس النافذة -->
                    <div
                        style="background: {{ $selectedUser->is_admin ? '#374151' : '#374151' }}; color: white; padding: 12px; font-weight: 600; display: flex; justify-content: space-between; align-items: center; border-top-left-radius: 12px; border-top-right-radius: 12px; direction: rtl;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="position: relative;">
                            <img
                                src="{{ $selectedUser->profile_photo_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($selectedUser->name) . '&background=fff&color=2563eb' }}"
                                alt="{{ $selectedUser->name }}"
                                style="width: 28px; height: 28px; border-radius: 50%; object-fit: cover; border: {{ $selectedUser->is_admin ? '2px solid white' : '1px solid white' }};">
                                <!-- Online status for selected user -->
                                @if($selectedUser->last_seen_at && \Carbon\Carbon::parse($selectedUser->last_seen_at)->gt(now()->subMinutes(5)))
                                    <div style="position: absolute; bottom: -2px; left: -2px; width: 8px; height: 8px; background: #22c55e; border: 1px solid white; border-radius: 50%;"></div>
                                @endif
                            </div>
                            <div>
                            <span>
                                
                                    Chat with {{ $selectedUser->name }}
                                
                            </span>
                                @if($selectedUser->last_seen_at && \Carbon\Carbon::parse($selectedUser->last_seen_at)->gt(now()->subMinutes(5)))
                                    <div style="font-size: 10px; opacity: 0.8;">Online now</div>
                                @else
                                    <div style="font-size: 10px; opacity: 0.8;">
                                        Last seen: {{ $selectedUser->last_seen_at ? $selectedUser->last_seen_at->diffForHumans() : 'Not specified' }}
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                        <button wire:click="deselectUser"
                                style="background: transparent; border: none; color: white; font-size: 20px; cursor: pointer; padding: 0 4px;">
                            ×
                        </button>
                        </div>
                    </div>

                    <!-- منطقة الرسائل -->
                    <div id="messagesContainer"
                         style="flex: 1; overflow-y: auto; padding: 12px; display: flex; flex-direction: column; gap: 12px; background: #f8fafc; direction: rtl;">
                        @if($selectedUser && isset($messages[$selectedUser->id]))
                            @foreach($messages[$selectedUser->id] as $message)
                                <div style="max-width: 80%; padding: 10px 14px; border-radius: 12px; word-break: break-word; direction: rtl;
                                    @if($message['from_user_id'] == auth()->id())
                                        background: #374151; color: white; align-self: flex-start; border-bottom-left-radius: 4px; margin-left: auto;
                                    @else
                                        background: white; border: 1px solid #e5e7eb; align-self: flex-end; border-bottom-right-radius: 4px; margin-right: auto;
                                    @endif">
                                    @if($message['content'])
                                        <div class="message-content" style="text-align: right;">{{ $message['content'] }}</div>
                                    @endif

                                    @if(isset($message['attachment_paths']) && $message['attachment_paths'])
                                        @foreach($message['attachment_paths'] as $attachmentPath)
                                            @if(Str::endsWith($attachmentPath, ['jpg', 'jpeg', 'png', 'gif', 'webp']))
                                                <div style="margin-top: 8px;">
                                                    <img src="{{ asset('storage/' . $attachmentPath) }}" 
                                                         style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                                                         onclick="window.open('{{ asset('storage/' . $attachmentPath) }}', '_blank')"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                                     <div style="display: none; background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                         <span style="color: #ef4444;">❌ لا يمكن تحميل الصورة</span><br>
                                                         <a href="{{ asset('storage/' . $attachmentPath) }}" 
                                                            target="_blank"
                                                            style="color: #374151; text-decoration: none;">
                                                             📎 اضغط للمحاولة مرة أخرى
                                                         </a>
                                                     </div>
                                                </div>
                                            @elseif(Str::endsWith($attachmentPath, ['pdf']))
                                                <div
                                                    style="background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                    <a href="{{ asset('storage/' . $attachmentPath) }}"
                                                       target="_blank"
                                                       class="attachment-link"
                                                       style="color: #374151; text-decoration: none; display: flex; align-items: center; gap: 6px; direction: rtl;">
                                                        📄 {{ basename($attachmentPath) }} (اضغط للعرض)
                                                    </a>
                                                </div>
                                            @else
                                                <div
                                                    style="background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                    <a href="{{ asset('storage/' . $attachmentPath) }}"
                                                       download
                                                       class="attachment-link"
                                                       style="color: #374151; text-decoration: none; display: flex; align-items: center; gap: 6px; direction: rtl;">
                                                        📎 {{ basename($attachmentPath) }} (اضغط للتحميل)
                                                    </a>
                                                </div>
                                            @endif
                                        @endforeach
                                    @endif

                                    <!-- Keep backward compatibility with single attachment -->
                                    @if(isset($message['attachment_path']) && $message['attachment_path'] && !isset($message['attachment_paths']))
                                        @if(Str::endsWith($message['attachment_path'], ['jpg', 'jpeg', 'png', 'gif', 'webp']))
                                            <div style="margin-top: 8px;">
                                                <img src="{{ asset('storage/' . $message['attachment_path']) }}" 
                                                     style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                                                     onclick="window.open('{{ asset('storage/' . $message['attachment_path']) }}', '_blank')"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                                 <div style="display: none; background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                     <span style="color: #ef4444;">❌ لا يمكن تحميل الصورة</span><br>
                                                     <a href="{{ asset('storage/' . $message['attachment_path']) }}" 
                                                        target="_blank"
                                                        style="color: #374151; text-decoration: none;">
                                                         📎 اضغط للمحاولة مرة أخرى
                                                     </a>
                                                 </div>
                                            </div>
                                        @elseif(Str::endsWith($message['attachment_path'], ['pdf']))
                                            <div
                                                style="background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                <a href="{{ asset('storage/' . $message['attachment_path']) }}"
                                                   target="_blank"
                                                   class="attachment-link"
                                                   style="color: #374151; text-decoration: none; display: flex; align-items: center; gap: 6px; direction: rtl;">
                                                    📄 {{ basename($message['attachment_path']) }} (اضغط للعرض)
                                                </a>
                                            </div>
                                        @else
                                            <div
                                                style="background: #f3f4f6; padding: 8px; border-radius: 8px; margin-top: 8px;">
                                                <a href="{{ asset('storage/' . $message['attachment_path']) }}"
                                                   download
                                                   class="attachment-link"
                                                   style="color: #374151; text-decoration: none; display: flex; align-items: center; gap: 6px; direction: rtl;">
                                                    📎 {{ basename($message['attachment_path']) }} (اضغط للتحميل)
                                                </a>
                                            </div>
                                        @endif
                                    @endif

                                    <div
                                        style="font-size: 10px; margin-top: 6px; opacity: 0.7; text-align: {{ $message['from_user_id'] == auth()->id() ? 'right' : 'left' }};">
                                        {{ \Carbon\Carbon::parse($message['created_at'])->format('h:i A') }}
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div
                                style="text-align: center; color: #6b7280; padding: 20px; flex: 1; display: flex; flex-direction: column; justify-content: center;">
                                <div style="font-size: 14px; margin-bottom: 8px;">لا توجد رسائل بعد</div>
                                <div style="font-size: 12px;">ابدأ محادثة جديدة مع {{ $selectedUser->name }}</div>
                            </div>
                        @endif
                    </div>

                    <!-- حقل إرسال الرسالة -->
                    <form wire:submit.prevent="sendMessage({{ $selectedUser->id }})"
                          style="display: flex; flex-direction: column; gap: 8px; border-top: 1px solid #e5e7eb; padding: 12px; background: white; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px; direction: rtl;">
                        <div style="display: flex; gap: 8px;">
                            <input type="text" wire:model.defer="newMessage" placeholder="Type a message or attach files..."
                                   id="messageInput"
                                   style="flex: 1; border: 1px solid #e5e7eb; border-radius: 8px; padding: 10px 12px; outline: none; transition: border 0.2s ease; text-align: right; direction: rtl;"
                                   onkeydown="if(event.key==='Enter' && !event.shiftKey){event.preventDefault(); this.form.dispatchEvent(new Event('submit', {bubbles: true}));}"
                                   onfocus="this.style.borderColor='#2563eb'" 
                                   onblur="this.style.borderColor='#e5e7eb'">
                            <button type="submit"
                                    style="background: #374151; color: white; border: none; border-radius: 8px; padding: 0 16px; cursor: pointer; transition: background 0.2s ease;"
                                    onmouseover="this.style.background='#1d4ed8'"
                                    onmouseout="this.style.background='#2563eb'">
                                Send
                            </button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; justify-content: flex-start;">
                            <label
                                style="cursor: pointer; padding: 6px 8px; border-radius: 6px; background: #f3f4f6; font-size: 12px; display: flex; align-items: center; gap: 4px;">
                                <input type="file" wire:model="attachments" multiple style="display: none;">
                                📎 Attach Files
                            </label>
                            @if($attachments)
                                <div style="font-size: 12px; color: #4b5563; display: flex; flex-wrap: wrap; gap: 4px;">
                                    @foreach($attachments as $index => $attachment)
                                        <div style="display: flex; align-items: center; gap: 4px; background: #e5e7eb; padding: 4px 8px; border-radius: 4px;">
                                            <span>{{ $attachment->getClientOriginalName() }}</span>
                                            <button type="button" wire:click="removeAttachment({{ $index }})"
                                                    style="color: #ef4444; background: none; border: none; cursor: pointer; font-size: 14px;">
                                                ×
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            @else
                <div
                    style="width: 320px; height: 400px; background: white; border: 1px solid #e5e7eb; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); display: flex; justify-content: center; align-items: center; color: #6b7280; direction: rtl;">
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 16px; margin-bottom: 8px;">👋 Hello!</div>
                        <div style="font-size: 14px;">Choose a user to start chatting</div>
                    </div>
                </div>
            @endif
        </div>
    @endif
</div>

<!-- Hidden audio element for notification sound -->
<audio id="notificationSound" preload="auto">
    <!-- Use a more reliable notification sound -->
    <!-- Use a working notification sound data URI -->
    <source src="data:audio/wav;base64,UklGRn4CAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YVoCAAC4uLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBAQEBAuLi4uLi4QEBA" type="audio/wav">
</audio>

<!-- Fallback: Generate beep sound programmatically -->
<script>
    // Create a smooth, pleasant notification sound using Web Audio API
    function createBeepSound() {
        try {
            // Create audio context - handle different browser prefixes
            let audioContext;
            if (window.AudioContext) {
                audioContext = new AudioContext();
            } else if (window.webkitAudioContext) {
                audioContext = new webkitAudioContext();
            } else {
                console.log('❌ Web Audio API not supported');
                return false;
            }
            
            // Resume audio context if it's suspended (required by some browsers)
            if (audioContext.state === 'suspended') {
                audioContext.resume().then(() => {
                    console.log('✅ Audio context resumed');
                }).catch(e => {
                    console.log('❌ Failed to resume audio context:', e);
                });
            }
            
            window.playBeepSound = function() {
                try {
                    if (audioContext.state === 'suspended') {
                        audioContext.resume();
                    }
                    
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.value = 800; // 800 Hz tone
                    oscillator.type = 'sine';
                    
                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.3);
                    
                    console.log('✅ Beep sound played successfully');
                    return true;
                } catch (e) {
                    console.log('❌ Beep sound failed:', e);
                    return false;
                }
            };

            // Create a smooth, pleasant two-tone notification chime
            window.createNotificationBeep = function(freq1 = 523, freq2 = 659, duration = 0.4) {
                try {
                    if (audioContext.state === 'suspended') {
                        audioContext.resume();
                    }
                    
                    // First tone (C5 - 523 Hz)
                    const oscillator1 = audioContext.createOscillator();
                    const gainNode1 = audioContext.createGain();
                    
                    oscillator1.connect(gainNode1);
                    gainNode1.connect(audioContext.destination);
                    
                    oscillator1.frequency.value = freq1;
                    oscillator1.type = 'sine';
                    
                    // Smooth fade in and out for first tone
                    gainNode1.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode1.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.05);
                    gainNode1.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 2);
                    
                    oscillator1.start(audioContext.currentTime);
                    oscillator1.stop(audioContext.currentTime + duration / 2);
                    
                    // Second tone (E5 - 659 Hz) with slight delay for harmony
                    setTimeout(() => {
                        try {
                            const oscillator2 = audioContext.createOscillator();
                            const gainNode2 = audioContext.createGain();
                            
                            oscillator2.connect(gainNode2);
                            gainNode2.connect(audioContext.destination);
                            
                            oscillator2.frequency.value = freq2;
                            oscillator2.type = 'sine';
                            
                            // Smooth fade in and out for second tone
                            gainNode2.gain.setValueAtTime(0, audioContext.currentTime);
                            gainNode2.gain.linearRampToValueAtTime(0.12, audioContext.currentTime + 0.05);
                            gainNode2.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 2);
                            
                            oscillator2.start(audioContext.currentTime);
                            oscillator2.stop(audioContext.currentTime + duration / 2);
                        } catch (e) {
                            console.log('❌ Second tone failed:', e);
                        }
                    }, 150); // 150ms delay between tones
                    
                    console.log('✅ Smooth notification chime played successfully');
                    return true;
                } catch (e) {
                    console.log('❌ Smooth notification chime failed:', e);
                    return false;
                }
            };

            // Create an alternative triple-tone notification (even smoother)
            window.createSmoothNotification = function() {
                try {
                    if (audioContext.state === 'suspended') {
                        audioContext.resume();
                    }
                    
                    const frequencies = [523, 659, 784]; // C5, E5, G5 (major chord)
                    const duration = 0.3;
                    
                    frequencies.forEach((freq, index) => {
                        setTimeout(() => {
                            try {
                                const oscillator = audioContext.createOscillator();
                                const gainNode = audioContext.createGain();
                                
                                oscillator.connect(gainNode);
                                gainNode.connect(audioContext.destination);
                                
                                oscillator.frequency.value = freq;
                                oscillator.type = 'sine';
                                
                                // Gentle envelope
                                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                                gainNode.gain.linearRampToValueAtTime(0.08, audioContext.currentTime + 0.03);
                                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
                                
                                oscillator.start(audioContext.currentTime);
                                oscillator.stop(audioContext.currentTime + duration);
                            } catch (e) {
                                console.log('❌ Triple tone failed:', e);
                            }
                        }, index * 100); // 100ms delay between each tone
                    });
                    
                    console.log('✅ Smooth triple-tone notification played successfully');
                    return true;
                } catch (e) {
                    console.log('❌ Smooth triple-tone notification failed:', e);
                    return false;
                }
            };
            
            console.log('✅ Enhanced Web Audio API notification sounds created');
            console.log('🎵 Available functions: playBeepSound, createNotificationBeep, createSmoothNotification');
            return true;
        } catch (e) {
            console.log('❌ Web Audio API not available:', e);
            return false;
        }
    }
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Detect and set document direction for proper positioning
        function detectAndSetDirection() {
            const htmlLang = document.documentElement.getAttribute('lang');
            const htmlDir = document.documentElement.getAttribute('dir');
            const bodyDir = document.body.getAttribute('dir');
            
            // Detect RTL languages
            const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
            const isRTLLang = htmlLang && rtlLanguages.some(lang => htmlLang.startsWith(lang));
            
            // Set direction based on language first, then dir attributes
            if (isRTLLang || htmlDir === 'rtl' || bodyDir === 'rtl') {
                document.documentElement.setAttribute('dir', 'rtl');
                if (!htmlLang || !htmlLang.startsWith('ar')) {
                    document.documentElement.setAttribute('lang', 'ar');
                }
                console.log('🔄 RTL direction applied - Widget will be on LEFT side');
            } else {
                document.documentElement.setAttribute('dir', 'ltr');
                if (!htmlLang || !htmlLang.startsWith('en')) {
                    document.documentElement.setAttribute('lang', 'en');
                }
                console.log('🔄 LTR direction applied - Widget will be on RIGHT side');
            }
        }
        
        // Initialize direction detection
        detectAndSetDirection();
        
        // Initialize audio for user interaction (required by most browsers)
        let audioInitialized = false;
        
        function initializeAudio() {
            if (!audioInitialized) {
                const audio = document.getElementById('notificationSound');
                
                // Try to load and prepare audio
                if (audio) {
                    audio.load();
                    audio.volume = 0.1; // Start quiet
                }
                
                // Initialize Web Audio API
                createBeepSound();
                
                audioInitialized = true;
                console.log('✅ Audio initialized after user interaction');
                
                // Test if functions are available
                console.log('🔍 Testing notification functions...');
                if (window.createSmoothNotification) {
                    console.log('✅ createSmoothNotification is available');
                } else {
                    console.log('❌ createSmoothNotification is NOT available');
                }
                if (window.createNotificationBeep) {
                    console.log('✅ createNotificationBeep is available');
                } else {
                    console.log('❌ createNotificationBeep is NOT available');
                }
                if (window.playBeepSound) {
                    console.log('✅ playBeepSound is available');
                } else {
                    console.log('❌ playBeepSound is NOT available');
                }
            }
        }
        
        // Initialize audio on first user interaction
        document.addEventListener('click', initializeAudio, { once: true });
        document.addEventListener('keydown', initializeAudio, { once: true });
        document.addEventListener('touchstart', initializeAudio, { once: true });
        
        // Also initialize Web Audio API immediately (will work in some browsers)
        createBeepSound();
        
        // Test audio permissions and request if needed
        if (window.Notification && Notification.permission === 'default') {
            console.log('🔔 Requesting notification permission...');
            Notification.requestPermission().then(permission => {
                console.log('🔔 Notification permission:', permission);
            });
        }
        
        // Scroll to bottom functionality
        window.addEventListener('scroll-to-bottom', () => {
            setTimeout(() => {
            const container = document.getElementById('messagesContainer');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
            }, 100);
        });
        
        // Enhanced notification sound with fallbacks
        window.addEventListener('play-notification-sound', () => {
            
            let soundPlayed = false;
            
            // Method 1: Try the new smooth triple-tone notification (most pleasant)
            if (window.createSmoothNotification && window.createSmoothNotification()) {
                console.log('✅ Smooth triple-tone notification played successfully');
                soundPlayed = true;
            }
            // Method 2: Try the two-tone chime notification
            else if (window.createNotificationBeep && window.createNotificationBeep(523, 659, 0.4)) {
                console.log('✅ Two-tone chime notification played successfully');
                soundPlayed = true;
            }
            // Method 3: Try single Web Audio API beep as fallback
            else if (window.playBeepSound && window.playBeepSound()) {
                console.log('✅ Single beep notification played successfully');
                soundPlayed = true;
            }
            // Method 4: Try HTML5 audio element
            else {
                const audio = document.getElementById('notificationSound');
                if (audio) {
                    audio.currentTime = 0;
                    audio.volume = 0.6;
                    
                    audio.play().then(() => {
                        console.log('✅ HTML5 audio notification played successfully');
                        soundPlayed = true;
                    }).catch(e => {
                        console.log('❌ HTML5 audio failed:', e);
                        soundPlayed = false;
                    });
                }
            }
            
            // Method 5: System notification as fallback
            if (!soundPlayed) {
                try {
                    if (window.Notification && Notification.permission === 'granted') {
                        new Notification('💬 رسالة جديدة', {
                            body: 'لديك رسالة جديدة في الدردشة',
                            icon: '/favicon.ico',
                            requireInteraction: false,
                            silent: false,
                            tag: 'chat-message'
                        });
                        console.log('✅ System notification shown with sound');
                        soundPlayed = true;
                    } else if (window.Notification && Notification.permission !== 'denied') {
                        Notification.requestPermission().then(permission => {
                            if (permission === 'granted') {
                                new Notification('💬 رسالة جديدة', {
                                    body: 'لديك رسالة جديدة في الدردشة',
                                    icon: '/favicon.ico',
                                    requireInteraction: false,
                                    silent: false,
                                    tag: 'chat-message'
                                });
                                console.log('✅ System notification shown after permission granted');
                            }
                        });
                    }
                } catch (e) {
                    console.log('❌ System notification failed:', e);
                }
            }
            
            // Method 6: Visual flash as last resort
            if (!soundPlayed) {
                console.log('🔔 Using visual flash as final fallback');
                const chatButton = document.querySelector('[wire\\:click="toggle"]');
                if (chatButton) {
                    const originalBg = chatButton.style.background;
                    chatButton.style.background = '#ef4444';
                    chatButton.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        chatButton.style.background = originalBg;
                        chatButton.style.transform = 'scale(1)';
                    }, 300);
                }
            }
        });

        // Close chat when clicking outside
        document.addEventListener('click', function (event) {
            const chatContainer = document.getElementById('chat-widget-container');
            if (!chatContainer.contains(event.target) && !event.target.closest('#chat-widget-container')) {
                if (chatContainer && window.Livewire) {
                    const componentId = chatContainer.getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component) {
                        component.set('open', false);
                    }
                }
            }
        });

        // Handle unread count updates with immediate UI refresh
        window.addEventListener('unread-counts-updated', (event) => {
            console.log('🔔 Unread counts updated:', event.detail);
            forceImmediateRefresh();
        });

        // Handle new unread count change events
        window.addEventListener('unread-count-changed', (event) => {
            console.log('📊 Unread count changed:', event.detail);
            forceImmediateRefresh();
            
            // Update UI immediately if widget is visible
            updateUnreadBadges(event.detail);
        });

        // Handle widget update required events
        window.addEventListener('widget-update-required', (event) => {
            console.log('⚡ Widget update required:', event.detail);
            forceImmediateRefresh();
        });

        // Handle unread counts refreshed events
        window.addEventListener('unread-counts-refreshed', (event) => {
            console.log('🔄 Unread counts refreshed:', event.detail);
            updateUnreadBadges(event.detail);
        });

        // Handle widget opened events
        window.addEventListener('widget-opened', (event) => {
            console.log('📂 Widget opened:', event.detail);
            
            // Clean up instant badges since widget is now open
            const instantBadges = document.querySelectorAll('.instant-unread-badge');
            instantBadges.forEach(badge => {
                badge.remove();
                console.log('🧹 Removed instant badge');
            });
            
            // Update with correct counts from server
            updateUnreadBadges(event.detail);
        });

        // Handle message sent success
        window.addEventListener('message-sent-success', (event) => {
            console.log('✅ Message sent successfully:', event.detail);
            
            // Clear input field immediately
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.value = '';
                console.log('✅ Input field cleared');
            }
            
            // Scroll to bottom
            setTimeout(() => {
                const container = document.getElementById('messagesContainer');
                if (container) {
                    container.scrollTop = container.scrollHeight;
                    console.log('📜 Scrolled to bottom after message sent');
                }
            }, 100);
        });

        // Enhanced component refresh events
        window.addEventListener('component-refreshed', (event) => {
            console.log('🔄 Component refreshed:', event.detail);
            
            if (event.detail && event.detail.unreadCounts) {
                updateUnreadBadges(event.detail);
            }
        });

        // Handle force refresh events
        window.addEventListener('force-component-refresh', () => {
            console.log('⚡ Force component refresh requested');
            forceImmediateRefresh();
        });

        // Function to force immediate refresh of the component
        function forceImmediateRefresh() {
            setTimeout(() => {
                const chatContainer = document.getElementById('chat-widget-container');
                if (chatContainer && window.Livewire) {
                    const componentId = chatContainer.getAttribute('wire:id');
                    const component = window.Livewire.find(componentId);
                    if (component) {
                        // Force component to re-render
                        component.call('$refresh');
                        
                        // Also call our custom refresh method
                        setTimeout(() => {
                            component.call('forceRefreshCounts');
                        }, 50);
                        
                        console.log('⚡ Immediate component refresh executed');
                    }
                }
            }, 10);
        }

        // Function to update unread badges in UI immediately
        function updateUnreadBadges(data) {
            try {
                const totalCount = data.totalCount || 0;
                
                // Update main chat button badge
                const mainButton = document.querySelector('.chat-main-button');
                if (mainButton) {
                    let badge = mainButton.querySelector('.unread-badge');
                    if (totalCount > 0) {
                        if (!badge) {
                            // Create badge if it doesn't exist
                            badge = document.createElement('div');
                            badge.className = 'unread-badge';
                            mainButton.appendChild(badge);
                        }
                        badge.textContent = totalCount > 99 ? '99+' : totalCount;
                        badge.style.display = 'flex';
                    } else if (badge) {
                        badge.style.display = 'none';
                    }
                }
                
                console.log('✅ UI badges updated for total count:', totalCount);
            } catch (e) {
                console.log('❌ Error updating badges:', e);
            }
        }
    });
</script>

<script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
<script>
    // Enable pusher logging - don't include this in production
    Pusher.logToConsole = true;

    var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
        // Use cluster from environment - required for proper app key routing
        cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
        // All communication still goes through api.pusherapp.com with cluster parameter
    });

    // Add connection debug info
    pusher.connection.bind('connected', function() {
        console.log('🔗 Pusher connected successfully');
    });

    pusher.connection.bind('error', function(err) {
        console.log('❌ Pusher connection error:', err);
    });

    // Chat channel for messages
    var channel = pusher.subscribe('chat');
    
    channel.bind('pusher:subscription_succeeded', function() {
        console.log('✅ Successfully subscribed to chat channel');
    });

    channel.bind('pusher:subscription_error', function(error) {
        console.log('❌ Failed to subscribe to chat channel:', error);
    });

    channel.bind('new-message', function (data) {
        console.log('🟢 Pusher received message:', data);
        
        // Check if message is for current user
        const currentUserId = {{ auth()->id() }};
        const isForCurrentUser = data.receiverId === currentUserId;
        
        // Get chat widget component
        const chatContainer = document.getElementById('chat-widget-container');
        if (chatContainer && window.Livewire) {
            const componentId = chatContainer.getAttribute('wire:id');
            const component = window.Livewire.find(componentId);
            if (component) {
                console.log('🎯 Calling handleNewMessage...');
                
                const payload = {
                    message_id: data.message_id,
                    senderId: data.senderId,
                    receiverId: data.receiverId,
                    message: data.message,
                    filePaths: data.filePaths,
                    created_at: new Date().toISOString(),
                };
                
                console.log('📦 Payload being sent:', payload);
                component.call('handleNewMessage', payload);
                console.log('✅ handleNewMessage call completed');
                
                // If message is for current user, update UI immediately
                if (isForCurrentUser) {
                    // Check if widget is closed to show instant badge
                    const isWidgetOpen = chatContainer && (
                        chatContainer.querySelector('[style*="width: 120px"]') !== null ||
                        chatContainer.querySelector('input[placeholder*="ابحث عن مستخدم"]') !== null ||
                        document.querySelector('#messagesContainer') !== null
                    );
                    
                    if (!isWidgetOpen) {
                        // Show instant unread badge
                        const mainChatButton = document.querySelector('[x-on\\:click="$wire.toggleWidget()"]');
                        if (mainChatButton) {
                            let existingBadge = mainChatButton.querySelector('.instant-unread-badge');
                            if (!existingBadge) {
                                existingBadge = document.createElement('div');
                                existingBadge.className = 'instant-unread-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold z-50';
                                existingBadge.textContent = '1';
                                mainChatButton.appendChild(existingBadge);
                            } else {
                                const currentCount = parseInt(existingBadge.textContent) || 0;
                                existingBadge.textContent = currentCount + 1;
                            }
                            
                            // Add animation
                            existingBadge.style.transform = 'scale(1.2)';
                            setTimeout(() => {
                                existingBadge.style.transform = 'scale(1)';
                            }, 200);
                        }
                    }
                    
                    // Trigger sound notification
                    window.dispatchEvent(new CustomEvent('play-notification-sound'));
                }
            } else {
                console.log('❌ Component not found with ID:', componentId);
            }
        } else {
            console.log('❌ Chat container or Livewire not found');
        }
    });
    
    // User status channel for online/offline updates
    var statusChannel = pusher.subscribe('user-status');
    
    statusChannel.bind('user-online-status-changed', function(data) {
        console.log('👤 User status changed:', data);
        // Handle user online/offline status updates here if needed
    });

    // DOM Content Loaded Event Handler
    document.addEventListener('DOMContentLoaded', () => {
        // Helper function to get our ChatWidget component
        window.getChatWidgetComponent = function() {
            const chatContainer = document.getElementById('chat-widget-container');
            if (chatContainer && window.Livewire) {
                const componentId = chatContainer.getAttribute('wire:id');
                const component = window.Livewire.find(componentId);
                if (component) {
                    console.log('✅ Found ChatWidget component:', component);
                    return component;
                } else {
                    console.log('❌ Could not find component with ID:', componentId);
                }
            } else {
                console.log('❌ Chat container or Livewire not found');
            }
            return null;
        };

        // Test functions for debugging
        window.testLivewireMessage = function() {
            console.log('🧪 Testing Livewire message delivery...');
            
            const testPayload = {
                message_id: 888888,
                senderId: 1, // Change this to a valid user ID
                receiverId: 2, // Change this to current user ID
                message: 'Test message from console - ' + new Date().toLocaleTimeString(),
                filePath: null
            };
            
            console.log('🧪 Test payload:', testPayload);
            
            const chatContainer = document.getElementById('chat-widget-container');
            if (chatContainer && window.Livewire) {
                const componentId = chatContainer.getAttribute('wire:id');
                const component = window.Livewire.find(componentId);
                if (component) {
                    component.call('handleNewMessage', testPayload);
                    console.log('✅ component.call called');
                }
            }
        };

        window.testAudioNotification = function() {
            console.log('🔊 Testing audio notification...');
            window.dispatchEvent(new CustomEvent('play-notification-sound'));
        };

        // Test functions for different notification sounds
        window.testSmoothNotification = function() {
            console.log('🎵 Testing smooth triple-tone notification...');
            if (window.createSmoothNotification) {
                window.createSmoothNotification();
            } else {
                console.log('❌ Smooth notification not available');
            }
        };

        window.testChimeNotification = function() {
            console.log('🔔 Testing two-tone chime notification...');
            if (window.createNotificationBeep) {
                window.createNotificationBeep(523, 659, 0.4);
            } else {
                console.log('❌ Chime notification not available');
            }
        };

        window.testBeepNotification = function() {
            console.log('📢 Testing simple beep notification...');
            if (window.playBeepSound) {
                window.playBeepSound();
            } else {
                console.log('❌ Beep notification not available');
            }
        };

        console.log('🧪 Added window.testLivewireMessage() function. Call it from console to test!');
        console.log('🔊 Added window.testAudioNotification() function to test audio!');
        console.log('🎵 Added window.testSmoothNotification() to test the smooth sound!');
        console.log('🔔 Added window.testChimeNotification() to test the chime sound!');
        console.log('📢 Added window.testBeepNotification() to test the simple beep!');
    });
</script>

<!-- Enhanced Arabic RTL Chat Widget -->
<style>
    #chat-widget-container {
        font-family: 'Segoe UI', 'Cairo', 'Amiri', 'Noto Sans Arabic', Tahoma, Geneva, Verdana, sans-serif !important;
        position: fixed !important;
        bottom: 0px !important;
        right: 0px !important;
        z-index: 9999 !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-end !important;
        gap: 10px !important;
        direction: rtl !important;
    }
    
    /* For Arabic/RTL - position on the left side to avoid right sidebar */
    html[lang^="ar"] #chat-widget-container,
    html[dir="rtl"] #chat-widget-container,
    body[dir="rtl"] #chat-widget-container {
        right: auto !important;
        left: 0px !important;
        align-items: flex-start !important;
        direction: rtl !important;
    }
    
    /* For English/LTR - position on the right side to avoid left sidebar */
    html[lang^="en"] #chat-widget-container,
    html[dir="ltr"] #chat-widget-container,
    body[dir="ltr"] #chat-widget-container {
        left: auto !important;
        right: 0px !important;
        align-items: flex-end !important;
        direction: ltr !important;
    }
    
    /* Chat button styling */
    #chat-widget-container .chat-main-button {
        background: #2563eb !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 60px !important;
        height: 60px !important;
        font-size: 24px !important;
        cursor: pointer !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    #chat-widget-container .chat-main-button:hover {
        background: #1d4ed8 !important;
        transform: scale(1.05) !important;
    }
    
    /* Badge positioning - always top-left for visibility */
    #chat-widget-container .unread-badge {
        position: absolute !important;
        top: -8px !important;
        left: -8px !important;
        background: #ef4444 !important;
        color: white !important;
        border-radius: 50% !important;
        width: 20px !important;
        height: 20px !important;
        font-size: 11px !important;
        font-weight: bold !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border: 2px solid white !important;
        direction: ltr !important;
    }
    
    /* Mobile responsive - move up to avoid bottom navigation */
    @media (max-width: 768px) {
        #chat-widget-container {
            bottom: 60px !important;
        }
    }
    
    /* Input fields styling */
    #chat-widget-container input[type="text"] {
        text-align: right !important;
        direction: rtl !important;
    }
    
    html[lang^="en"] #chat-widget-container input[type="text"],
    html[dir="ltr"] #chat-widget-container input[type="text"],
    body[dir="ltr"] #chat-widget-container input[type="text"] {
        text-align: left !important;
        direction: ltr !important;
    }
    
    #chat-widget-container .message-content {
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: 1.4;
    }
    
    /* File attachments */
    #chat-widget-container .attachment-link {
        direction: rtl !important;
        text-align: right !important;
    }
    
    html[lang^="en"] #chat-widget-container .attachment-link,
    html[dir="ltr"] #chat-widget-container .attachment-link,
    body[dir="ltr"] #chat-widget-container .attachment-link {
        direction: ltr !important;
        text-align: left !important;
    }
    
    /* Smooth transitions */
    #chat-widget-container * {
        transition: all 0.2s ease;
    }
</style>



