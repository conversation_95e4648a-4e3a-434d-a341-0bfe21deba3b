    <!-- Dropdown Panel -->
    <div id="notification-dropdown" 
         class="absolute top-full right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50 notification-dropdown hidden"
         style="direction: rtl; min-width: 400px; max-width: 500px; height: 480px;">
         
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 rounded-t-lg flex-shrink-0">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    @if($unreadCount > 0)
                        <button 
                            wire:click="markAllAsRead"
                            class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                            وضع علامة على الكل كمقروء
                        </button>
                        <span class="text-gray-300 dark:text-gray-500">|</span>
                    @endif
                    <button 
                        wire:click="deleteAllRead"
                        class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium transition-colors">
                        حذف المقروء
                    </button>
                </div>
            </div>
            
            @if($unreadCount > 0)
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    لديك {{ $unreadCount }} إشعار غير مقروء
                </p>
            @endif
        </div>
        
        <div class="flex flex-col" style="height: 400px;">
            <!-- Scrollable notifications area with fixed height -->
            <div class="flex-1 overflow-y-auto notification-list" style="max-height: 320px; min-height: 320px;">
                @if(count($recentNotifications) > 0)
                    @foreach($recentNotifications as $notification)
                        <div class="notification-item px-4 py-3 border-b border-gray-100 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors 
                                    {{ is_null($notification['read_at']) ? 'unread bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900 dark:to-blue-800 shadow-sm' : '' }}"
                             wire:key="notification-{{ $notification['id'] }}">
                            <div class="flex items-start justify-between">
                                <!-- Notification Content -->
                                <div class="flex-1 cursor-pointer" wire:click="openNotification({{ $notification['id'] }})">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <!-- Icon -->
                                        <span class="text-lg">
                                            {{ $notification['icon'] ?? '🔔' }}
                                        </span>
                                        
                                        <!-- Title -->
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate {{ is_null($notification['read_at']) ? 'font-bold text-blue-900 dark:text-blue-100' : '' }}">
                                            {{ $notification['title'] }}
                                        </p>
                                        
                                        <!-- Unread Indicator -->
                                        @if(is_null($notification['read_at']))
                                            <span class="w-3 h-3 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse shadow-md"></span>
                                        @endif
                                    </div>
                                    
                                    <!-- Message -->
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2 {{ is_null($notification['read_at']) ? 'text-gray-700 dark:text-gray-200 font-medium' : '' }}">
                                        {{ $notification['message'] }}
                                    </p>
                                    
                                    <!-- Meta Information -->
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ \Carbon\Carbon::parse($notification['created_at'])->diffForHumans() }}
                                        </span>
                                        @if(isset($notification['from_user']) && $notification['from_user'])
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                من: {{ $notification['from_user']['name'] ?? 'النظام' }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-1 space-x-reverse ml-2">
                                    @if(is_null($notification['read_at']))
                                        <button 
                                            wire:click.stop="markAsRead({{ $notification['id'] }})"
                                            class="p-1 text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                                            title="وضع علامة كمقروء">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </button>
                                    @else
                                        <button 
                                            wire:click.stop="markAsUnread({{ $notification['id'] }})"
                                            class="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                            title="وضع علامة كغير مقروء">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    @endif
                                    
                                    <button 
                                        wire:click.stop="deleteNotification({{ $notification['id'] }})"
                                        class="p-1 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                                        title="حذف الإشعار">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Empty State -->
                    <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                        <svg class="w-12 h-12 mx-auto text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12h0a3 3 0 0 0-6 0v12h5z M12 2a3 3 0 0 1 3 3v6l2 2v1H7v-1l2-2V5a3 3 0 0 1 3-3z"></path>
                        </svg>
                        <p class="text-sm">No notifications</p>
                    </div>
                @endif
            </div>
            
            <!-- Footer - Always visible outside the scrollable area -->
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg flex-shrink-0">
                <button 
                    wire:click="viewAllNotifications"
                    onclick="console.log('View all notifications clicked!'); $wire.viewAllNotifications();"
                    class="w-full text-center text-sm text-gray-900 dark:text-gray-100 hover:text-blue-800 dark:hover:text-blue-300 font-bold transition-all duration-200 py-3 px-4 rounded-md border border-transparent hover:border-blue-200 dark:hover:border-blue-600 cursor-pointer">
                    📋 View all notifications →
                </button>
            </div>
        </div>
    </div> 