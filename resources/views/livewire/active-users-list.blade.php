<div>
    <!-- Users List Header -->
    <div class="w-[430px] bg-white border-b" style="margin: 5px">
        <!-- Search Bar -->
        <div class="px-4 py-3 border-b">
            <div class="relative">
                <input type="text" 
                       wire:model="search"
                       placeholder="Search" 
                       class="w-full pl-10 pr-4 py-2 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
        </div>

        <!-- Users Slider -->
        <div class="relative px-2 py-3">
            <div class="flex space-x-2 overflow-x-auto scrollbar-hide px-2">
                <!-- Active Users -->
                @foreach($users as $user)
                    <div class="flex-none cursor-pointer" style="width: 100px;" wire:click="startChat({{ $user['id'] }})">
                        <div class="relative">
                            <div class="w-14 h-14 rounded-full flex items-center justify-center relative">
                                @if(isset($user['avatar']))
                                    <img src="{{ $user['avatar'] }}" alt="{{ $user['name'] }}" class="w-14 h-14 rounded-full object-cover">
                                @else
                                    <div class="w-14 h-14 rounded-full bg-{{ substr($user['name'], 0, 2) === 'AD' ? 'orange' : 'primary' }}-500 flex items-center justify-center text-white text-lg font-semibold">
                                        {{ substr($user['name'], 0, 2) }}
                                    </div>
                                @endif
                                @if($user['is_online'])
                                    <div class="absolute bottom-0 right-0 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-white"></div>
                                @endif
                            </div>
                            <div class="mt-1 text-center">
                                <span class="text-xs font-medium truncate block w-14">{{ $user['name'] }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <style>
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</div> 