@php
    $brand = $reservation->brand
@endphp

<div class="container-flud"
    @if($clientReview)
        style="pointer-events: none"
    @endif
>
    <div class="card text-center">
        @if($brand)
            <img
                src="{{ Storage::url($brand->logo) }}"
                class="logo wow fadeInDown"
                data-wow-duration="1s"
                alt="4 Seasons Logo"
            />
        @endif
{{--        <div class="text-start wow slideInRight" data-wow-duration="1s">--}}
{{--            <div class="driver-name">{{ $driverName }}</div>--}}
{{--            <div class="text-muted">{{ $driverNumber }}</div>--}}
{{--        </div>--}}
        <p class="mt-3 text-start wow slideInRight" data-wow-duration="2s">
            نحن نقدر رأيك في سائقينا. شارك تجربتك وساعد في تحسين خدمتنا!!
        </p>
        <div class="text-start wow slideInRight" data-wow-duration="3s">
            <h5 class="mt-4 mb-2">التقييم:</h5>
            <div class="rating-container">
                <div class="rating-stars">
                    <input type="radio" id="star5" name="rating" value="5" wire:click="setRating(5)" {{ $rating == 5 ? 'checked' : '' }} />
                    <label for="star5" title="5 نجوم"></label>
                    <input type="radio" id="star4" name="rating" value="4" wire:click="setRating(4)" {{ $rating == 4 ? 'checked' : '' }} />
                    <label for="star4" title="4 نجوم"></label>
                    <input type="radio" id="star3" name="rating" value="3" wire:click="setRating(3)" {{ $rating == 3 ? 'checked' : '' }} />
                    <label for="star3" title="3 نجوم"></label>
                    <input type="radio" id="star2" name="rating" value="2" wire:click="setRating(2)" {{ $rating == 2 ? 'checked' : '' }} />
                    <label for="star2" title="2 نجوم"></label>
                    <input type="radio" id="star1" name="rating" value="1" wire:click="setRating(1)" {{ $rating == 1 ? 'checked' : '' }} />
                    <label for="star1" title="نجمة واحدة"></label>
                </div>
            </div>
        </div>

        <div class="grid-questions text-start mt-4" id="questions">
            @foreach($questions as $key => $question)
                <div class="wow slideInRight" data-wow-duration="3s">
                    <label class="form-label">{{ $question }}</label><br />
                    <button class="btn btn-choice btn-yes {{ isset($answers[$key]) && $answers[$key] === 'yes' ? 'active' : '' }}"
                            wire:click="setAnswer('{{ $key }}', 'yes')">
                        نعم
                    </button>
                    <button class="btn btn-choice btn-no {{ isset($answers[$key]) && $answers[$key] === 'no' ? 'active' : '' }}"
                            wire:click="setAnswer('{{ $key }}', 'no')">
                        لا
                    </button>
                </div>
            @endforeach
        </div>

        <div class="mt-4 text-start">
            <h6>اضافة تعليق</h6>
            <textarea
                wire:model="comment"
                class="form-control mb-3"
                rows="3"
                placeholder="اكتب تعليق"
            ></textarea>

            <div class="suggested-tags">
                @foreach($suggestedTags as $tag)
                    <button class="btn btn-outline-secondary {{ in_array($tag, $tags) ? 'active' : '' }}" wire:click="addTag('{{ $tag }}')">
                        {{ $tag }}
                    </button>
                @endforeach
            </div>

        </div>

        @if($showError)
            <div class="alert alert-danger mt-3">
                {{ $errorMessage }}
            </div>
        @endif

        @if($showConfirmation)
            <div class="alert alert-success mt-3">
                تم إرسال تقييمك بنجاح! شكراً لك.
            </div>
        @endif

        @if(!$this->clientReview)
            <button class="btn-choice btn btn-success mt-4" wire:click="submitReview">
                إرسال
            </button>
        @endif
    </div>
</div>
