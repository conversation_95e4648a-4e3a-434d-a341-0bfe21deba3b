<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
  <!-- Meta Tags and Character Encoding -->
  <meta charset="utf-8" />
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />

  <!-- Stylesheets -->
  <link href="/trip-pdfs/amazon/assets/style/style.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="/trip-pdfs/amazon/assets/style/final_style_PRINT_FIXED.css" />

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect" />
  <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&amp;display=swap"
    rel="stylesheet" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Smooch&display=swap" rel="stylesheet">



  <!-- Page Title -->
  <title>Amazons</title>
</head>

<body>
  <main>
    <section>
      <div class="hero-banner">
        <!-- شعار 4 Seasons -->
        <div class="logo">
          <div class="logo-text">
              <img src="/trip-pdfs/amazon/assets/image/image 3.png" alt="" style="width: 80%;">
          </div>
        </div>

        <div class="hero-content">

          <!-- الجانب الأيسر - المحتوى العربي -->
          <div class="left-section">
            <!-- الشريط البنفسجي مع الزر -->
            <div class="purple-strip">
              <h3 class="program-title">برنامج جورجيا السياحي</h3>
            </div>

            <!-- زر المدة -->
            <button class="duration-btn">
              6 أيام - 5 ليالي <span class="golden-text">الذهبي</span>
            </button>
          </div>

          <!-- الجانب الأيمن - النص الإنجليزي -->
          <div class="right-section">
            <h1 class="magic-text">
              <h2 class="georgia-text"></h2>

            </h1>
          </div>
        </div>
      </div>
    </section>
    <!-- Package Inclusions Section -->
    <section>
      <div class="container-fluid">
        <div class="content-wrapper">
          <!-- Decorative Element -->
          <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_1"
            style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

          <!-- Section Header -->
          <div class="program-header">
            <h1 class="program-title">البرنامج يشمل الآتي</h1>
          </div>

          <!-- Features Grid - What's Included -->
          <div class="feature-grid">
            <!-- Airport Transfer -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="استقبال" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/reception_icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                الاستقبال و التوديع<br />
                من و إلى المطار
              </div>
            </div>

            <!-- Hotel Reservations -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="فندق" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/hotelreservations_icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                حجوزات الفنادق مع الافطار لمدة 5 ليالي:<br />
                2 ليلة تبليسي، 2 ليلة باتومي، 1 تبليسي
              </div>
            </div>

            <!-- Transportation -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="سيارة" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/transfers_by_car.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                جميع التنقلات بسيارة<br />
                سياحية خاصة
              </div>
            </div>

            <!-- Internet SIM -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="انترنت" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/Internet_SIM_icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                شريحة انترنت<br />
                مفتوح لكل فرد
              </div>
            </div>

            <!-- Travel Insurance -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="تأمين" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/International_icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">تأمين السفر الدولي</div>
            </div>

            <!-- Customer Service -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="خدمة" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/customer_service _icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                خدمة عملاء باللغة<br />
                العربية 24 ساعة
              </div>
            </div>

            <!-- All Taxes Included -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="سعر" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/price_includes _icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">السعر شامل كافة الضرائب</div>
            </div>

            <!-- Entrance Tickets -->
            <div class="feature-item">
              <div class="icon-circle">
                <img alt="تذاكر" class="icon-img" src="/trip-pdfs/amazon/assets/image/icon/entrance_tickets _icon.svg"
                  style="filter: invert(100%) sepia(1%) saturate(2%) hue-rotate(319deg) brightness(116%) contrast(100%);" />
              </div>
              <div class="feature-text">
                تذاكر دخول الأماكن السياحية<br />
                (تليفريك العاصمة تبليسي،<br />
                حديقة تبليسي المائية)
              </div>
            </div>
          </div>

          <!-- What's Not Included Section -->
            @if($tripPdf && $tripPdf->not_contains && is_array($tripPdf->not_contains) && count($tripPdf->not_contains) > 0)
                <!-- What's Not Included Section -->
                <div class="text-start mb-3">
                    <h2 class="secondary-title">البرنامج لا يشمل الآتي</h2>
                </div>
                <div class="container-fluid">

                    <div class="orange-box">
                        <ul class="feature-list">
                            @foreach($tripPdf->not_contains as $notContain)
                                <li>{{ $notContain['not_contains'] }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            @endif
        </div>
      </div>
    </section>

      @if($tripPdf && $tripPdf->tours->count())
          @php
              $order = [
                  'اليوم الاول',
                  'اليوم الثاني',
                  'اليوم الثالث',
                  'اليوم الرابع',
                  'اليوم الخامس',
                  'اليوم السادس',
                  'اليوم السابع',
                  'اليوم الثامن',
                  'اليوم التاسع',
                  'اليوم العاشر',
                  'اليوم الحادي عشر',
                  'اليوم الثاني عشر',
                  'اليوم الثالث عشر',
                  'اليوم الرابع عشر',
                  'اليوم الخامس عشر',
                  'اليوم السادس عشر',
                  'اليوم السابع عشر',
                  'اليوم الثامن عشر',
                  'اليوم التاسع عشر',
                  'اليوم العشرون',
                  'اليوم الحادي والعشرون',
                  'اليوم الثاني والعشرون',
                  'اليوم الثالث والعشرون',
                  'اليوم الرابع والعشرون',
                  'اليوم الخامس والعشرون',
                  'اليوم السادس والعشرون',
                  'اليوم السابع والعشرون',
                  'اليوم الثامن والعشرون',
                  'اليوم التاسع والعشرون',
                  'اليوم الثلاثون',

];
          @endphp
          @foreach($tripPdf->tours as $index => $tour)
              <!-- Day One Itinerary Section -->
              <section>
                  <div class="container position-relative">
                      <div class="row container-flex">
                          <div class="col-7">
                              <h2 class="headline">
                                  {{ $order[$index] }} : {{ $tour->tour?->name }}
                              </h2>
                              <p class="tour-description">
                                  {{ $tour->tour?->description }}
                              </p>
                          </div>
                          <div class="col-5">
                              <div class="image-container">
                                  <!-- Decorative Frames -->
                                  <div class="orange-frame top-frame"></div>
                                  <div class="orange-frame bottom-frame"></div>
                                  <div class="image-wrapper">
                                      <img alt="صورة جبال القوقاز وقصر أنانوري" src="{{ $tour->tour?->fileUrl('image') }}"
                                           style="width: 100%; height: auto" />
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
          @endforeach
      @endif

    <!-- General Information About Georgia Section -->
    <section>
      <div class="container">
        <div class="page">
          <!-- Decorative Element -->
          <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_2"
            style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

          <h1 class="page-title">معلومات عامة عن جورجيا</h1>
          <p class="introduction">
            تقع جورجيا عند تقاطع أوروبا وآسيا، مما جعلها غنية بالثقافات والتاريخ
            العريق. تتميز بمناظرها الطبيعية المتنوعة، من جبال القوقاز المغطاة
            بالثلوج شتاءً إلى الوديان الخضراء والأنهار المتدفقة. توفر العاصمة
            تبليسي تجربة سياحية مميزة تجمع بين المباني التاريخية والتصاميم
            العصرية.
          </p>

          <!-- Key Facts Grid -->
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">العاصمة:</span>
              تبليسي، وهي أكبر مدينة في البلاد
            </div>
            <div class="info-item">
              <span class="info-label">المساحة:</span>
              حوالي 69,700 كيلومتر مربع
            </div>
            <div class="info-item">
              <span class="info-label">عدد السكان:</span>
              حوالي 3.7 مليون نسمة
            </div>
            <div class="info-item">
              <span class="info-label">اللغة الرسمية:</span>
              الجورجية
            </div>
            <div class="info-item">
              <span class="info-label">العملة:</span>
              لاري جورجي (GEL)
            </div>
            <div class="info-item">
              <span class="info-label">نظام الحكم:</span>
              جمهورية ديمقراطية برلمانية
            </div>
          </div>

          <!-- Climate Information -->
          <div class="climate">
            <strong>المناخ:</strong>
            يتميز طقس جورجيا بتنوع بين الصيف والشتاء. في الصيف، يكون الجو معتدل
            إلى دافئ، حيث تتراوح درجات الحرارة بين 25 - 35 °م. أما في الشتاء،
            يكون باردًا في الجبال وتنخفض الحرارة إلى ما دون الصفر، بينما تكون
            المناطق الساحلية أكثر اعتدالاً مع أمطار غزيرة.
          </div>
        </div>
      </div>
    </section>

    <!-- Distances Between Cities Section -->
    <section>
      <div class="container">
        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image.svg" alt="" class="airplane-icon_3"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">المسافات بين المدن بالسيارة</h1>
        </div>

        <!-- Grid of Distance Images -->
        <div class="grid-container mt-4">
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 14.svg" />
          </div>
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 15.svg" />
          </div>
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 16.svg" />
          </div>
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 17.svg" />
          </div>
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 18.svg" />
          </div>
          <div class="distances">
            <img alt="" src="/trip-pdfs/amazon/assets/image/icon/Group 19.svg" />
          </div>
        </div>
      </div>
    </section>

    <!-- Restaurants in Tbilisi Section -->
    <section>
      <div class="container">

        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_4"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">المطاعم</h1>
        </div>

        <!-- Tbilisi Restaurants -->
        <h2 class="section-title">تبليسي</h2>
        <div style="margin-right: 2rem">
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم بيروت باشا</span>
            <span class="restaurant-rating"> (4.1):</span>
            <span class="restaurant-desc">يقدم المأكولات اللبنانية الأصيلة</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم السلطان سوفراسي</span>
            <span class="restaurant-rating"> (3.4):</span>
            <span class="restaurant-desc">مطعم تركي، يقدم مأكولات حلال متنوعة</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم Mr. Kebab</span>
            <span class="restaurant-rating"> (4.8):</span>
            <span class="restaurant-desc">مطعم إيراني، يقدم مجموعة متنوعة من الكباب</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم أنقرة</span>
            <span class="restaurant-rating"> (4):</span>
            <span class="restaurant-desc">مطعم تركي، يقدم مأكولات حلال متنوعة</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم نسيم التركي</span>
            <span class="restaurant-rating"> (4.1):</span>
            <span class="restaurant-desc">مطعم تركي، يقدم مأكولات حلال متنوعة</span>
          </div>
        </div>

        <!-- Batumi Restaurants -->
        <h2 class="section-title mt-3">باتومي</h2>
        <div style="margin-right: 2rem">
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم مكة</span>
            <span class="restaurant-rating"> (4):</span>
            <span class="restaurant-desc">يديره مالك سعودي، ويقدم مأكولات سعودية شهيرة</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم خراباك</span>
            <span class="restaurant-rating"> (4.1):</span>
            <span class="restaurant-desc">مطعم تركي، يقدم مأكولات حلال متنوعة</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم SpiceGarden</span>
            <span class="restaurant-rating"> (4.5):</span>
            <span class="restaurant-desc">مطعم هندي، يقدم مجموعة متنوعة من الطعام الحلال</span>
          </div>
        </div>
      </div>
    </section>

    <!-- More Restaurants Section -->
    <section>
      <div class="container">
        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_5"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">المطاعم</h1>
        </div>

        <!-- Bakuriani Restaurants -->
        <h2 class="section-title">باكورياني</h2>
        <div style="margin-right: 2rem">
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم أوشبا </span>
            <span class="restaurant-rating"> ( 4 ) :</span>
            <span class="restaurant-desc">عد خيارًا جيدًا لتجربة الطعام الجورجي التقليدي الحلال</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم ميمينو </span>
            <span class="restaurant-rating"> ( 4.3 ):</span>
            <span class="restaurant-desc">مطعم جورجي تقليدي يقدم مجموعة متنوعة من الأطباق</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">كافي اسبن </span>
            <span class="restaurant-rating"> ( 4 ):</span>
            <span class="restaurant-desc">يوفر تجربة حلال مميزة مع قائمة طعام متنوعة تناسب الزوار
              المسلمين</span>
          </div>
        </div>

        <!-- Kutaisi Restaurants -->
        <h2 class="section-title mt-3">كوتايسي</h2>
        <div style="margin-right: 2rem">
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم سمسم </span>
            <span class="restaurant-rating"> ( 4.7 ):</span>
            <span class="restaurant-desc">يعد من المطاعم الراقية في كوتايسي، ويقدم الأطباق الأوروبية</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم بلاتي </span>
            <span class="restaurant-rating"> ( 4.6 ):</span>
            <span class="restaurant-desc">يقدم مجموعة متنوعة من الأطباق الجورجية التقليدية في أجواء مريحة،
              مع خيارات حلال</span>
          </div>
          <div class="restaurant-item">
            <span class="restaurant-name">مطعم The Biryani House </span>
            <span class="restaurant-rating"> (4):</span>
            <span class="restaurant-desc">مطعم يقدم مجموعة متنوعة من الطعام الحلال.</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Important Notes Section -->
    <section>
      <div class=" container">
        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_6"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">ملاحظات هامة</h1>
        </div>

        <!-- Cancellation Policy -->
        <div class="cancellation_policy">
          <h2 class="section-title">سياسة الإلغاء</h2>
          <span class="info-list">
            <li>البرنامج غير مسترد</li>
          </span>
        </div>

        <!-- Hotel Information -->
        <h2 class="section-title-hotail mt-1">الفنادق</h2>
        <div class="contat-section-title_h">
          <ul class="info-list">
            <li>
              الإفطار يكون يوميًا بالفندق من الساعة 7 صباحًا إلى 10 صباحًا
            </li>
            <li>
              تسجيل الدخول بالفندق يكون بعد الساعة 3 - 2 الظهر وتسجيل الخروج
              يكون قبل الساعة 12 الظهر
            </li>
            <li>
              في حال الرغبة بالاستمتاع بخدمات الفندق مثل (المسبح العام ، المطعم)
              أو وجود أي أعطال
            </li>
            <li>في حالة عدم توفر غرفة يتم التواصل مع الاستقبال مباشرة</li>
            <li>
              في حال التأخر عن تسجيل الخروج بالوقت المحدد، يتم تحصيل المسؤولية
              الخاصة عن أي تكاليف
            </li>
            <li>إضافية يفرضها الفندق</li>
          </ul>
        </div>

        <!-- Tour Information -->
        <h2 class="section-title_gaolh">الجولات السياحية</h2>
        <div class="contat-section-title_h">
          <ul class="info-list">
            <li>جميع الجولات المذكورة هي على سبيل المثال وليس الحصر</li>
            <li>
              في حال كانت إحدى الأماكن السياحية غير متوفرة لأسباب خارجية مثل
              الأعياد الرسمية او اعمال
            </li>
            <li>الصيانة سيقوم السائق بالتوجه في جولة سياحية بديلة</li>
            <li>حال الحاجة لطلبات خارجية أو شخصية يتم التنسيق مع السائق</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Travel Requirements Section -->
    <section>
      <div class="container">
        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_7"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">ملاحظات هامة</h1>
        </div>

        <!-- Gulf Citizens Travel Requirements -->
        <div class="cancellation_policy">
          <h2 class="section-title mt-1">
            <span><img alt="" src="/trip-pdfs/amazon/assets/image/icon/worlad_plan.svg" style="width: 10%" /></span>
            اجراءات السفر (جنسية خليجية) :
          </h2>
          <span class="info-list_1">
            <li>يجب أن تكون صلاحية جواز السفر لا تقل عن 6 أشهر</li>
          </span>
        </div>

        <!-- Resident Travel Requirements -->
        <h2 class="section-title-hotail mt-3">
          <span><img alt="" src="/trip-pdfs/amazon/assets/image/icon/worlad_plan.svg" style="width: 10%" /></span>
          اجراءات السفر (مقيم) :
        </h2>
        <div class="contat-section-title">
          <ul class="info-list">
            <li>يجب أن تكون صلاحية جواز السفر لا تقل عن 6 أشهر.</li>
            <li>يجب أن تكون صلاحية الإقامة لا تقل عن 3 أشهر.</li>
            <li>ترجمة الإقامة لدى مكتب ترجمة معتمد</li>
          </ul>
        </div>

        <!-- Other Nationalities Travel Requirements -->
        <h2 class="section-title_gaolh mt-2">
          <span><img alt="" src="/trip-pdfs/amazon/assets/image/icon/worlad_plan.svg" style="width: 10%" /></span>
          اجراءات السفر (غير ذلك) :
        </h2>
        <div class="contat-section-title">
          <ul class="info-list">
            <li>يجب أن تكون صلاحية جواز السفر لا تقل عن 6 أشهر.</li>
            <li>
              يتم التوجه اللي السفارة لاستخراج التاشيره او اونلاين حسب الامكانية
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Banking Information Section -->
    <section>
      <div class=" container">
        <!-- Decorative Element -->
        <img src="/trip-pdfs/amazon/assets/image/icon/plan_image-2.svg" alt="" class="airplane-icon_8"
          style="filter: invert(55%) sepia(89%) saturate(618%) hue-rotate(352deg) brightness(107%) contrast(101%);" />

        <div class="text-start mt-5 ms-4">
          <h1 class="title">ملاحظات هامة</h1>
        </div>

        <h2 class="section-title mt-2">المعاملات البنكية</h2>
        <ul class="info-list_1">
          <li>يُنصح بإحضار مبلغ كا ف من المال للتعامل النقدي في جورجيا.</li>
          <li>يمكن استخدام الدولار الأمريكي أو اللاري الجورجي.</li>
          <li>يُؤخذ في الاعتبار وجود عمولة على استخدام البطاقات البنكية.</li>
        </ul>
      </div>
    </section>
  </main>
  <script>
      window.onload = function() {
          window.print();
      };
  </script>
</body>

</html>
