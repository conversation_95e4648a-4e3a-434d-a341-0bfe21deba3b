<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Pusher Test - Real-Time Notifications</title>
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .config-table th, .config-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .config-table th {
            background-color: #f2f2f2;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .notification-demo {
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8f9ff;
        }
        .event-counter {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Financial Pusher Test - Real-Time Notifications</h1>
        <p>This page tests the real-time financial notification system using Pusher WebSockets.</p>

        <!-- Connection Status -->
        <div id="connection-status" class="status connecting">
            🔄 Connecting to Pusher...
        </div>

        <!-- Configuration Display -->
        <h2>📋 Configuration</h2>
        <table class="config-table">
            <tr>
                <th>Setting</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>PUSHER_APP_KEY</td>
                <td>{{ env('PUSHER_APP_KEY', 'NOT SET') }}</td>
            </tr>
            <tr>
                <td>PUSHER_APP_CLUSTER</td>
                <td>{{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}</td>
            </tr>
            <tr>
                <td>BROADCAST_DRIVER</td>
                <td>{{ env('BROADCAST_DRIVER', 'NOT SET') }}</td>
            </tr>
            <tr>
                <td>User ID</td>
                <td>{{ auth()->id() ?? 'NOT LOGGED IN' }}</td>
            </tr>
            <tr>
                <td>Channel</td>
                <td>financial-notifications-{{ auth()->id() ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Event</td>
                <td>financial-notification-received</td>
            </tr>
        </table>

        <!-- Event Counter -->
        <div class="event-counter">
            📊 Events Received: <span id="event-count">0</span>
        </div>

        <!-- Test Button -->
        <div style="text-align: center;">
            <button id="test-btn" onclick="sendTestNotification()" disabled>
                🧪 Send Test Notification
            </button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
            <button onclick="playTestSound()">🔊 Test Sound</button>
        </div>

        <!-- Live Event Log -->
        <h2>📝 Live Event Log</h2>
        <div id="event-log" class="log">
            <div>🔄 Initializing Pusher connection...</div>
        </div>

        <!-- Notification Demo Area -->
        <div class="notification-demo">
            <h3>🎯 Last Received Notification</h3>
            <div id="last-notification">
                <em>No notifications received yet...</em>
            </div>
        </div>
    </div>

    <script>
        let eventCount = 0;
        let pusher = null;
        let channel = null;

        function log(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.className = `status ${status}`;
            statusDiv.innerHTML = message;
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '<div>🗑️ Log cleared</div>';
            eventCount = 0;
            document.getElementById('event-count').textContent = eventCount;
        }

        function playTestSound() {
            try {
                // Create a simple beep sound using Web Audio API
                if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    
                    // Resume audio context if suspended (required for autoplay policy)
                    if (audioContext.state === 'suspended') {
                        audioContext.resume().then(() => {
                            createTestBeep(audioContext);
                        }).catch(error => {
                            log('❌ Could not resume audio context: ' + error.message);
                        });
                    } else {
                        createTestBeep(audioContext);
                    }
                } else {
                    log('❌ Web Audio API not supported');
                }
            } catch (error) {
                log('❌ Error playing test sound: ' + error.message);
            }
        }
        
        function createTestBeep(audioContext) {
            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
                gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.5);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
                
                log('🔊 Test sound played successfully');
            } catch (error) {
                log('❌ Error creating test beep: ' + error.message);
            }
        }

        function sendTestNotification() {
            fetch('/api/test-financial-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    user_id: {{ auth()->id() ?? 'null' }}
                })
            })
            .then(response => response.json())
            .then(data => {
                log('🚀 Test notification sent via API: ' + JSON.stringify(data));
            })
            .catch(error => {
                log('❌ Error sending test notification: ' + error.message);
            });
        }

        // Initialize Pusher
        document.addEventListener('DOMContentLoaded', function() {
            log('🔄 Starting Pusher initialization...');
            
            try {
                // Enable pusher logging
                Pusher.logToConsole = true;
                
                // Initialize Pusher
                pusher = new Pusher('{{ env("PUSHER_APP_KEY") }}', {
                    cluster: '{{ env("PUSHER_APP_CLUSTER") }}',
                    encrypted: true
                });

                // Connection event handlers
                pusher.connection.bind('connected', function() {
                    log('✅ Pusher connected successfully');
                    updateConnectionStatus('connected', '✅ Connected to Pusher');
                    document.getElementById('test-btn').disabled = false;
                });

                pusher.connection.bind('connecting', function() {
                    log('🔄 Connecting to Pusher...');
                    updateConnectionStatus('connecting', '🔄 Connecting to Pusher...');
                });

                pusher.connection.bind('disconnected', function() {
                    log('❌ Pusher disconnected');
                    updateConnectionStatus('disconnected', '❌ Disconnected from Pusher');
                    document.getElementById('test-btn').disabled = true;
                });

                pusher.connection.bind('error', function(err) {
                    log('❌ Pusher connection error: ' + JSON.stringify(err));
                    updateConnectionStatus('disconnected', '❌ Connection Error');
                });

                // Subscribe to financial notifications channel
                @auth
                const channelName = 'financial-notifications-{{ auth()->id() }}';
                log('📡 Subscribing to channel: ' + channelName);
                
                channel = pusher.subscribe(channelName);
                
                channel.bind('pusher:subscription_succeeded', function() {
                    log('✅ Successfully subscribed to financial notifications channel');
                });

                channel.bind('pusher:subscription_error', function(error) {
                    log('❌ Failed to subscribe to channel: ' + JSON.stringify(error));
                });

                // Listen for financial notification events
                channel.bind('financial-notification-received', function(data) {
                    eventCount++;
                    document.getElementById('event-count').textContent = eventCount;
                    
                    log('🟢 FINANCIAL NOTIFICATION RECEIVED: ' + JSON.stringify(data, null, 2));
                    
                    // Update last notification display
                    document.getElementById('last-notification').innerHTML = `
                        <strong>📢 ${data.title || 'No Title'}</strong><br>
                        <em>${data.message || 'No Message'}</em><br>
                        <small>🕒 ${new Date().toLocaleString()}</small><br>
                        <small>👤 User ID: ${data.user_id || 'N/A'}</small>
                    `;
                    
                    // Play notification sound
                    playTestSound();
                    
                    // Show browser notification if supported
                    if ('Notification' in window) {
                        if (Notification.permission === 'granted') {
                            new Notification(data.title || 'Financial Notification', {
                                body: data.message || '',
                                icon: '/favicon.ico'
                            });
                        } else if (Notification.permission !== 'denied') {
                            Notification.requestPermission().then(permission => {
                                if (permission === 'granted') {
                                    new Notification(data.title || 'Financial Notification', {
                                        body: data.message || '',
                                        icon: '/favicon.ico'
                                    });
                                }
                            });
                        }
                    }
                });
                @else
                log('❌ User not authenticated - cannot subscribe to notifications');
                updateConnectionStatus('disconnected', '❌ Not Authenticated');
                @endauth

                log('✅ Pusher initialization completed');
                
            } catch (error) {
                log('❌ Error initializing Pusher: ' + error.message);
                updateConnectionStatus('disconnected', '❌ Initialization Error');
            }
        });

        // Request notification permission on page load
        if ('Notification' in window && Notification.permission === 'default') {
            log('🔔 Requesting notification permission...');
            Notification.requestPermission().then(permission => {
                log('🔔 Notification permission: ' + permission);
            });
        } else if ('Notification' in window) {
            log('🔔 Notification permission: ' + Notification.permission);
        } else {
            log('❌ Browser notifications not supported');
        }
    </script>
</body>
</html> 