<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Instruction Notifications - Real-time (No Polling)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .section {
            margin-bottom: 30px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .notifications {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
        }
        .notification-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .notification-item.high-priority {
            border-left-color: #dc3545;
        }
        .notification-item.urgent-priority {
            border-left-color: #fd7e14;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .timestamp {
            font-size: 0.8em;
            color: #666;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🔔 Real-time Instruction Notifications Test - No Polling!</h1>
    <p>This demonstrates real-time instruction notifications using only Pusher events, without any polling that slows down the website.</p>
    
    <div class="status" id="connectionStatus">
        Connecting to Pusher...
    </div>

    <div class="container">
        <div class="section">
            <h3>🔧 Configuration Info</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace;">
                <strong>Current Pusher Configuration:</strong><br>
                App Key: {{ env('PUSHER_APP_KEY', 'NOT SET') }}<br>
                App ID: {{ env('PUSHER_APP_ID', 'NOT SET') }}<br>
                Cluster: {{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}<br>
                Broadcast Driver: {{ env('BROADCAST_DRIVER', 'NOT SET') }}<br>
                Current User: {{ auth()->user()->name ?? 'Not logged in' }}<br>
                User Role: {{ auth()->user()->is_admin ? 'admin' : (auth()->user()->roles[0] ?? 'none') }}
            </div>
        </div>

        <div class="section">
            <h3>📤 Send Test Instruction</h3>
            <form id="instructionForm">
                @csrf
                <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" id="title" name="title" placeholder="Test instruction title" required>
                </div>
                <div class="form-group">
                    <label for="content">Content:</label>
                    <textarea id="content" name="content" placeholder="Test instruction content..." required></textarea>
                </div>
                <div class="form-group">
                    <label for="priority">Priority:</label>
                    <select id="priority" name="priority">
                        <option value="low">Low</option>
                        <option value="normal" selected>Normal</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="target_roles">Target Roles:</label>
                    <select id="target_roles" name="target_roles[]" multiple>
                        <option value="admin">Admin</option>
                        <option value="sales">Sales</option>
                        <option value="accountant">Accountant</option>
                        <option value="cash_employee">Cash Employee</option>
                        <option value="flight_employee">Flight Employee</option>
                    </select>
                    <small>Hold Ctrl/Cmd to select multiple roles</small>
                </div>
                <button type="submit" class="button">Send Test Instruction</button>
            </form>
            <p><small>This will create an instruction and broadcast it via Pusher</small></p>
        </div>

        <div class="section">
            <h3>📥 Received Instruction Notifications</h3>
            <div class="notifications" id="notificationsContainer">
                <p><em>Waiting for instruction notifications...</em></p>
            </div>
            <p><small>Notifications appear here instantly via Pusher (no polling!)</small></p>
        </div>
    </div>

    <div class="section">
        <h3>🧪 Test Functions</h3>
        <button class="button" onclick="testPusherConnection()">Test Pusher Connection</button>
        <button class="button" onclick="simulateInstruction()">Simulate Incoming Instruction</button>
        <button class="button success" onclick="clearNotifications()">Clear Notifications</button>
    </div>

    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <script>
        // Enable pusher logging - don't include this in production
        Pusher.logToConsole = true;

        // Initialize Pusher with your credentials
        var pusher = new Pusher('{{ env("PUSHER_APP_KEY", "309d0f1beaad790cf00e") }}', {
            cluster: '{{ env("PUSHER_APP_CLUSTER", "eu") }}',
        });

        const statusEl = document.getElementById('connectionStatus');
        const notificationsEl = document.getElementById('notificationsContainer');

        // Connection event handlers
        pusher.connection.bind('connected', function() {
            console.log('✅ Pusher connected successfully');
            statusEl.textContent = '✅ Connected to Pusher (Socket: ' + pusher.connection.socket_id + ')';
            statusEl.className = 'status connected';
        });

        pusher.connection.bind('error', function(err) {
            console.log('❌ Pusher connection error:', err);
            statusEl.textContent = '❌ Pusher connection error: ' + (err.error?.data?.code || err.type || 'Unknown error');
            statusEl.className = 'status error';
        });

        pusher.connection.bind('connecting', function() {
            console.log('🔄 Connecting to Pusher...');
            statusEl.textContent = '🔄 Connecting to Pusher...';
            statusEl.className = 'status connecting';
        });

        // Subscribe to the instructions channel
        var instructionsChannel = pusher.subscribe('instructions');
        
        instructionsChannel.bind('pusher:subscription_succeeded', function() {
            console.log('✅ Successfully subscribed to instructions channel');
            addNotification('System', '✅ Successfully subscribed to instructions channel', 'normal');
        });

        instructionsChannel.bind('pusher:subscription_error', function(error) {
            console.log('❌ Failed to subscribe to instructions channel:', error);
            addNotification('System', '❌ Failed to subscribe to instructions channel: ' + error, 'high');
        });

        // Listen for new instruction events
        instructionsChannel.bind('new-instruction', function(data) {
            console.log('🟢 Received instruction notification via Pusher:', data);
            
            const currentUserRole = getCurrentUserRole();
            console.log('Current user role:', currentUserRole);
            console.log('Instruction target roles:', data.target_roles);
            
            if (data.target_roles && data.target_roles.includes(currentUserRole)) {
                addNotification(
                    data.created_by_name || 'System',
                    `📋 ${data.title || 'New Instruction'}: ${data.content}`,
                    data.priority || 'normal'
                );
                
                // Play notification sound for high/urgent priority
                if (data.priority === 'high' || data.priority === 'urgent') {
                    playNotificationSound();
                }
            } else {
                console.log('📋 Instruction not relevant to current user role');
            }
        });

        // Helper function to get current user role
        function getCurrentUserRole() {
            const userIsAdmin = {{ auth()->user()->is_admin ? 'true' : 'false' }};
            if (userIsAdmin) {
                return 'admin';
            }
            
            const userRoles = @json(auth()->user()->roles ?? []);
            return userRoles.length > 0 ? userRoles[0] : null;
        }

        // Helper function to add notifications to the display
        function addNotification(sender, message, priority = 'normal') {
            const timestamp = new Date().toLocaleTimeString();
            const notificationDiv = document.createElement('div');
            notificationDiv.className = `notification-item ${priority}-priority`;
            notificationDiv.innerHTML = `
                <strong>${sender}</strong> <span class="timestamp">[${timestamp}]</span><br>
                ${message}
            `;
            
            // Remove the "waiting" message if it exists
            const waitingMsg = notificationsEl.querySelector('p em');
            if (waitingMsg) {
                waitingMsg.remove();
            }
            
            // Add to top of notifications
            notificationsEl.insertBefore(notificationDiv, notificationsEl.firstChild);
            
            // Scroll to top to show new notification
            notificationsEl.scrollTop = 0;
        }

        // Form submission handler
        document.getElementById('instructionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                title: formData.get('title'),
                content: formData.get('content'),
                priority: formData.get('priority'),
                target_roles: formData.getAll('target_roles[]'),
                is_active: true,
                created_by: {{ auth()->id() ?? 'null' }}
            };
            
            try {
                const response = await fetch('/test-send-instruction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    addNotification('Laravel Backend', `✅ Instruction sent successfully: "${data.title}"`, 'normal');
                    addNotification('System', `Broadcasting to Pusher with ID: ${result.data.instruction_id}`, 'normal');
                    this.reset();
                } else {
                    addNotification('Error', `❌ Failed to send instruction: ${result.message}`, 'high');
                }
            } catch (error) {
                console.error('Error sending instruction:', error);
                addNotification('Error', `❌ Network error: ${error.message}`, 'high');
            }
        });

        // Test functions
        function testPusherConnection() {
            addNotification('Test', `Connection state: ${pusher.connection.state}`, 'normal');
            addNotification('Test', `Socket ID: ${pusher.connection.socket_id || 'Not connected'}`, 'normal');
            addNotification('Test', `App Key: {{ env('PUSHER_APP_KEY', 'NOT SET') }}`, 'normal');
            addNotification('Test', `Cluster: {{ env('PUSHER_APP_CLUSTER', 'NOT SET') }}`, 'normal');
            addNotification('Test', `Current User Role: ${getCurrentUserRole()}`, 'normal');
        }

        function simulateInstruction() {
            const testData = {
                instruction_id: Math.floor(Math.random() * 1000),
                title: 'Simulated Test Instruction',
                content: 'This is a simulated test instruction - ' + new Date().toLocaleTimeString(),
                priority: 'normal',
                target_roles: [getCurrentUserRole()],
                created_by_name: 'Test System',
                created_at: new Date().toISOString()
            };
            
            addNotification('Simulation', `Simulated instruction: ${testData.content}`, testData.priority);
        }

        function clearNotifications() {
            notificationsEl.innerHTML = '<p><em>Notifications cleared...</em></p>';
        }

        function playNotificationSound() {
            // Create a simple beep sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        }

        // Show instructions
        console.log('🚀 Real-time Instruction Notifications Test Loaded!');
        console.log('📝 To test real instruction notifications:');
        console.log('1. Open browser console to see Pusher events');
        console.log('2. Use testPusherConnection() to check connection');
        console.log('3. Use simulateInstruction() to test notification display');
        console.log('4. Fill the form and send a test instruction');
    </script>
</body>
</html> 