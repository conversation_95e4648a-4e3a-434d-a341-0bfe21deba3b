# Financial Notifications Testing Guide

## Overview
This guide will help you test each financial notification scenario in your Laravel CRM system. The system now uses **real-time Pusher broadcasting** for immediate notifications without page refresh.

## Prerequisites

### 1. Check Your User Roles
First, identify which user account you'll use for testing. Run this command to see available users and their roles:

```bash
php artisan users:show-roles
```

### 2. User Roles for Testing
You'll need users with these roles:
- `ACCOUNTANT_TRANSPORTATION` - Receives transportation payment notifications
- `ACCOUNTANT_HOTEL` - Receives hotel payment notifications  
- `ACCOUNTANT_BR` - You<PERSON><PERSON>'s role, receives approval requests
- `TRANSPORTATION_MANAGER` - Receives tour activation notifications
- `ROLE_TRANSPORTATION_COMPANY` - Transportation company users

## Real-Time Testing (NEW!)

### Quick Real-Time Test
```bash
# Test real-time notifications with Pusher broadcasting
php artisan notifications:test-financial --type=sound --user-id=YOUR_USER_ID
```

**IMPORTANT**: Before running this command:
1. **Open your browser** and login as the test user
2. **Open browser console** (F12) to see Pusher events
3. **Keep the browser tab active** and visible
4. Look for these console messages:
   - ✅ Pusher connected for financial notifications
   - ✅ Successfully subscribed to financial notifications channel

### Expected Real-Time Results ⚡
When you run the test command, you should see **immediately** (no refresh needed):
- ✅ Notification count increases
- 🔊 Sound plays automatically  
- 🔔 Browser notification appears
- ✨ Notification icon flashes/animates
- 📝 Console shows: "🟢 Received financial notification via Pusher"

## Testing Commands

### Test All Scenarios
```bash
# Test all notification scenarios with detailed output
php artisan notifications:test-financial --type=scenarios

# Test real-time notifications for specific user
php artisan notifications:test-financial --type=sound --user-id=YOUR_USER_ID
```

## Manual Testing Scenarios

### Scenario 1: Payment Approval Notifications ✅

**What it tests**: When Youssef approves a payment, accounting users get notified **immediately**.

**Steps to test**:

1. **Login as accounting user in browser** (with `ACCOUNTANT_TRANSPORTATION` role)
2. **Keep browser tab open** and console visible (F12)
3. **In terminal, trigger payment approval**:
   ```bash
   php artisan tinker
   ```
   ```php
   $payment = TransportationCompanyPayment::first();
   $payment->status = \App\Enums\PaymentStatus::APPROVED;
   $payment->save(); // This triggers real-time notification
   ```

4. **Expected Result** (should happen immediately):
   - Notification count increases without refresh
   - Sound plays automatically
   - Title: "✅ تم الموافقة على الدفع"
   - Console shows Pusher event received

### Scenario 2: Payment Pending Approval Notifications ⏳

**What it tests**: When payments are sent for approval, Youssef gets notified **immediately**.

**Steps to test**:

1. **Login as Youssef** (user with `ACCOUNTANT_BR` role)
2. **Trigger payment for approval**:
   ```bash
   php artisan tinker
   ```
   ```php
   $payment = TransportationCompanyPayment::first();
   $payment->send_to_approve_at = now();
   $payment->save(); // Triggers real-time notification
   ```

3. **Expected Result**:
   - Immediate notification without refresh
   - Title: "⏳ دفعة تحتاج موافقة"

### Scenario 3: Tour Activation Notifications 🚌

**What it tests**: When tours become active, drivers and managers get notified **immediately**.

**Steps to test**:

1. **Login as transportation manager**
2. **Activate tour**:
   ```php
   $booking = TransportationCompanyBooking::first();
   $booking->is_active = true;
   $booking->save(); // Triggers real-time notification
   ```

3. **Expected Result**:
   - Immediate notification
   - Title: "🚌 جولة نشطة جديدة"

### Scenario 4: Daily Financial Alerts 📊

**What it tests**: Daily reports of unpaid amounts sent **immediately**.

**Steps to test**:

1. **Login as accounting user**
2. **Run daily alerts**:
   ```bash
   php artisan notifications:send-daily-financial-alerts
   ```

3. **Expected Result**:
   - Immediate notifications for unpaid amounts
   - No page refresh needed

### Scenario 5: Sales Manager Rejection Notifications ❌

**What it tests**: When sales managers reject requests, users get notified **immediately**.

**Steps to test**:

1. **Login as the user who created the payment**
2. **Trigger rejection**:
   ```bash
   php artisan tinker
   ```
   ```php
   $payment = TransportationCompanyPayment::first();
   app(\App\Services\NotificationService::class)->sendSalesManagerRejectionNotification(
       $payment,
       'payment',
       'Test rejection - insufficient documentation'
   );
   ```

3. **Expected Result**:
   - Immediate rejection notification
   - Title: "❌ رفض دفعة النقل"

## Real-Time System Architecture 🏗️

### How It Works
1. **Laravel Event** → Creates notification in database
2. **Pusher Broadcasting** → Sends real-time event to browser
3. **JavaScript Listener** → Receives event and updates UI immediately
4. **Sound & Animation** → Plays automatically without user action

### Pusher Channels Used
- `private-financial-notifications.{user_id}` - Individual user notifications
- Event: `financial-notification-received`

## Troubleshooting Real-Time Issues 🐛

### Notifications Not Appearing Immediately?

1. **Check Pusher Connection**:
   ```javascript
   // In browser console
   console.log('Pusher state:', pusher.connection.state);
   ```

2. **Verify Environment Variables**:
   ```bash
   # Check your .env file
   PUSHER_APP_KEY=your_key_here
   PUSHER_APP_CLUSTER=eu
   BROADCAST_DRIVER=pusher
   ```

3. **Check Browser Console**:
   - Look for Pusher connection errors
   - Verify subscription to financial notifications channel
   - Check for JavaScript errors

### Sound Not Playing?

1. **Browser Permissions**:
   - Allow notifications for your domain
   - Allow audio autoplay
   - Try clicking on the page first (some browsers require user interaction)

2. **Test Sound Manually**:
   ```javascript
   // In browser console
   window.playNotificationSound();
   ```

3. **Check Audio Context**:
   ```javascript
   // In browser console
   console.log('AudioContext supported:', typeof AudioContext !== 'undefined');
   ```

### Pusher Connection Issues?

1. **Check Pusher Status**:
   ```bash
   php artisan tinker
   ```
   ```php
   // Test broadcasting
   broadcast(new \App\Events\FinancialNotificationSent(
       \App\Models\Notification::first()
   ));
   ```

2. **Verify Pusher Credentials**:
   - Login to Pusher dashboard
   - Check app key and cluster match your .env
   - Verify app is active

3. **Check Laravel Logs**:
   ```bash
   tail -f storage/logs/laravel.log
   ```

## Step-by-Step Real-Time Testing Workflow

### Complete Real-Time Test:

1. **Setup** (5 minutes):
   ```bash
   # Find your user ID
   php artisan users:show-roles
   
   # Note your user ID (e.g., 90)
   ```

2. **Browser Setup**:
   - Open browser and login as test user
   - Open console (F12)
   - Navigate to any page with notifications

3. **Run Real-Time Test**:
   ```bash
   php artisan notifications:test-financial --type=sound --user-id=90
   ```

4. **Verify Results**:
   - ✅ Console shows Pusher connection
   - ✅ Notification appears immediately
   - ✅ Sound plays automatically
   - ✅ Count updates without refresh

## Success Criteria ✅

**Real-Time Requirements (NEW)**:
- ⚡ **Immediate appearance** (0-1 second delay)
- 🔊 **Automatic sound** (no user action needed)
- 🔄 **No refresh required** (count updates instantly)
- 📱 **Browser notifications** (if permissions granted)
- 🎯 **Pusher events** visible in console

**Traditional Requirements**:
- ✅ Correct users receive notifications based on roles
- ✅ Notification content is accurate and in Arabic
- ✅ Action URLs work correctly

## Performance Benefits 🚀

**Before (Session Flash)**:
- ❌ Required page refresh
- ❌ 2-3 second delays
- ❌ No immediate feedback

**After (Real-Time Pusher)**:
- ✅ Instant notifications (< 1 second)
- ✅ No refresh needed
- ✅ Immediate sound and visual feedback
- ✅ Better user experience

## Support

If you encounter issues:
1. **Check browser console** for Pusher connection errors
2. **Run the real-time test**: `php artisan notifications:test-financial --type=sound --user-id=YOUR_ID`
3. **Verify Pusher config** in .env file
4. **Check Laravel logs**: `tail -f storage/logs/laravel.log`
5. **Test Pusher connection**: Visit `/test-pusher` route if available

---

**Note**: The system now uses **real-time Pusher broadcasting** for immediate notifications. Make sure your browser is open and logged in when testing!

## Expected Notification Recipients

| Notification Type | Recipients | Role Required |
|------------------|------------|---------------|
| Payment Approved | Transportation Accountants | `ACCOUNTANT_TRANSPORTATION` |
| Payment Approved | Hotel Accountants | `ACCOUNTANT_HOTEL` |
| Pending Approval | Youssef | `ACCOUNTANT_BR` |
| Tour Activated | Transportation Managers | `TRANSPORTATION_MANAGER` |
| Tour Activated | Transportation Companies | `ROLE_TRANSPORTATION_COMPANY` |
| Daily Alerts | All Accountants | Various `ACCOUNTANT_*` roles |
| Sales Rejection | Request Creator | Any user who created the request |

## Success Criteria

✅ **Notification appears immediately** (no refresh needed)
✅ **Sound plays when notification arrives**
✅ **Correct users receive notifications based on roles**
✅ **Notification count updates in real-time**
✅ **Notification content is accurate and in Arabic**
✅ **Action URLs work correctly**
✅ **Browser notifications appear (if permissions granted)**

## Support

If you encounter issues:
1. Check the logs: `tail -f storage/logs/laravel.log`
2. Run the test command: `php artisan notifications:test-financial --type=scenarios`
3. Verify your user roles match the expected recipients
4. Test sound separately: `php artisan notifications:test-financial --type=sound --user-id=YOUR_ID`

---

**Note**: Replace `YOUR_USER_ID` with your actual user ID throughout this guide. 